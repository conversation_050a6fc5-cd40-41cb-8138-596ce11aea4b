"""
核心模拟引擎模块

包含微观模拟模型的核心组件：
- 人口初始化和管理
- 生命表和死亡率建模
- 疾病进展模拟
- 筛查过程模拟
"""

from .enums import (
    DiseaseState, PathwayType, CancerStage, Gender,
    ScreeningResult, ScreeningTool
)
from .individual import Individual, HealthEvent
from .population import Population, PopulationStatistics
from .simulation import SimulationState, SimulationEvent, EventQueue
from .mortality_engine import MortalityEngine, MortalityApplicationResult

__all__ = [
    # 枚举类
    "DiseaseState",
    "PathwayType",
    "CancerStage",
    "Gender",
    "ScreeningResult",
    "ScreeningTool",
    # 个体相关
    "Individual",
    "HealthEvent",
    # 人群相关
    "Population",
    "PopulationStatistics",
    # 模拟相关
    "SimulationState",
    "SimulationEvent",
    "EventQueue",
    # 死亡率相关
    "MortalityEngine",
    "MortalityApplicationResult",
]
