#!/usr/bin/env python3
"""
人群生成器文件导入功能演示

演示PopulationGenerator类从文件生成人群的功能
"""

import sys
import pandas as pd
import numpy as np
from pathlib import Path

# 添加src目录到Python路径
project_root = Path(__file__).parent
src_path = project_root / "src"
sys.path.insert(0, str(src_path))

def demo_file_generation():
    """演示从文件生成人群"""
    print("=== 人群生成器文件导入功能演示 ===\n")
    
    try:
        from modules.population.population_generator import PopulationGenerator
        from core import PathwayType
        
        generator = PopulationGenerator()
        
        # 测试个体格式文件
        print("1. 从个体格式文件生成人群:")
        individual_file = Path('data/sample_population.csv')
        if individual_file.exists():
            try:
                population = generator.generate_population_from_file(
                    individual_file,
                    show_progress=False
                )
                
                print(f"   ✅ 成功生成人群")
                print(f"   📊 人群规模: {len(population.individuals):,}")
                
                # 分析年龄分布
                ages = [ind.get_age(2025) for ind in population.individuals]
                print(f"   📈 年龄范围: {min(ages)}-{max(ages)}岁")
                print(f"   📈 平均年龄: {np.mean(ages):.1f}岁")
                
                # 分析性别分布
                male_count = sum(1 for ind in population.individuals if ind.gender.value == 'male')
                female_count = len(population.individuals) - male_count
                male_ratio = male_count / len(population.individuals) * 100
                print(f"   👥 性别分布: 男性 {male_count} ({male_ratio:.1f}%), 女性 {female_count} ({100-male_ratio:.1f}%)")
                
                # 显示生成摘要
                summary = generator.get_last_generation_summary()
                if summary:
                    print(f"   ⏱️ 生成时间: {summary.generation_time:.3f}秒")
                
            except Exception as e:
                print(f"   ❌ 生成失败: {e}")
        else:
            print(f"   ⚠️ 文件不存在: {individual_file}")
        
        # 测试聚合格式文件
        print("\n2. 从聚合格式文件生成人群:")
        aggregated_file = Path('data/sample_population_aggregated.csv')
        if aggregated_file.exists():
            try:
                # 带疾病通路分布的生成
                pathway_distribution = {
                    'normal': 0.80,
                    'adenoma': 0.15,
                    'serrated': 0.05
                }
                
                population = generator.generate_population_from_file(
                    aggregated_file,
                    pathway_distribution=pathway_distribution,
                    show_progress=False
                )
                
                print(f"   ✅ 成功生成人群")
                print(f"   📊 人群规模: {len(population.individuals):,}")
                
                # 分析疾病通路分布
                pathway_counts = {}
                for ind in population.individuals:
                    pathway = ind.pathway.value
                    pathway_counts[pathway] = pathway_counts.get(pathway, 0) + 1
                
                print(f"   🧬 疾病通路分布:")
                total = len(population.individuals)
                for pathway, count in pathway_counts.items():
                    ratio = count / total * 100
                    print(f"      {pathway}: {count:,} ({ratio:.1f}%)")
                
            except Exception as e:
                print(f"   ❌ 生成失败: {e}")
        else:
            print(f"   ⚠️ 文件不存在: {aggregated_file}")
        
        # 测试中文格式文件
        print("\n3. 从中文格式文件生成人群:")
        chinese_file = Path('data/sample_population_chinese.csv')
        if chinese_file.exists():
            try:
                population = generator.generate_population_from_file(
                    chinese_file,
                    show_progress=False
                )
                
                print(f"   ✅ 成功生成人群")
                print(f"   📊 人群规模: {len(population.individuals):,}")
                print(f"   🇨🇳 支持中文列名")
                
            except Exception as e:
                print(f"   ❌ 生成失败: {e}")
        else:
            print(f"   ⚠️ 文件不存在: {chinese_file}")
            
    except ImportError as e:
        print(f"无法导入模块: {e}")
        print("请确保已正确设置Python路径")

def demo_distribution_preservation():
    """演示分布保持功能"""
    print("\n=== 分布保持功能演示 ===\n")
    
    try:
        from modules.population.population_generator import PopulationGenerator
        
        generator = PopulationGenerator()
        
        # 创建测试数据
        test_data = []
        # 45岁：100人（50男50女）
        for _ in range(50):
            test_data.append({'age': 45, 'gender': 'M'})
        for _ in range(50):
            test_data.append({'age': 45, 'gender': 'F'})
        
        # 55岁：200人（100男100女）
        for _ in range(100):
            test_data.append({'age': 55, 'gender': 'M'})
        for _ in range(100):
            test_data.append({'age': 55, 'gender': 'F'})
        
        # 保存为临时文件
        df = pd.DataFrame(test_data)
        temp_file = Path('temp_test_population.csv')
        df.to_csv(temp_file, index=False)
        
        print("原始数据分布:")
        print(f"  45岁: {len(df[df['age'] == 45])}人")
        print(f"  55岁: {len(df[df['age'] == 55])}人")
        print(f"  总计: {len(df)}人")
        
        try:
            # 从文件生成人群
            population = generator.generate_population_from_file(
                temp_file,
                show_progress=False
            )
            
            print(f"\n生成的模拟人群分布:")
            ages = [ind.get_age(2025) for ind in population.individuals]
            age_45_count = sum(1 for age in ages if age == 45)
            age_55_count = sum(1 for age in ages if age == 55)
            
            print(f"  45岁: {age_45_count}人")
            print(f"  55岁: {age_55_count}人")
            print(f"  总计: {len(population.individuals)}人")
            
            # 验证分布是否保持
            original_45_ratio = len(df[df['age'] == 45]) / len(df)
            generated_45_ratio = age_45_count / len(population.individuals)
            
            print(f"\n分布保持验证:")
            print(f"  原始45岁比例: {original_45_ratio:.1%}")
            print(f"  生成45岁比例: {generated_45_ratio:.1%}")
            print(f"  分布保持: {'✅' if abs(original_45_ratio - generated_45_ratio) < 0.01 else '❌'}")
            
            print(f"\n✨ 关键特性:")
            print(f"  • 即使输入个体数据，也会累加成分布后重新生成")
            print(f"  • 保持原始的年龄-性别分布特征")
            print(f"  • 生成新的随机个体用于模拟")
            print(f"  • 支持疾病通路的随机分配")
            
        finally:
            # 清理临时文件
            if temp_file.exists():
                temp_file.unlink()
                
    except ImportError as e:
        print(f"无法导入模块: {e}")
    except Exception as e:
        print(f"演示过程中出错: {e}")

def demo_error_handling():
    """演示错误处理"""
    print("\n=== 错误处理演示 ===\n")
    
    try:
        from modules.population.population_generator import PopulationGenerator
        from utils import ValidationError
        
        generator = PopulationGenerator()
        
        # 测试文件不存在
        print("1. 文件不存在错误:")
        try:
            generator.generate_population_from_file("nonexistent_file.csv", show_progress=False)
        except FileNotFoundError as e:
            print(f"   ✅ 正确捕获: {type(e).__name__}")
        
        # 测试无效年龄数据
        print("\n2. 无效年龄数据错误:")
        invalid_data = pd.DataFrame({
            'age': ['abc', 'def'],
            'gender': ['M', 'F']
        })
        temp_file = Path('temp_invalid.csv')
        invalid_data.to_csv(temp_file, index=False)
        
        try:
            generator.generate_population_from_file(temp_file, show_progress=False)
        except ValidationError as e:
            print(f"   ✅ 正确捕获: {e}")
        finally:
            if temp_file.exists():
                temp_file.unlink()
        
        # 测试年龄范围错误
        print("\n3. 年龄范围错误:")
        range_data = pd.DataFrame({
            'age': [150, 200],
            'gender': ['M', 'F']
        })
        temp_file = Path('temp_range.csv')
        range_data.to_csv(temp_file, index=False)
        
        try:
            generator.generate_population_from_file(temp_file, show_progress=False)
        except ValidationError as e:
            print(f"   ✅ 正确捕获: {e}")
        finally:
            if temp_file.exists():
                temp_file.unlink()
                
    except ImportError as e:
        print(f"无法导入模块: {e}")

def main():
    """主演示函数"""
    print("结直肠癌筛查模拟器 - 人群生成器文件导入功能演示")
    print("=" * 70)
    
    demo_file_generation()
    demo_distribution_preservation()
    demo_error_handling()
    
    print("\n" + "=" * 70)
    print("人群生成器文件导入功能演示完成！")
    print("\n🎯 核心功能:")
    print("• 支持个体和聚合两种文件格式")
    print("• 自动累加个体数据为分布统计")
    print("• 基于分布重新生成模拟个体")
    print("• 保持原始的年龄-性别分布特征")
    print("• 支持疾病通路的随机分配")
    print("• 完善的数据验证和错误处理")
    print("\n💡 设计优势:")
    print("• 避免直接使用真实个体数据")
    print("• 生成新的随机个体用于模拟")
    print("• 支持基于分布扩展人群规模")
    print("• 确保模拟的科学性和隐私性")

if __name__ == "__main__":
    main()
