"""
人群配置管理单元测试

测试PopulationConfig类的配置加载、验证和管理功能。
"""

import pytest
import tempfile
import json
import yaml
from pathlib import Path

from src.modules.population import PopulationConfig
from src.modules.population.population_config import (
    PopulationConfigData, AgeDistributionConfig, 
    GenderDistributionConfig, PathwayDistributionConfig
)
from src.utils import ValidationError, ParameterValidationError


class TestAgeDistributionConfig:
    """测试年龄分布配置"""
    
    def test_default_config(self):
        """测试默认配置"""
        config = AgeDistributionConfig()

        assert config.type == "uniform"
        assert config.mean is None
        assert config.std is None
        assert config.min_age == 18
        assert config.max_age == 100
    
    def test_custom_config(self):
        """测试自定义配置"""
        config = AgeDistributionConfig(
            type="normal",
            mean=60.0,
            std=10.0,
            min_age=50,
            max_age=75
        )
        
        assert config.type == "normal"
        assert config.mean == 60.0
        assert config.std == 10.0
        assert config.min_age == 50
        assert config.max_age == 75
    
    def test_to_dict(self):
        """测试转换为字典"""
        config = AgeDistributionConfig(
            type="normal",
            mean=55.0,
            std=12.0
        )
        
        result = config.to_dict()
        expected = {
            "type": "normal",
            "mean": 55.0,
            "std": 12.0,
            "min_age": 18,
            "max_age": 100
        }
        
        assert result == expected


class TestGenderDistributionConfig:
    """测试性别分布配置"""
    
    def test_default_config(self):
        """测试默认配置"""
        config = GenderDistributionConfig()
        
        assert config.male_ratio == 0.5
        assert config.female_ratio == 0.5
    
    def test_custom_config(self):
        """测试自定义配置"""
        config = GenderDistributionConfig(
            male_ratio=0.52,
            female_ratio=0.48
        )
        
        assert config.male_ratio == 0.52
        assert config.female_ratio == 0.48
    
    def test_to_dict(self):
        """测试转换为字典"""
        config = GenderDistributionConfig(male_ratio=0.6, female_ratio=0.4)
        result = config.to_dict()
        expected = {"male_ratio": 0.6, "female_ratio": 0.4}
        
        assert result == expected


class TestPathwayDistributionConfig:
    """测试疾病通路分布配置"""
    
    def test_default_config(self):
        """测试默认配置"""
        config = PathwayDistributionConfig()
        
        assert config.adenoma_carcinoma_ratio == 0.85
        assert config.serrated_adenoma_ratio == 0.15
    
    def test_custom_config(self):
        """测试自定义配置"""
        config = PathwayDistributionConfig(
            adenoma_carcinoma_ratio=0.8,
            serrated_adenoma_ratio=0.2
        )
        
        assert config.adenoma_carcinoma_ratio == 0.8
        assert config.serrated_adenoma_ratio == 0.2


class TestPopulationConfigData:
    """测试人群配置数据"""
    
    def test_basic_config_data(self):
        """测试基本配置数据"""
        config = PopulationConfigData(
            name="测试人群",
            description="测试用人群配置",
            size=1000
        )
        
        assert config.name == "测试人群"
        assert config.description == "测试用人群配置"
        assert config.size == 1000
        assert config.random_seed is None
        assert config.birth_year_base == 2025
        
        # 验证默认子配置已创建
        assert isinstance(config.age_distribution, AgeDistributionConfig)
        assert isinstance(config.gender_distribution, GenderDistributionConfig)
        assert isinstance(config.pathway_distribution, PathwayDistributionConfig)
    
    def test_to_generation_params(self):
        """测试转换为生成器参数"""
        config = PopulationConfigData(
            name="测试",
            description="测试",
            size=500,
            birth_year_base=2020
        )
        
        params = config.to_generation_params()
        
        assert params["size"] == 500
        assert params["birth_year_base"] == 2020
        assert "age_distribution" in params
        assert "gender_distribution" in params
        assert "pathway_distribution" in params


class TestPopulationConfig:
    """测试PopulationConfig类"""
    
    def test_config_manager_creation(self):
        """测试配置管理器创建"""
        config_manager = PopulationConfig()
        
        assert config_manager.config_dir == Path("data/population_configs")
        assert len(config_manager._loaded_configs) == 0
    
    def test_config_manager_with_custom_dir(self):
        """测试使用自定义目录创建配置管理器"""
        custom_dir = "/custom/config/dir"
        config_manager = PopulationConfig(custom_dir)
        
        assert config_manager.config_dir == Path(custom_dir)
    
    def test_load_yaml_config(self):
        """测试加载YAML配置文件"""
        config_data = {
            "population_config": {
                "name": "测试YAML配置",
                "description": "YAML格式的测试配置",
                "size": 2000,
                "random_seed": 123,
                "birth_year_base": 2020,
                "age_distribution": {
                    "type": "normal",
                    "mean": 65.0,
                    "std": 8.0,
                    "min_age": 50,
                    "max_age": 80
                },
                "gender_distribution": {
                    "male_ratio": 0.55,
                    "female_ratio": 0.45
                },
                "pathway_distribution": {
                    "adenoma_carcinoma_ratio": 0.9,
                    "serrated_adenoma_ratio": 0.1
                }
            }
        }
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            yaml.dump(config_data, f)
            temp_path = f.name
        
        try:
            config_manager = PopulationConfig()
            loaded_config = config_manager.load_config(temp_path)
            
            assert loaded_config.name == "测试YAML配置"
            assert loaded_config.size == 2000
            assert loaded_config.random_seed == 123
            assert loaded_config.birth_year_base == 2020
            
            # 验证年龄分布
            assert loaded_config.age_distribution.type == "normal"
            assert loaded_config.age_distribution.mean == 65.0
            assert loaded_config.age_distribution.std == 8.0
            
            # 验证性别分布
            assert loaded_config.gender_distribution.male_ratio == 0.55
            assert loaded_config.gender_distribution.female_ratio == 0.45
            
            # 验证疾病通路分布
            assert loaded_config.pathway_distribution.adenoma_carcinoma_ratio == 0.9
            assert loaded_config.pathway_distribution.serrated_adenoma_ratio == 0.1
            
        finally:
            Path(temp_path).unlink()
    
    def test_load_json_config(self):
        """测试加载JSON配置文件"""
        config_data = {
            "population_config": {
                "name": "测试JSON配置",
                "description": "JSON格式的测试配置",
                "size": 1500,
                "age_distribution": {
                    "type": "uniform",
                    "min_age": 40,
                    "max_age": 70
                },
                "gender_distribution": {
                    "male_ratio": 0.5,
                    "female_ratio": 0.5
                }
            }
        }
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            json.dump(config_data, f)
            temp_path = f.name
        
        try:
            config_manager = PopulationConfig()
            loaded_config = config_manager.load_config(temp_path)
            
            assert loaded_config.name == "测试JSON配置"
            assert loaded_config.size == 1500
            assert loaded_config.age_distribution.type == "uniform"
            assert loaded_config.age_distribution.min_age == 40
            assert loaded_config.age_distribution.max_age == 70
            
        finally:
            Path(temp_path).unlink()
    
    def test_load_nonexistent_config(self):
        """测试加载不存在的配置文件"""
        config_manager = PopulationConfig()
        
        with pytest.raises(ValidationError, match="配置文件不存在"):
            config_manager.load_config("nonexistent_config.yaml")
    
    def test_load_invalid_format_config(self):
        """测试加载无效格式的配置文件"""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False) as f:
            f.write("invalid config content")
            temp_path = f.name
        
        try:
            config_manager = PopulationConfig()
            
            with pytest.raises(ValidationError, match="不支持的配置文件格式"):
                config_manager.load_config(temp_path)
                
        finally:
            Path(temp_path).unlink()
    
    def test_load_malformed_yaml(self):
        """测试加载格式错误的YAML文件"""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            f.write("invalid: yaml: content: [")
            temp_path = f.name
        
        try:
            config_manager = PopulationConfig()
            
            with pytest.raises(ValidationError, match="配置文件解析失败"):
                config_manager.load_config(temp_path)
                
        finally:
            Path(temp_path).unlink()
    
    def test_config_validation_invalid_size(self):
        """测试配置验证 - 无效人群规模"""
        config_data = {
            "population_config": {
                "name": "无效配置",
                "description": "测试无效人群规模",
                "size": 0  # 无效规模
            }
        }
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            yaml.dump(config_data, f)
            temp_path = f.name
        
        try:
            config_manager = PopulationConfig()
            
            with pytest.raises(ParameterValidationError, match="人群规模必须大于0"):
                config_manager.load_config(temp_path)
                
        finally:
            Path(temp_path).unlink()
    
    def test_config_validation_invalid_age_distribution(self):
        """测试配置验证 - 无效年龄分布"""
        config_data = {
            "population_config": {
                "name": "无效年龄配置",
                "description": "测试无效年龄分布",
                "size": 1000,
                "age_distribution": {
                    "type": "normal",
                    "mean": 50,
                    "std": -5,  # 无效标准差
                    "min_age": 30,
                    "max_age": 70
                }
            }
        }
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            yaml.dump(config_data, f)
            temp_path = f.name
        
        try:
            config_manager = PopulationConfig()
            
            with pytest.raises(ValidationError, match="标准差必须大于0"):
                config_manager.load_config(temp_path)
                
        finally:
            Path(temp_path).unlink()
    
    def test_config_validation_invalid_gender_distribution(self):
        """测试配置验证 - 无效性别分布"""
        config_data = {
            "population_config": {
                "name": "无效性别配置",
                "description": "测试无效性别分布",
                "size": 1000,
                "age_distribution": {
                    "type": "uniform",
                    "min_age": 30,
                    "max_age": 70
                },
                "gender_distribution": {
                    "male_ratio": 0.7,
                    "female_ratio": 0.4  # 总和不等于1
                }
            }
        }
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            yaml.dump(config_data, f)
            temp_path = f.name
        
        try:
            config_manager = PopulationConfig()
            
            with pytest.raises(ValidationError, match="男女比例之和必须等于1.0"):
                config_manager.load_config(temp_path)
                
        finally:
            Path(temp_path).unlink()
    
    def test_save_config_yaml(self):
        """测试保存配置为YAML格式"""
        config = PopulationConfigData(
            name="保存测试",
            description="测试保存功能",
            size=800,
            random_seed=456,
            age_distribution=AgeDistributionConfig(
                type="uniform",
                min_age=30,
                max_age=70
            )
        )
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            temp_path = f.name
        
        try:
            config_manager = PopulationConfig()
            config_manager.save_config(config, temp_path, format="yaml")
            
            # 验证文件已创建
            assert Path(temp_path).exists()
            
            # 重新加载并验证
            loaded_config = config_manager.load_config(temp_path)
            assert loaded_config.name == "保存测试"
            assert loaded_config.size == 800
            assert loaded_config.random_seed == 456
            
        finally:
            Path(temp_path).unlink()
    
    def test_save_config_json(self):
        """测试保存配置为JSON格式"""
        config = PopulationConfigData(
            name="JSON保存测试",
            description="测试JSON保存功能",
            size=600,
            age_distribution=AgeDistributionConfig(
                type="uniform",
                min_age=25,
                max_age=65
            )
        )
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            temp_path = f.name
        
        try:
            config_manager = PopulationConfig()
            config_manager.save_config(config, temp_path, format="json")
            
            # 验证文件已创建
            assert Path(temp_path).exists()
            
            # 重新加载并验证
            loaded_config = config_manager.load_config(temp_path)
            assert loaded_config.name == "JSON保存测试"
            assert loaded_config.size == 600
            
        finally:
            Path(temp_path).unlink()
    
    def test_save_config_invalid_format(self):
        """测试保存配置 - 无效格式"""
        config = PopulationConfigData(
            name="测试",
            description="测试",
            size=100,
            age_distribution=AgeDistributionConfig(
                type="uniform",
                min_age=20,
                max_age=80
            )
        )
        
        config_manager = PopulationConfig()
        
        with pytest.raises(ValidationError, match="不支持的保存格式"):
            config_manager.save_config(config, "test.xml", format="xml")
    
    def test_get_loaded_configs(self):
        """测试获取已加载的配置"""
        config_manager = PopulationConfig()
        
        # 初始状态应该为空
        loaded_configs = config_manager.get_loaded_configs()
        assert len(loaded_configs) == 0
        
        # 加载一个配置
        config_data = {
            "population_config": {
                "name": "测试配置1",
                "description": "第一个测试配置",
                "size": 1000,
                "age_distribution": {
                    "type": "uniform",
                    "min_age": 30,
                    "max_age": 70
                }
            }
        }
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            yaml.dump(config_data, f)
            temp_path = f.name
        
        try:
            config_manager.load_config(temp_path)
            
            loaded_configs = config_manager.get_loaded_configs()
            assert len(loaded_configs) == 1
            assert "测试配置1" in loaded_configs
            
        finally:
            Path(temp_path).unlink()
    
    def test_create_default_config(self):
        """测试创建默认配置"""
        config_manager = PopulationConfig()
        
        default_config = config_manager.create_default_config()
        
        assert default_config.name == "默认中国成人筛查人群"
        assert default_config.size == 100000
        assert default_config.random_seed == 42
        assert default_config.age_distribution.type == "normal"
        assert default_config.age_distribution.mean == 62.5
        assert default_config.age_distribution.min_age == 50
        assert default_config.age_distribution.max_age == 75
        assert default_config.gender_distribution.male_ratio == 0.52
        assert default_config.pathway_distribution.adenoma_carcinoma_ratio == 0.85
    
    def test_create_custom_default_config(self):
        """测试创建自定义默认配置"""
        config_manager = PopulationConfig()
        
        custom_config = config_manager.create_default_config(
            name="自定义配置",
            description="自定义描述"
        )
        
        assert custom_config.name == "自定义配置"
        assert custom_config.description == "自定义描述"
        # 其他参数应该保持默认值
        assert custom_config.size == 100000
