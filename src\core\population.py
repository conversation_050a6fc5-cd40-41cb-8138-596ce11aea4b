"""
人群容器类实现

定义Population类，高效管理Individual对象集合，
提供统计计算、筛选和批量操作功能。
"""

from typing import Dict, List, Iterator, Optional, Callable, Any, Set
from collections import defaultdict, Counter
import numpy as np

from .individual import Individual
from .enums import DiseaseState, Gender, PathwayType, CancerStage


class PopulationStatistics:
    """人群统计信息类"""
    
    def __init__(self, population: "Population"):
        self.population = population
    
    def get_age_distribution(self, bins: Optional[List[float]] = None) -> Dict[str, int]:
        """
        获取年龄分布
        
        Args:
            bins: 年龄分组边界，默认为[0, 20, 30, 40, 50, 60, 70, 80, 90, 100]
            
        Returns:
            年龄分组统计字典
        """
        if bins is None:
            bins = [0, 20, 30, 40, 50, 60, 70, 80, 90, 100]
        
        ages = [ind.get_current_age() for ind in self.population.individuals.values()]
        hist, _ = np.histogram(ages, bins=bins)
        
        result = {}
        for i in range(len(bins) - 1):
            key = f"{bins[i]}-{bins[i+1]}"
            result[key] = int(hist[i])
        
        return result
    
    def get_gender_distribution(self) -> Dict[str, int]:
        """获取性别分布"""
        counter = Counter(ind.gender.value for ind in self.population.individuals.values())
        return dict(counter)
    
    def get_disease_state_distribution(self) -> Dict[str, int]:
        """获取疾病状态分布"""
        counter = Counter(
            ind.current_disease_state.value 
            for ind in self.population.individuals.values()
        )
        return dict(counter)
    
    def get_pathway_distribution(self) -> Dict[str, int]:
        """获取疾病通路分布"""
        counter = Counter(
            ind.pathway_type.value if ind.pathway_type else "none"
            for ind in self.population.individuals.values()
        )
        return dict(counter)
    
    def get_cancer_stage_distribution(self) -> Dict[str, int]:
        """获取癌症分期分布（仅癌症患者）"""
        cancer_individuals = [
            ind for ind in self.population.individuals.values()
            if ind.current_disease_state.is_cancer() and ind.cancer_stage
        ]
        
        counter = Counter(ind.cancer_stage.value for ind in cancer_individuals)
        return dict(counter)
    
    def get_survival_rate(self) -> float:
        """获取存活率"""
        total = len(self.population.individuals)
        if total == 0:
            return 0.0
        
        alive = sum(1 for ind in self.population.individuals.values() if ind.is_alive())
        return alive / total
    
    def get_mean_age(self) -> float:
        """获取平均年龄"""
        ages = [ind.get_current_age() for ind in self.population.individuals.values()]
        return float(np.mean(ages)) if ages else 0.0
    
    def get_age_statistics(self) -> Dict[str, float]:
        """获取详细年龄统计"""
        ages = [ind.get_current_age() for ind in self.population.individuals.values()]
        if not ages:
            return {}

        ages_array = np.array(ages)
        return {
            "mean": float(np.mean(ages_array)),
            "median": float(np.median(ages_array)),
            "std": float(np.std(ages_array)),
            "min": float(np.min(ages_array)),
            "max": float(np.max(ages_array)),
            "q25": float(np.percentile(ages_array, 25)),
            "q75": float(np.percentile(ages_array, 75)),
            "iqr": float(np.percentile(ages_array, 75) - np.percentile(ages_array, 25))
        }

    def get_age_group_distribution(self, group_size: int = 5) -> Dict[str, int]:
        """
        获取年龄组分布

        Args:
            group_size: 年龄组大小（年）

        Returns:
            年龄组分布字典
        """
        ages = [ind.get_current_age() for ind in self.population.individuals.values()]
        if not ages:
            return {}

        min_age = int(min(ages))
        max_age = int(max(ages))

        # 创建年龄组
        age_groups = {}
        for age in ages:
            group_start = (int(age) // group_size) * group_size
            group_end = group_start + group_size - 1
            group_key = f"{group_start}-{group_end}"
            age_groups[group_key] = age_groups.get(group_key, 0) + 1

        return age_groups

    def get_summary(self) -> Dict[str, Any]:
        """获取人群统计摘要"""
        return {
            "total_individuals": len(self.population.individuals),
            "mean_age": self.get_mean_age(),
            "survival_rate": self.get_survival_rate(),
            "age_statistics": self.get_age_statistics(),
            "age_distribution": self.get_age_distribution(),
            "age_group_distribution": self.get_age_group_distribution(),
            "gender_distribution": self.get_gender_distribution(),
            "disease_state_distribution": self.get_disease_state_distribution(),
            "pathway_distribution": self.get_pathway_distribution(),
            "cancer_stage_distribution": self.get_cancer_stage_distribution(),
            "survival_statistics": self.get_survival_statistics(),
        }

    def get_survival_statistics(self) -> Dict[str, Any]:
        """获取生存统计信息"""
        alive_individuals = [ind for ind in self.population.individuals.values() if ind.is_alive()]
        dead_individuals = [ind for ind in self.population.individuals.values() if not ind.is_alive()]

        total = len(self.population.individuals)
        alive_count = len(alive_individuals)
        dead_count = len(dead_individuals)

        # 基本生存统计
        survival_stats = {
            "total_individuals": total,
            "alive_count": alive_count,
            "dead_count": dead_count,
            "survival_rate": alive_count / total if total > 0 else 0.0,
            "mortality_rate": dead_count / total if total > 0 else 0.0
        }

        # 死亡原因分析
        if dead_individuals:
            death_causes = Counter(ind.current_disease_state.value for ind in dead_individuals)
            survival_stats["death_causes"] = dict(death_causes)

            # 计算死亡年龄统计
            death_ages = []
            for ind in dead_individuals:
                # 从健康历史中找到死亡事件
                death_events = [event for event in ind.health_history
                              if event.to_state.is_death()]
                if death_events:
                    death_ages.append(death_events[-1].age_at_event)

            if death_ages:
                survival_stats["death_age_statistics"] = {
                    "mean": float(np.mean(death_ages)),
                    "median": float(np.median(death_ages)),
                    "std": float(np.std(death_ages)),
                    "min": float(np.min(death_ages)),
                    "max": float(np.max(death_ages))
                }

        # 按年龄组的生存率
        age_group_survival = {}
        age_groups = [(18, 30), (31, 40), (41, 50), (51, 60), (61, 70), (71, 80), (81, 90), (91, 120)]

        for min_age, max_age in age_groups:
            group_name = f"{min_age}-{max_age}"
            group_individuals = [
                ind for ind in self.population.individuals.values()
                if min_age <= ind.get_current_age() <= max_age
            ]

            if group_individuals:
                group_alive = sum(1 for ind in group_individuals if ind.is_alive())
                group_total = len(group_individuals)
                age_group_survival[group_name] = {
                    "total": group_total,
                    "alive": group_alive,
                    "survival_rate": group_alive / group_total
                }

        survival_stats["age_group_survival"] = age_group_survival

        # 按性别的生存率
        gender_survival = {}
        for gender in [Gender.MALE, Gender.FEMALE]:
            gender_individuals = [
                ind for ind in self.population.individuals.values()
                if ind.gender == gender
            ]

            if gender_individuals:
                gender_alive = sum(1 for ind in gender_individuals if ind.is_alive())
                gender_total = len(gender_individuals)
                gender_survival[gender.value] = {
                    "total": gender_total,
                    "alive": gender_alive,
                    "survival_rate": gender_alive / gender_total
                }

        survival_stats["gender_survival"] = gender_survival

        return survival_stats

    def calculate_survival_curve(
        self,
        time_points: Optional[List[float]] = None,
        reference_year: int = 2025
    ) -> Dict[str, Any]:
        """
        计算Kaplan-Meier生存曲线

        Args:
            time_points: 时间点列表（年），默认为0-100年
            reference_year: 参考年份

        Returns:
            生存曲线数据
        """
        if time_points is None:
            time_points = list(range(0, 101, 5))  # 0, 5, 10, ..., 100年

        # 收集所有个体的生存数据
        survival_data = []

        for individual in self.population.individuals.values():
            birth_year = individual.birth_year
            current_age = individual.get_current_age(reference_year)

            if individual.is_alive():
                # 存活个体：观察时间为当前年龄，事件=0（未死亡）
                survival_data.append({
                    "time": current_age,
                    "event": 0,
                    "gender": individual.gender.value,
                    "individual_id": individual.individual_id
                })
            else:
                # 死亡个体：从健康历史中获取死亡时间
                death_events = [event for event in individual.health_history
                              if event.to_state.is_death()]
                if death_events:
                    death_age = death_events[-1].age_at_event
                    survival_data.append({
                        "time": death_age,
                        "event": 1,
                        "gender": individual.gender.value,
                        "individual_id": individual.individual_id
                    })

        if not survival_data:
            return {"time_points": time_points, "survival_probabilities": [], "at_risk": []}

        # 计算Kaplan-Meier估计
        survival_curve = self._calculate_kaplan_meier(survival_data, time_points)

        return survival_curve

    def _calculate_kaplan_meier(
        self,
        survival_data: List[Dict[str, Any]],
        time_points: List[float]
    ) -> Dict[str, Any]:
        """
        计算Kaplan-Meier生存估计

        Args:
            survival_data: 生存数据列表
            time_points: 时间点列表

        Returns:
            生存曲线数据
        """
        # 按时间排序
        sorted_data = sorted(survival_data, key=lambda x: x["time"])

        survival_probabilities = []
        at_risk_counts = []
        cumulative_survival = 1.0

        for time_point in time_points:
            # 计算在该时间点的风险人数
            at_risk = sum(1 for data in sorted_data if data["time"] >= time_point)

            # 计算在该时间点之前的死亡事件
            deaths_before = [
                data for data in sorted_data
                if data["time"] <= time_point and data["event"] == 1
            ]

            # 按死亡时间分组计算生存概率
            death_times = {}
            for death in deaths_before:
                death_time = death["time"]
                if death_time not in death_times:
                    death_times[death_time] = {"deaths": 0, "at_risk": 0}
                death_times[death_time]["deaths"] += 1

            # 计算每个死亡时间点的风险人数
            for death_time in death_times:
                death_times[death_time]["at_risk"] = sum(
                    1 for data in sorted_data if data["time"] >= death_time
                )

            # 计算累积生存概率
            for death_time in sorted(death_times.keys()):
                if death_time <= time_point:
                    deaths = death_times[death_time]["deaths"]
                    at_risk_at_death = death_times[death_time]["at_risk"]

                    if at_risk_at_death > 0:
                        survival_prob_at_time = (at_risk_at_death - deaths) / at_risk_at_death
                        cumulative_survival *= survival_prob_at_time

            survival_probabilities.append(cumulative_survival)
            at_risk_counts.append(at_risk)

        return {
            "time_points": time_points,
            "survival_probabilities": survival_probabilities,
            "at_risk": at_risk_counts,
            "total_individuals": len(survival_data),
            "total_events": sum(1 for data in survival_data if data["event"] == 1)
        }

    def get_cohort_survival_analysis(
        self,
        cohort_definition: str = "birth_year",
        reference_year: int = 2025
    ) -> Dict[str, Any]:
        """
        队列生存分析

        Args:
            cohort_definition: 队列定义方式 ("birth_year", "age_group", "gender")
            reference_year: 参考年份

        Returns:
            队列生存分析结果
        """
        cohorts = {}

        # 根据队列定义方式分组
        for individual in self.population.individuals.values():
            if cohort_definition == "birth_year":
                cohort_key = str(individual.birth_year)
            elif cohort_definition == "age_group":
                age = individual.get_current_age(reference_year)
                if age < 30:
                    cohort_key = "18-29"
                elif age < 40:
                    cohort_key = "30-39"
                elif age < 50:
                    cohort_key = "40-49"
                elif age < 60:
                    cohort_key = "50-59"
                elif age < 70:
                    cohort_key = "60-69"
                elif age < 80:
                    cohort_key = "70-79"
                else:
                    cohort_key = "80+"
            elif cohort_definition == "gender":
                cohort_key = individual.gender.value
            else:
                raise ValueError(f"不支持的队列定义方式: {cohort_definition}")

            if cohort_key not in cohorts:
                cohorts[cohort_key] = []
            cohorts[cohort_key].append(individual)

        # 为每个队列计算生存统计
        cohort_analysis = {}

        for cohort_name, individuals in cohorts.items():
            alive_count = sum(1 for ind in individuals if ind.is_alive())
            total_count = len(individuals)

            # 计算平均年龄
            ages = [ind.get_current_age(reference_year) for ind in individuals]
            mean_age = np.mean(ages) if ages else 0.0

            # 计算死亡年龄统计
            death_ages = []
            for ind in individuals:
                if not ind.is_alive():
                    death_events = [event for event in ind.health_history
                                  if event.to_state.is_death()]
                    if death_events:
                        death_ages.append(death_events[-1].age_at_event)

            cohort_analysis[cohort_name] = {
                "total_individuals": total_count,
                "alive_count": alive_count,
                "dead_count": total_count - alive_count,
                "survival_rate": alive_count / total_count if total_count > 0 else 0.0,
                "mean_age": mean_age,
                "death_age_statistics": {
                    "mean": float(np.mean(death_ages)) if death_ages else None,
                    "median": float(np.median(death_ages)) if death_ages else None,
                    "count": len(death_ages)
                }
            }

        return {
            "cohort_definition": cohort_definition,
            "reference_year": reference_year,
            "cohorts": cohort_analysis,
            "total_cohorts": len(cohorts)
        }


class Population:
    """
    人群容器类
    
    高效管理Individual对象集合，提供添加、删除、查找、
    统计计算、筛选和批量操作功能。
    """
    
    def __init__(self, initial_individuals: Optional[List[Individual]] = None):
        """
        初始化人群
        
        Args:
            initial_individuals: 初始个体列表
        """
        self.individuals: Dict[str, Individual] = {}
        self._gender_index: Dict[Gender, Set[str]] = defaultdict(set)
        self._disease_state_index: Dict[DiseaseState, Set[str]] = defaultdict(set)
        self._age_index: Dict[int, Set[str]] = defaultdict(set)
        
        # 添加初始个体
        if initial_individuals:
            for individual in initial_individuals:
                self.add_individual(individual)
        
        # 创建统计对象
        self.statistics = PopulationStatistics(self)
    
    def add_individual(self, individual: Individual) -> bool:
        """
        添加个体到人群
        
        Args:
            individual: 要添加的个体
            
        Returns:
            是否成功添加
        """
        if individual.individual_id in self.individuals:
            return False
        
        self.individuals[individual.individual_id] = individual
        self._update_indices(individual, add=True)
        return True
    
    def remove_individual(self, individual_id: str) -> bool:
        """
        从人群中移除个体
        
        Args:
            individual_id: 个体ID
            
        Returns:
            是否成功移除
        """
        if individual_id not in self.individuals:
            return False
        
        individual = self.individuals[individual_id]
        self._update_indices(individual, add=False)
        del self.individuals[individual_id]
        return True
    
    def get_individual(self, individual_id: str) -> Optional[Individual]:
        """
        获取指定个体
        
        Args:
            individual_id: 个体ID
            
        Returns:
            个体对象或None
        """
        return self.individuals.get(individual_id)
    
    def _update_indices(self, individual: Individual, add: bool = True) -> None:
        """
        更新索引
        
        Args:
            individual: 个体对象
            add: True为添加，False为移除
        """
        individual_id = individual.individual_id
        
        if add:
            self._gender_index[individual.gender].add(individual_id)
            self._disease_state_index[individual.current_disease_state].add(individual_id)
            age_group = int(individual.get_current_age() // 10) * 10  # 按10岁分组
            self._age_index[age_group].add(individual_id)
        else:
            self._gender_index[individual.gender].discard(individual_id)
            self._disease_state_index[individual.current_disease_state].discard(individual_id)
            age_group = int(individual.get_current_age() // 10) * 10
            self._age_index[age_group].discard(individual_id)
    
    def filter_by_gender(self, gender: Gender) -> List[Individual]:
        """按性别筛选个体"""
        individual_ids = self._gender_index[gender]
        return [self.individuals[id_] for id_ in individual_ids]
    
    def filter_by_disease_state(self, disease_state: DiseaseState) -> List[Individual]:
        """按疾病状态筛选个体"""
        individual_ids = self._disease_state_index[disease_state]
        return [self.individuals[id_] for id_ in individual_ids]
    
    def filter_by_age_range(self, min_age: float, max_age: float) -> List[Individual]:
        """
        按年龄范围筛选个体
        
        Args:
            min_age: 最小年龄
            max_age: 最大年龄
            
        Returns:
            符合条件的个体列表
        """
        return [
            individual for individual in self.individuals.values()
            if min_age <= individual.get_current_age() <= max_age
        ]
    
    def filter_by_criteria(self, criteria: Callable[[Individual], bool]) -> List[Individual]:
        """
        按自定义条件筛选个体
        
        Args:
            criteria: 筛选条件函数
            
        Returns:
            符合条件的个体列表
        """
        return [individual for individual in self.individuals.values() if criteria(individual)]
    
    def get_alive_individuals(self) -> List[Individual]:
        """获取所有存活的个体"""
        return [individual for individual in self.individuals.values() if individual.is_alive()]
    
    def get_cancer_patients(self) -> List[Individual]:
        """获取所有癌症患者"""
        return [
            individual for individual in self.individuals.values()
            if individual.current_disease_state.is_cancer()
        ]
    
    def batch_update_ages(self, years_passed: float) -> None:
        """
        批量更新所有个体的年龄（通过更新参考年份）
        
        Args:
            years_passed: 经过的年数
        """
        # 注意：这里不直接修改birth_year，而是在计算年龄时使用新的参考年份
        # 实际实现中可能需要在Individual类中添加参考年份属性
        pass
    
    def batch_transition_states(
        self,
        criteria: Callable[[Individual], bool],
        new_state: DiseaseState,
        cancer_stage: Optional[CancerStage] = None
    ) -> int:
        """
        批量状态转换

        Args:
            criteria: 筛选条件
            new_state: 新状态
            cancer_stage: 癌症分期

        Returns:
            成功转换的个体数量
        """
        count = 0
        # 先收集符合条件的个体，避免在迭代过程中修改索引
        individuals_to_update = [
            individual for individual in self.individuals.values()
            if criteria(individual)
        ]

        for individual in individuals_to_update:
            # 先从旧索引中移除
            self._update_indices(individual, add=False)

            # 执行状态转换
            if individual.transition_to_state(new_state, cancer_stage):
                count += 1

            # 添加到新索引
            self._update_indices(individual, add=True)

        return count
    
    def get_size(self) -> int:
        """获取人群大小"""
        return len(self.individuals)
    
    def is_empty(self) -> bool:
        """检查人群是否为空"""
        return len(self.individuals) == 0
    
    def clear(self) -> None:
        """清空人群"""
        self.individuals.clear()
        self._gender_index.clear()
        self._disease_state_index.clear()
        self._age_index.clear()
    
    def __iter__(self) -> Iterator[Individual]:
        """迭代器支持"""
        return iter(self.individuals.values())
    
    def __len__(self) -> int:
        """长度支持"""
        return len(self.individuals)
    
    def __contains__(self, individual_id: str) -> bool:
        """包含检查支持"""
        return individual_id in self.individuals
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "size": self.get_size(),
            "statistics": self.statistics.get_summary(),
            "individuals": [individual.to_dict() for individual in self.individuals.values()]
        }
    
    def __repr__(self) -> str:
        """字符串表示"""
        return f"Population(size={self.get_size()}, alive={len(self.get_alive_individuals())})"
