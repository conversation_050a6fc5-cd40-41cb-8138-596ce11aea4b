"""
桌面应用集成测试

测试桌面应用各组件之间的集成功能。
"""

import pytest
import sys
from unittest.mock import patch, MagicMock

# 确保PyQt6可用
pytest.importorskip("PyQt6")

from PyQt6.QtWidgets import QApplication

from src.interfaces.desktop.main import Application, MainWindow
from src.interfaces.desktop.windows.config_wizard import ConfigWizard, PopulationConfigWidget
from src.interfaces.desktop.windows.results_viewer import ResultsWindow
from src.interfaces.desktop.widgets.simulation_control import SimulationControlWidget
from src.interfaces.desktop.utils.validators import (
    create_age_validator, create_population_size_validator, create_gender_ratio_validator
)


@pytest.fixture
def qapp():
    """创建QApplication实例"""
    if not QApplication.instance():
        app = QApplication(sys.argv)
    else:
        app = QApplication.instance()
    yield app


class TestDesktopIntegration:
    """测试桌面应用集成功能"""
    
    def test_application_components_creation(self, qapp):
        """测试应用程序组件创建"""
        # 创建主应用程序
        app = Application(sys.argv)
        
        # 创建主窗口
        main_window = app.create_main_window()
        assert isinstance(main_window, MainWindow)
        
        # 测试组件创建
        config_widget = PopulationConfigWidget()
        assert config_widget is not None
        
        simulation_control = SimulationControlWidget()
        assert simulation_control is not None
        
        results_window = ResultsWindow()
        assert results_window is not None
        
        config_wizard = ConfigWizard()
        assert config_wizard is not None
    
    def test_config_widget_validation(self, qapp):
        """测试配置组件验证功能"""
        config_widget = PopulationConfigWidget()
        
        # 测试默认配置
        config = config_widget.get_config()
        assert 'population_size' in config
        assert 'age_distribution' in config
        assert 'gender_distribution' in config
        
        # 测试配置值范围
        assert 1 <= config['population_size'] <= 1_000_000
        assert 0 <= config['gender_distribution']['male_ratio'] <= 1
        assert 0 <= config['gender_distribution']['female_ratio'] <= 1
    
    def test_simulation_control_states(self, qapp):
        """测试模拟控制状态管理"""
        control_widget = SimulationControlWidget()
        
        # 测试初始状态
        params = control_widget.get_parameters()
        assert 'duration_years' in params
        assert 'time_step' in params
        assert 'random_seed' in params
        
        # 测试状态转换
        from src.interfaces.desktop.widgets.simulation_control import SimulationStatus
        
        control_widget.set_status(SimulationStatus.RUNNING)
        assert control_widget.current_status == SimulationStatus.RUNNING
        
        control_widget.set_status(SimulationStatus.COMPLETED)
        assert control_widget.current_status == SimulationStatus.COMPLETED
    
    def test_validators_integration(self, qapp):
        """测试验证器集成功能"""
        # 测试年龄验证器
        age_validator = create_age_validator("测试年龄")
        
        # 有效年龄
        results = age_validator.validate(25)
        assert any(result.is_valid for result in results)
        
        # 无效年龄
        results = age_validator.validate(-5)
        assert any(not result.is_valid for result in results)
        
        # 测试人群规模验证器
        size_validator = create_population_size_validator("测试规模")
        
        # 有效规模
        results = size_validator.validate(1000)
        assert any(result.is_valid for result in results)
        
        # 无效规模
        results = size_validator.validate(0)
        assert any(not result.is_valid for result in results)
        
        # 测试性别比例验证器
        ratio_validator = create_gender_ratio_validator("测试比例")
        
        # 有效比例
        results = ratio_validator.validate(0.5)
        assert any(result.is_valid for result in results)
        
        # 无效比例
        results = ratio_validator.validate(1.5)
        assert any(not result.is_valid for result in results)
    
    def test_results_window_functionality(self, qapp):
        """测试结果窗口功能"""
        results_window = ResultsWindow()
        
        # 测试初始状态
        assert results_window.population is None
        assert results_window.statistics is None
        
        # 测试日志功能
        results_window.add_log_message("测试消息")
        log_content = results_window.log_text.toPlainText()
        assert "测试消息" in log_content
        
        # 测试清空功能
        results_window.clear_data()
        assert results_window.population is None
    
    @patch('src.interfaces.desktop.main.QMessageBox')
    def test_error_handling_integration(self, mock_msgbox, qapp):
        """测试错误处理集成"""
        app = Application(sys.argv)
        
        # 测试错误消息显示
        app.show_error_message("测试错误", "这是一个测试错误消息")
        mock_msgbox.critical.assert_called_once()
        
        # 测试信息消息显示
        app.show_info_message("测试信息", "这是一个测试信息消息")
        mock_msgbox.information.assert_called_once()
    
    def test_signal_connections(self, qapp):
        """测试信号连接"""
        config_widget = PopulationConfigWidget()
        simulation_control = SimulationControlWidget()
        
        # 测试信号是否正确定义
        assert hasattr(config_widget, 'config_changed')
        assert hasattr(config_widget, 'preview_requested')
        
        assert hasattr(simulation_control, 'simulation_start_requested')
        assert hasattr(simulation_control, 'simulation_pause_requested')
        assert hasattr(simulation_control, 'simulation_stop_requested')
        
        # 测试信号连接（通过检查信号接收器数量）
        receivers_before = config_widget.config_changed.receivers(config_widget.config_changed)
        
        # 连接一个测试槽
        test_slot = MagicMock()
        config_widget.config_changed.connect(test_slot)
        
        receivers_after = config_widget.config_changed.receivers(config_widget.config_changed)
        assert receivers_after > receivers_before
    
    def test_component_data_flow(self, qapp):
        """测试组件间数据流"""
        config_widget = PopulationConfigWidget()
        simulation_control = SimulationControlWidget()
        
        # 获取配置数据
        config_data = config_widget.get_config()
        
        # 获取模拟参数
        sim_params = simulation_control.get_parameters()
        
        # 验证数据结构
        assert isinstance(config_data, dict)
        assert isinstance(sim_params, dict)
        
        # 验证必要字段存在
        required_config_fields = ['population_size', 'age_distribution', 'gender_distribution']
        for field in required_config_fields:
            assert field in config_data
        
        required_sim_fields = ['duration_years', 'time_step', 'random_seed']
        for field in required_sim_fields:
            assert field in sim_params
    
    def test_ui_responsiveness(self, qapp):
        """测试UI响应性"""
        main_window = MainWindow()
        
        # 测试窗口基本属性
        assert main_window.windowTitle() == "结直肠癌筛查模拟器"
        assert main_window.minimumSize().width() >= 1200
        assert main_window.minimumSize().height() >= 800
        
        # 测试标签页
        assert hasattr(main_window, 'tab_widget')
        assert main_window.tab_widget.count() >= 3
        
        # 测试菜单栏
        menubar = main_window.menuBar()
        assert menubar is not None
        
        # 测试状态栏
        statusbar = main_window.statusBar()
        assert statusbar is not None
    
    def test_cross_platform_compatibility(self, qapp):
        """测试跨平台兼容性"""
        from pathlib import Path
        
        # 测试路径处理
        test_path = Path("test/path/file.txt")
        assert isinstance(test_path, Path)
        
        # 测试应用程序在不同平台的基本功能
        app = Application(sys.argv)
        main_window = app.create_main_window()
        
        # 验证应用程序信息设置
        assert app.applicationName() == "结直肠癌筛查模拟器"
        assert app.applicationVersion() == "1.0.0"
        assert app.organizationName() == "深圳市南山区慢性病防治院"
        
        # 验证窗口可以正常创建和显示
        assert main_window is not None
        assert not main_window.isVisible()  # 窗口创建但未显示

    def test_main_window_component_integration(self, qapp):
        """测试主窗口组件集成"""
        main_window = MainWindow()

        # 验证组件已正确集成
        assert hasattr(main_window, 'config_widget')
        assert hasattr(main_window, 'simulation_control')
        assert hasattr(main_window, 'tab_widget')

        # 验证标签页数量和标题
        assert main_window.tab_widget.count() == 3
        tab_titles = [main_window.tab_widget.tabText(i) for i in range(main_window.tab_widget.count())]
        expected_titles = ["人群配置", "模拟控制", "结果显示"]
        assert tab_titles == expected_titles

        # 验证组件类型
        from src.interfaces.desktop.windows.config_wizard import PopulationConfigWidget
        from src.interfaces.desktop.widgets.simulation_control import SimulationControlWidget

        assert isinstance(main_window.config_widget, PopulationConfigWidget)
        assert isinstance(main_window.simulation_control, SimulationControlWidget)

    def test_icon_loading(self, qapp):
        """测试图标加载"""
        app = Application(sys.argv)

        # 验证图标已设置（即使是空图标也应该有QIcon对象）
        icon = app.windowIcon()
        assert icon is not None

        # 检查图标文件是否存在
        from pathlib import Path
        project_root = Path(__file__).parent.parent.parent
        icon_path = project_root / "resources" / "icons" / "app.png"

        if icon_path.exists():
            assert not icon.isNull()  # 如果图标文件存在，图标不应为空


class TestDesktopWorkflow:
    """测试桌面应用工作流程"""
    
    def test_complete_simulation_workflow(self, qapp):
        """测试完整的模拟工作流程"""
        # 1. 创建应用程序
        app = Application(sys.argv)
        main_window = app.create_main_window()
        
        # 2. 配置人群参数
        config_widget = PopulationConfigWidget()
        config_data = config_widget.get_config()
        
        # 3. 设置模拟参数
        simulation_control = SimulationControlWidget()
        sim_params = simulation_control.get_parameters()
        
        # 4. 验证参数
        age_validator = create_age_validator()
        size_validator = create_population_size_validator()
        
        # 验证年龄参数
        age_results = age_validator.validate(config_data['age_distribution']['mean'])
        assert any(result.is_valid for result in age_results)
        
        # 验证人群规模
        size_results = size_validator.validate(config_data['population_size'])
        assert any(result.is_valid for result in size_results)
        
        # 5. 准备结果显示
        results_window = ResultsWindow()
        
        # 验证整个工作流程的数据一致性
        assert config_data['population_size'] > 0
        assert sim_params['duration_years'] > 0
        assert results_window is not None
    
    def test_error_recovery_workflow(self, qapp):
        """测试错误恢复工作流程"""
        simulation_control = SimulationControlWidget()
        
        # 模拟错误状态
        simulation_control.simulation_error("测试错误")
        
        # 验证错误状态
        from src.interfaces.desktop.widgets.simulation_control import SimulationStatus
        assert simulation_control.current_status == SimulationStatus.ERROR
        
        # 测试重置功能
        simulation_control._on_reset_clicked()
        assert simulation_control.current_status == SimulationStatus.NOT_STARTED
