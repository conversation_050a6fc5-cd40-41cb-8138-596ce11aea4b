"""
模拟控制组件

提供模拟运行控制的用户界面，包括开始、暂停、停止按钮，
进度显示和状态监控功能。
"""

import logging
from enum import Enum
from typing import Optional, Dict, Any
from datetime import datetime

from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
    QGroupBox, QLabel, QPushButton, QProgressBar,
    QTextEdit, QSpinBox, QDoubleSpinBox, QComboBox,
    QFormLayout, QFrame, QSplitter
)
from PyQt6.QtCore import Qt, pyqtSignal, QTimer, QThread, pyqtSlot
from PyQt6.QtGui import QFont, QColor, QPalette

from src.core import SimulationState


class SimulationStatus(Enum):
    """模拟状态枚举"""
    NOT_STARTED = "未开始"
    RUNNING = "运行中"
    PAUSED = "已暂停"
    COMPLETED = "已完成"
    ERROR = "错误"
    STOPPED = "已停止"


class SimulationControlWidget(QWidget):
    """
    模拟控制面板
    
    提供模拟运行的完整控制界面，包括参数设置、状态监控、
    进度跟踪和日志显示功能。
    """
    
    # 信号定义
    simulation_start_requested = pyqtSignal(dict)  # 请求开始模拟
    simulation_pause_requested = pyqtSignal()      # 请求暂停模拟
    simulation_stop_requested = pyqtSignal()       # 请求停止模拟
    simulation_reset_requested = pyqtSignal()      # 请求重置模拟
    parameters_changed = pyqtSignal(dict)          # 参数变更信号
    
    def __init__(self, parent=None):
        """初始化模拟控制面板"""
        super().__init__(parent)
        
        self.simulation_state: Optional[SimulationState] = None
        self.current_status = SimulationStatus.NOT_STARTED
        self.start_time: Optional[datetime] = None
        self.simulation_parameters = {}
        
        self._setup_ui()
        self._connect_signals()
        self._setup_timer()
        self._update_ui_state()
        
        logging.info("模拟控制面板初始化完成")
    
    def _setup_ui(self) -> None:
        """设置用户界面"""
        layout = QVBoxLayout(self)
        
        # 创建分割器
        splitter = QSplitter(Qt.Orientation.Vertical)
        layout.addWidget(splitter)
        
        # 控制面板
        control_panel = self._create_control_panel()
        splitter.addWidget(control_panel)
        
        # 参数设置面板
        params_panel = self._create_parameters_panel()
        splitter.addWidget(params_panel)
        
        # 状态监控面板
        status_panel = self._create_status_panel()
        splitter.addWidget(status_panel)
        
        # 日志显示面板
        log_panel = self._create_log_panel()
        splitter.addWidget(log_panel)
        
        # 设置分割器比例
        splitter.setStretchFactor(0, 1)  # 控制面板
        splitter.setStretchFactor(1, 2)  # 参数面板
        splitter.setStretchFactor(2, 2)  # 状态面板
        splitter.setStretchFactor(3, 3)  # 日志面板
    
    def _create_control_panel(self) -> QGroupBox:
        """创建控制面板"""
        group = QGroupBox("模拟控制")
        layout = QHBoxLayout(group)
        
        # 主要控制按钮
        self.start_button = QPushButton("开始模拟")
        self.start_button.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
            QPushButton:disabled {
                background-color: #cccccc;
                color: #666666;
            }
        """)
        layout.addWidget(self.start_button)
        
        self.pause_button = QPushButton("暂停模拟")
        self.pause_button.setStyleSheet("""
            QPushButton {
                background-color: #FF9800;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #e68900;
            }
            QPushButton:disabled {
                background-color: #cccccc;
                color: #666666;
            }
        """)
        layout.addWidget(self.pause_button)
        
        self.stop_button = QPushButton("停止模拟")
        self.stop_button.setStyleSheet("""
            QPushButton {
                background-color: #f44336;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #da190b;
            }
            QPushButton:disabled {
                background-color: #cccccc;
                color: #666666;
            }
        """)
        layout.addWidget(self.stop_button)
        
        self.reset_button = QPushButton("重置")
        self.reset_button.setStyleSheet("""
            QPushButton {
                background-color: #607D8B;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #546E7A;
            }
        """)
        layout.addWidget(self.reset_button)
        
        layout.addStretch()
        
        return group
    
    def _create_parameters_panel(self) -> QGroupBox:
        """创建参数设置面板"""
        group = QGroupBox("模拟参数")
        layout = QFormLayout(group)
        
        # 模拟持续时间
        self.duration_spin = QSpinBox()
        self.duration_spin.setRange(1, 100)
        self.duration_spin.setValue(50)
        self.duration_spin.setSuffix(" 年")
        layout.addRow("模拟持续时间:", self.duration_spin)
        
        # 时间步长
        self.time_step_spin = QDoubleSpinBox()
        self.time_step_spin.setRange(0.1, 5.0)
        self.time_step_spin.setValue(1.0)
        self.time_step_spin.setSingleStep(0.1)
        self.time_step_spin.setSuffix(" 年")
        layout.addRow("时间步长:", self.time_step_spin)
        
        # 随机种子
        self.seed_spin = QSpinBox()
        self.seed_spin.setRange(1, 999999)
        self.seed_spin.setValue(42)
        layout.addRow("随机种子:", self.seed_spin)
        
        # 输出频率
        self.output_frequency_combo = QComboBox()
        self.output_frequency_combo.addItems(["每年", "每5年", "每10年", "仅最终结果"])
        layout.addRow("输出频率:", self.output_frequency_combo)
        
        return group
    
    def _create_status_panel(self) -> QGroupBox:
        """创建状态监控面板"""
        group = QGroupBox("状态监控")
        layout = QGridLayout(group)
        
        # 当前状态
        layout.addWidget(QLabel("当前状态:"), 0, 0)
        self.status_label = QLabel("未开始")
        self.status_label.setStyleSheet("font-weight: bold; color: #666666;")
        layout.addWidget(self.status_label, 0, 1)
        
        # 进度条
        layout.addWidget(QLabel("总体进度:"), 1, 0)
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)
        layout.addWidget(self.progress_bar, 1, 1)
        
        # 当前年份
        layout.addWidget(QLabel("当前年份:"), 2, 0)
        self.current_year_label = QLabel("--")
        layout.addWidget(self.current_year_label, 2, 1)
        
        # 已用时间
        layout.addWidget(QLabel("已用时间:"), 3, 0)
        self.elapsed_time_label = QLabel("--")
        layout.addWidget(self.elapsed_time_label, 3, 1)
        
        # 预计剩余时间
        layout.addWidget(QLabel("预计剩余:"), 4, 0)
        self.remaining_time_label = QLabel("--")
        layout.addWidget(self.remaining_time_label, 4, 1)
        
        # 处理的事件数
        layout.addWidget(QLabel("处理事件:"), 5, 0)
        self.events_processed_label = QLabel("0")
        layout.addWidget(self.events_processed_label, 5, 1)
        
        return group
    
    def _create_log_panel(self) -> QGroupBox:
        """创建日志显示面板"""
        group = QGroupBox("模拟日志")
        layout = QVBoxLayout(group)
        
        # 日志文本区域
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        self.log_text.setMaximumHeight(200)
        
        # 设置等宽字体
        font = QFont("Consolas", 9)
        font.setStyleHint(QFont.StyleHint.Monospace)
        self.log_text.setFont(font)
        
        layout.addWidget(self.log_text)
        
        # 日志控制按钮
        log_buttons = QHBoxLayout()
        
        self.clear_log_button = QPushButton("清空日志")
        self.clear_log_button.clicked.connect(self.clear_log)
        log_buttons.addWidget(self.clear_log_button)
        
        self.save_log_button = QPushButton("保存日志")
        self.save_log_button.clicked.connect(self.save_log)
        log_buttons.addWidget(self.save_log_button)
        
        log_buttons.addStretch()
        layout.addLayout(log_buttons)
        
        return group

    def _connect_signals(self) -> None:
        """连接信号和槽"""
        # 控制按钮
        self.start_button.clicked.connect(self._on_start_clicked)
        self.pause_button.clicked.connect(self._on_pause_clicked)
        self.stop_button.clicked.connect(self._on_stop_clicked)
        self.reset_button.clicked.connect(self._on_reset_clicked)

        # 参数变化
        self.duration_spin.valueChanged.connect(self._on_parameters_changed)
        self.time_step_spin.valueChanged.connect(self._on_parameters_changed)
        self.seed_spin.valueChanged.connect(self._on_parameters_changed)
        self.output_frequency_combo.currentTextChanged.connect(self._on_parameters_changed)

    def _setup_timer(self) -> None:
        """设置定时器"""
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self._update_status_display)
        self.update_timer.setInterval(1000)  # 每秒更新一次

    def _update_ui_state(self) -> None:
        """更新UI状态"""
        if self.current_status == SimulationStatus.NOT_STARTED:
            self.start_button.setEnabled(True)
            self.pause_button.setEnabled(False)
            self.stop_button.setEnabled(False)
            self.reset_button.setEnabled(True)
            self.status_label.setText("未开始")
            self.status_label.setStyleSheet("font-weight: bold; color: #666666;")

        elif self.current_status == SimulationStatus.RUNNING:
            self.start_button.setEnabled(False)
            self.pause_button.setEnabled(True)
            self.stop_button.setEnabled(True)
            self.reset_button.setEnabled(False)
            self.status_label.setText("运行中")
            self.status_label.setStyleSheet("font-weight: bold; color: #4CAF50;")

        elif self.current_status == SimulationStatus.PAUSED:
            self.start_button.setEnabled(True)
            self.start_button.setText("继续模拟")
            self.pause_button.setEnabled(False)
            self.stop_button.setEnabled(True)
            self.reset_button.setEnabled(True)
            self.status_label.setText("已暂停")
            self.status_label.setStyleSheet("font-weight: bold; color: #FF9800;")

        elif self.current_status == SimulationStatus.COMPLETED:
            self.start_button.setEnabled(False)
            self.pause_button.setEnabled(False)
            self.stop_button.setEnabled(False)
            self.reset_button.setEnabled(True)
            self.status_label.setText("已完成")
            self.status_label.setStyleSheet("font-weight: bold; color: #2196F3;")

        elif self.current_status == SimulationStatus.ERROR:
            self.start_button.setEnabled(False)
            self.pause_button.setEnabled(False)
            self.stop_button.setEnabled(False)
            self.reset_button.setEnabled(True)
            self.status_label.setText("错误")
            self.status_label.setStyleSheet("font-weight: bold; color: #f44336;")

        elif self.current_status == SimulationStatus.STOPPED:
            self.start_button.setEnabled(True)
            self.start_button.setText("开始模拟")
            self.pause_button.setEnabled(False)
            self.stop_button.setEnabled(False)
            self.reset_button.setEnabled(True)
            self.status_label.setText("已停止")
            self.status_label.setStyleSheet("font-weight: bold; color: #607D8B;")

    def _on_start_clicked(self) -> None:
        """开始按钮点击事件"""
        logging.info("用户点击开始模拟")

        # 收集参数
        params = {
            'duration_years': self.duration_spin.value(),
            'time_step': self.time_step_spin.value(),
            'random_seed': self.seed_spin.value(),
            'output_frequency': self.output_frequency_combo.currentText()
        }

        self.simulation_parameters = params
        self.start_time = datetime.now()
        self.current_status = SimulationStatus.RUNNING

        self._update_ui_state()
        self.update_timer.start()

        self.add_log_message("开始模拟...")
        self.simulation_start_requested.emit(params)

    def _on_pause_clicked(self) -> None:
        """暂停按钮点击事件"""
        logging.info("用户点击暂停模拟")

        self.current_status = SimulationStatus.PAUSED
        self._update_ui_state()
        self.update_timer.stop()

        self.add_log_message("模拟已暂停")
        self.simulation_pause_requested.emit()

    def _on_stop_clicked(self) -> None:
        """停止按钮点击事件"""
        logging.info("用户点击停止模拟")

        self.current_status = SimulationStatus.STOPPED
        self._update_ui_state()
        self.update_timer.stop()

        self.add_log_message("模拟已停止")
        self.simulation_stop_requested.emit()

    def _on_reset_clicked(self) -> None:
        """重置按钮点击事件"""
        logging.info("用户点击重置模拟")

        self.current_status = SimulationStatus.NOT_STARTED
        self.start_time = None
        self.simulation_state = None

        # 重置显示
        self.progress_bar.setValue(0)
        self.current_year_label.setText("--")
        self.elapsed_time_label.setText("--")
        self.remaining_time_label.setText("--")
        self.events_processed_label.setText("0")

        self._update_ui_state()
        self.update_timer.stop()

        self.add_log_message("模拟已重置")
        self.simulation_reset_requested.emit()

    def _on_parameters_changed(self) -> None:
        """参数变化事件"""
        params = {
            'duration_years': self.duration_spin.value(),
            'time_step': self.time_step_spin.value(),
            'random_seed': self.seed_spin.value(),
            'output_frequency': self.output_frequency_combo.currentText()
        }

        self.simulation_parameters = params
        self.parameters_changed.emit(params)

    def _update_status_display(self) -> None:
        """更新状态显示"""
        if self.simulation_state and self.start_time:
            # 更新进度
            progress = self.simulation_state.get_progress()
            self.progress_bar.setValue(int(progress * 100))

            # 更新当前年份
            current_year = int(self.simulation_state.current_year)
            self.current_year_label.setText(str(current_year))

            # 更新已用时间
            elapsed = datetime.now() - self.start_time
            elapsed_str = str(elapsed).split('.')[0]  # 去掉微秒
            self.elapsed_time_label.setText(elapsed_str)

            # 估算剩余时间
            if progress > 0:
                total_estimated = elapsed.total_seconds() / progress
                remaining_seconds = total_estimated - elapsed.total_seconds()
                if remaining_seconds > 0:
                    remaining_str = str(datetime.fromtimestamp(remaining_seconds) - datetime.fromtimestamp(0)).split('.')[0]
                    self.remaining_time_label.setText(remaining_str)
                else:
                    self.remaining_time_label.setText("即将完成")

            # 更新处理的事件数
            events_count = getattr(self.simulation_state, 'total_events_processed', 0)
            self.events_processed_label.setText(f"{events_count:,}")

    def add_log_message(self, message: str) -> None:
        """添加日志消息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        formatted_message = f"[{timestamp}] {message}"

        self.log_text.append(formatted_message)

        # 自动滚动到底部
        scrollbar = self.log_text.verticalScrollBar()
        scrollbar.setValue(scrollbar.maximum())

        logging.info(f"模拟日志: {message}")

    def clear_log(self) -> None:
        """清空日志"""
        self.log_text.clear()
        logging.info("清空模拟日志")

    def save_log(self) -> None:
        """保存日志"""
        from PyQt6.QtWidgets import QFileDialog

        file_path, _ = QFileDialog.getSaveFileName(
            self,
            "保存模拟日志",
            f"simulation_log_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt",
            "文本文件 (*.txt);;所有文件 (*)"
        )

        if file_path:
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(self.log_text.toPlainText())
                self.add_log_message(f"日志已保存到: {file_path}")
            except Exception as e:
                self.add_log_message(f"保存日志失败: {e}")
                logging.error(f"保存日志失败: {e}")

    # 公共接口方法
    def set_simulation_state(self, state: SimulationState) -> None:
        """设置模拟状态"""
        self.simulation_state = state
        self._update_status_display()

    def set_status(self, status: SimulationStatus) -> None:
        """设置模拟状态"""
        self.current_status = status
        self._update_ui_state()

    def get_parameters(self) -> Dict[str, Any]:
        """获取当前参数"""
        return self.simulation_parameters.copy()

    def update_progress(self, progress: float) -> None:
        """更新进度（0.0-1.0）"""
        self.progress_bar.setValue(int(progress * 100))

    def simulation_completed(self) -> None:
        """模拟完成"""
        self.current_status = SimulationStatus.COMPLETED
        self._update_ui_state()
        self.update_timer.stop()
        self.add_log_message("模拟完成！")

    def simulation_error(self, error_message: str) -> None:
        """模拟错误"""
        self.current_status = SimulationStatus.ERROR
        self._update_ui_state()
        self.update_timer.stop()
        self.add_log_message(f"模拟错误: {error_message}")
