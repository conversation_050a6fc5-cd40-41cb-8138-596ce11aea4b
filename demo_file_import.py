#!/usr/bin/env python3
"""
文件导入功能演示

演示人群配置界面的文件导入功能，支持个体和聚合两种数据格式
"""

import sys
import pandas as pd
import numpy as np
from pathlib import Path

# 添加src目录到Python路径
project_root = Path(__file__).parent
src_path = project_root / "src"
sys.path.insert(0, str(src_path))

def demo_file_validation():
    """演示文件验证功能"""
    print("=== 文件验证功能演示 ===\n")
    
    try:
        from interfaces.desktop.windows.config_wizard import PopulationConfigWidget
        
        # 创建配置组件实例（仅用于测试验证方法）
        widget = PopulationConfigWidget()
        
        # 测试个体数据格式
        print("1. 个体数据格式验证:")
        individual_df = pd.DataFrame({
            'age': [45, 50, 55, 60],
            'gender': ['M', 'F', 'M', 'F']
        })
        
        try:
            column_mapping = widget._validate_file_columns(individual_df)
            print(f"   ✅ 列映射: {column_mapping}")
            print(f"   📊 数据格式: 个体格式（每行一个人）")
        except Exception as e:
            print(f"   ❌ 验证失败: {e}")
        
        # 测试聚合数据格式
        print("\n2. 聚合数据格式验证:")
        aggregated_df = pd.DataFrame({
            'age': [45, 45, 50, 50],
            'gender': ['M', 'F', 'M', 'F'],
            'number': [120, 115, 150, 145]
        })
        
        try:
            column_mapping = widget._validate_file_columns(aggregated_df)
            print(f"   ✅ 列映射: {column_mapping}")
            print(f"   📊 数据格式: 聚合格式（年龄-性别-人数）")
        except Exception as e:
            print(f"   ❌ 验证失败: {e}")
        
        # 测试中文列名
        print("\n3. 中文列名验证:")
        chinese_df = pd.DataFrame({
            '年龄': [45, 50],
            '性别': ['男', '女'],
            '人数': [120, 115]
        })
        
        try:
            column_mapping = widget._validate_file_columns(chinese_df)
            print(f"   ✅ 列映射: {column_mapping}")
            print(f"   🇨🇳 支持中文列名")
        except Exception as e:
            print(f"   ❌ 验证失败: {e}")
            
    except ImportError as e:
        print(f"无法导入模块: {e}")
        print("这是正常的，因为需要PyQt6依赖")

def demo_data_processing():
    """演示数据处理功能"""
    print("\n=== 数据处理功能演示 ===\n")
    
    try:
        from interfaces.desktop.windows.config_wizard import PopulationConfigWidget
        
        widget = PopulationConfigWidget()
        
        # 演示个体数据处理
        print("1. 个体数据处理:")
        individual_df = pd.DataFrame({
            'age': [45, 50, 55, 60, 65, 45, 50, 55],
            'gender': ['M', 'F', 'M', 'F', 'M', 'F', 'M', 'F']
        })
        
        column_mapping = {'age': 'age', 'gender': 'gender'}
        age_dist, gender_dist = widget._process_population_data(individual_df, column_mapping)
        
        print(f"   总人数: {sum(age_dist.values())}")
        print(f"   年龄分布: {dict(sorted(age_dist.items()))}")
        print(f"   性别分布: {gender_dist}")
        
        # 演示聚合数据处理
        print("\n2. 聚合数据处理:")
        aggregated_df = pd.DataFrame({
            'age': [45, 45, 50, 50, 55, 55],
            'gender': ['M', 'F', 'M', 'F', 'M', 'F'],
            'number': [120, 115, 150, 145, 180, 175]
        })
        
        column_mapping = {'age': 'age', 'gender': 'gender', 'count': 'number'}
        age_dist, gender_dist = widget._process_population_data(aggregated_df, column_mapping)
        
        print(f"   总人数: {sum(age_dist.values())}")
        print(f"   年龄分布: {dict(sorted(age_dist.items()))}")
        print(f"   性别分布: {gender_dist}")
        
        # 演示中文数据处理
        print("\n3. 中文数据处理:")
        chinese_df = pd.DataFrame({
            '年龄': [45, 45, 50, 50],
            '性别': ['男', '女', '男', '女'],
            '人数': [120, 115, 150, 145]
        })
        
        column_mapping = {'age': '年龄', 'gender': '性别', 'count': '人数'}
        age_dist, gender_dist = widget._process_population_data(chinese_df, column_mapping)
        
        print(f"   总人数: {sum(age_dist.values())}")
        print(f"   年龄分布: {dict(sorted(age_dist.items()))}")
        print(f"   性别分布: {gender_dist}")
        
    except ImportError as e:
        print(f"无法导入模块: {e}")
    except Exception as e:
        print(f"处理过程中出错: {e}")

def demo_sample_files():
    """演示示例文件"""
    print("\n=== 示例文件演示 ===\n")
    
    data_dir = Path('data')
    
    sample_files = [
        ('sample_population.csv', '个体格式CSV'),
        ('sample_population_aggregated.csv', '聚合格式CSV'),
        ('sample_population_chinese.csv', '中文聚合格式CSV')
    ]
    
    for filename, description in sample_files:
        file_path = data_dir / filename
        if file_path.exists():
            print(f"📄 {description}: {filename}")
            
            try:
                df = pd.read_csv(file_path)
                print(f"   行数: {len(df)}")
                print(f"   列名: {list(df.columns)}")
                
                # 显示前几行
                print("   前3行数据:")
                for i, row in df.head(3).iterrows():
                    print(f"     {dict(row)}")
                
                # 如果有人数列，显示总人数
                count_cols = ['number', 'count', '人数', '数量']
                count_col = None
                for col in count_cols:
                    if col in df.columns:
                        count_col = col
                        break
                
                if count_col:
                    total_pop = df[count_col].sum()
                    print(f"   总人群规模: {total_pop:,}")
                else:
                    print(f"   个体记录数: {len(df)}")
                
                print()
                
            except Exception as e:
                print(f"   ❌ 读取文件失败: {e}")
        else:
            print(f"❌ 文件不存在: {filename}")

def demo_error_handling():
    """演示错误处理"""
    print("\n=== 错误处理演示 ===\n")
    
    try:
        from interfaces.desktop.windows.config_wizard import PopulationConfigWidget
        
        widget = PopulationConfigWidget()
        
        # 测试缺少必需列
        print("1. 缺少必需列:")
        try:
            df = pd.DataFrame({'other_col': [1, 2, 3]})
            widget._validate_file_columns(df)
        except ValueError as e:
            print(f"   ✅ 正确捕获错误: {e}")
        
        # 测试无效年龄数据
        print("\n2. 无效年龄数据:")
        try:
            df = pd.DataFrame({'age': ['abc', 'def'], 'gender': ['M', 'F']})
            column_mapping = {'age': 'age', 'gender': 'gender'}
            widget._process_population_data(df, column_mapping)
        except ValueError as e:
            print(f"   ✅ 正确捕获错误: {e}")
        
        # 测试无效性别数据
        print("\n3. 无效性别数据:")
        try:
            df = pd.DataFrame({'age': [45, 50], 'gender': ['X', 'Y']})
            column_mapping = {'age': 'age', 'gender': 'gender'}
            widget._process_population_data(df, column_mapping)
        except ValueError as e:
            print(f"   ✅ 正确捕获错误: {e}")
        
        # 测试无效人数数据
        print("\n4. 无效人数数据:")
        try:
            df = pd.DataFrame({'age': [45, 50], 'gender': ['M', 'F'], 'number': [-10, 0]})
            column_mapping = {'age': 'age', 'gender': 'gender', 'count': 'number'}
            widget._process_population_data(df, column_mapping)
        except ValueError as e:
            print(f"   ✅ 正确捕获错误: {e}")
            
    except ImportError as e:
        print(f"无法导入模块: {e}")

def demo_configuration_update():
    """演示配置更新"""
    print("\n=== 配置更新演示 ===\n")
    
    try:
        from interfaces.desktop.windows.config_wizard import PopulationConfig
        
        # 创建配置实例
        config = PopulationConfig()
        print("初始配置:")
        print(f"  人群规模: {config.size}")
        print(f"  年龄均值: {config.age_mean}")
        print(f"  男性比例: {config.male_ratio:.1%}")
        print(f"  使用导入数据: {config.use_imported_data}")
        
        # 模拟导入数据
        print("\n模拟导入聚合数据...")
        age_distribution = {45: 235, 50: 295, 55: 355, 60: 395, 65: 340}
        gender_distribution = {'Male': 810, 'Female': 810}
        
        config.imported_age_distribution = age_distribution
        config.imported_gender_distribution = gender_distribution
        config.use_imported_data = True
        
        # 计算更新后的配置
        total_size = sum(age_distribution.values())
        ages = []
        for age, count in age_distribution.items():
            ages.extend([age] * count)
        
        config.size = total_size
        config.age_mean = float(np.mean(ages))
        config.age_std = float(np.std(ages))
        config.age_min = min(age_distribution.keys())
        config.age_max = max(age_distribution.keys())
        
        male_count = gender_distribution.get('Male', 0)
        total_gender = sum(gender_distribution.values())
        config.male_ratio = male_count / total_gender if total_gender > 0 else 0.5
        
        print("更新后配置:")
        print(f"  人群规模: {config.size}")
        print(f"  年龄均值: {config.age_mean:.1f}")
        print(f"  年龄标准差: {config.age_std:.1f}")
        print(f"  年龄范围: {config.age_min}-{config.age_max}")
        print(f"  男性比例: {config.male_ratio:.1%}")
        print(f"  使用导入数据: {config.use_imported_data}")
        
    except ImportError as e:
        print(f"无法导入模块: {e}")

def main():
    """主演示函数"""
    print("结直肠癌筛查模拟器 - 文件导入功能演示")
    print("=" * 60)
    
    demo_file_validation()
    demo_data_processing()
    demo_sample_files()
    demo_error_handling()
    demo_configuration_update()
    
    print("\n" + "=" * 60)
    print("文件导入功能演示完成！")
    print("\n支持的文件格式:")
    print("• 个体格式: age, gender (每行代表一个人)")
    print("• 聚合格式: age, gender, number/count (每行代表一个年龄-性别组合的人数)")
    print("• 支持中文列名: 年龄, 性别, 人数")
    print("• 支持文件类型: CSV, Excel (.xlsx, .xls)")
    print("\n这个功能允许直接导入真实的人群分布数据，")
    print("而不需要假设正态分布，更符合实际情况。")

if __name__ == "__main__":
    main()
