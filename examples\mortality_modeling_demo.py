"""
生命表和死亡率建模演示脚本

演示如何使用生命表和死亡率引擎进行死亡率建模和生存分析。
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.modules.population import LifeTable, LifeTableType, PopulationGenerator
from src.core import MortalityEngine, Gender, Individual, Population


def demo_life_table_basic_usage():
    """演示生命表基本使用"""
    print("\n" + "=" * 60)
    print("生命表基本使用演示")
    print("=" * 60)
    
    # 创建生命表管理器
    life_table = LifeTable()
    
    try:
        # 加载中国生命表数据
        china_table = life_table.load_predefined_table(LifeTableType.CHINA_2020)
        print(f"✅ 成功加载生命表: {china_table.name}")
        print(f"   数据来源: {china_table.source}")
        print(f"   数据年份: {china_table.year}")
        print(f"   数据记录数: {len(china_table.data)}")
        
    except FileNotFoundError:
        print("❌ 中国生命表数据文件不存在，使用示例数据")
        return None
    
    # 查询不同年龄的死亡率
    print("\n📊 年龄特异性死亡率查询:")
    ages = [50, 60, 70, 80]
    
    for age in ages:
        male_rate = life_table.get_mortality_rate(age, "male")
        female_rate = life_table.get_mortality_rate(age, "female")
        
        print(f"   {age}岁: 男性 {male_rate:.5f}, 女性 {female_rate:.5f}")
    
    # 计算生存概率和预期寿命
    print("\n📈 生存概率和预期寿命:")
    test_age = 65
    male_survival = life_table.get_survival_probability(test_age, "male")
    female_survival = life_table.get_survival_probability(test_age, "female")
    male_life_exp = life_table.get_life_expectancy(test_age, "male")
    female_life_exp = life_table.get_life_expectancy(test_age, "female")
    
    print(f"   {test_age}岁男性: 生存概率 {male_survival:.5f}, 预期寿命 {male_life_exp:.1f}年")
    print(f"   {test_age}岁女性: 生存概率 {female_survival:.5f}, 预期寿命 {female_life_exp:.1f}年")
    
    return life_table


def demo_data_validation():
    """演示数据验证功能"""
    print("\n" + "=" * 60)
    print("生命表数据验证演示")
    print("=" * 60)
    
    life_table = LifeTable()
    
    try:
        life_table.load_predefined_table(LifeTableType.CHINA_2020)
        
        # 验证数据质量
        validation_result = life_table.validate_table_data()
        
        print(f"📋 数据验证结果:")
        print(f"   生命表名称: {validation_result['table_name']}")
        print(f"   总记录数: {validation_result['total_records']}")
        print(f"   年龄范围: {validation_result['age_range']['min']:.0f} - {validation_result['age_range']['max']:.0f}岁")
        print(f"   性别覆盖: {', '.join(validation_result['genders'])}")
        print(f"   死亡率范围: {validation_result['mortality_rate_range']['min']:.6f} - {validation_result['mortality_rate_range']['max']:.6f}")
        
        # 数据完整性检查
        print(f"\n📊 数据完整性:")
        for gender, completeness in validation_result['data_completeness'].items():
            coverage = completeness['coverage_ratio']
            print(f"   {gender}: {completeness['records']}条记录, 覆盖率 {coverage:.1%}")
        
        # 数据问题报告
        if validation_result['issues']:
            print(f"\n⚠️  发现的问题:")
            for issue in validation_result['issues']:
                print(f"   - {issue}")
        else:
            print(f"\n✅ 数据质量良好，未发现问题")
            
    except FileNotFoundError:
        print("❌ 生命表数据文件不存在")


def demo_mortality_engine():
    """演示死亡率引擎功能"""
    print("\n" + "=" * 60)
    print("死亡率引擎演示")
    print("=" * 60)
    
    # 创建生命表
    life_table = LifeTable()
    
    try:
        life_table.load_predefined_table(LifeTableType.CHINA_2020)
    except FileNotFoundError:
        print("❌ 生命表数据文件不存在，跳过演示")
        return
    
    # 创建死亡率引擎
    mortality_engine = MortalityEngine(
        life_table=life_table,
        random_seed=42,
        time_precision="monthly"
    )
    
    print(f"✅ 创建死亡率引擎 (时间精度: 月度)")
    
    # 创建测试个体
    individual = Individual(
        birth_year=1960,  # 65岁
        gender=Gender.MALE,
        individual_id="test_person"
    )
    
    print(f"\n👤 测试个体: {individual.individual_id}")
    print(f"   出生年份: {individual.birth_year}")
    print(f"   性别: {individual.gender.value}")
    print(f"   当前年龄: {individual.get_current_age(2025)}岁")
    
    # 模拟多年的死亡率应用
    print(f"\n⏰ 模拟10年死亡率应用:")
    
    for year in range(2025, 2035):
        if individual.is_alive():
            death_occurred = mortality_engine.apply_mortality_to_individual(
                individual=individual,
                current_time=float(year),
                time_step=1.0
            )
            
            age = individual.get_current_age(year)
            mortality_rate = life_table.get_mortality_rate(age, individual.gender)
            
            if death_occurred:
                print(f"   {year}年 (年龄{age}): ☠️  死亡 (死亡率: {mortality_rate:.5f})")
                print(f"   死亡状态: {individual.current_disease_state.value}")
                break
            else:
                print(f"   {year}年 (年龄{age}): ✅ 存活 (死亡率: {mortality_rate:.5f})")
        else:
            break


def demo_population_mortality():
    """演示人群死亡率应用"""
    print("\n" + "=" * 60)
    print("人群死亡率应用演示")
    print("=" * 60)
    
    # 创建生命表和死亡率引擎
    life_table = LifeTable()
    
    try:
        life_table.load_predefined_table(LifeTableType.CHINA_2020)
    except FileNotFoundError:
        print("❌ 生命表数据文件不存在，跳过演示")
        return
    
    mortality_engine = MortalityEngine(
        life_table=life_table,
        random_seed=42,
        time_precision="monthly"
    )
    
    # 生成测试人群
    generator = PopulationGenerator(random_seed=42)
    
    population = generator.generate_population(
        size=1000,
        age_distribution={
            "type": "normal",
            "mean": 65.0,
            "std": 10.0,
            "min_age": 50,
            "max_age": 80
        },
        gender_distribution={
            "male_ratio": 0.5,
            "female_ratio": 0.5
        },
        show_progress=False
    )
    
    print(f"👥 生成测试人群: {population.get_size()}个体")
    
    # 获取初始统计
    initial_stats = population.statistics.get_survival_statistics()
    print(f"   初始存活率: {initial_stats['survival_rate']:.1%}")
    
    # 应用5年的死亡率
    print(f"\n⏰ 应用5年死亡率:")
    
    for year in range(2025, 2030):
        result = mortality_engine.apply_mortality_to_population(
            population=population,
            current_time=float(year),
            time_step=1.0,
            show_progress=False
        )
        
        survival_stats = population.statistics.get_survival_statistics()
        
        print(f"   {year}年: 死亡 {result.deaths_applied}人 "
              f"(自然死亡: {result.natural_deaths}, 癌症死亡: {result.cancer_deaths})")
        print(f"         存活率: {survival_stats['survival_rate']:.1%}, "
              f"存活人数: {survival_stats['alive_count']}")
    
    # 最终统计
    final_stats = population.statistics.get_survival_statistics()
    print(f"\n📊 最终统计:")
    print(f"   总个体数: {final_stats['total_individuals']}")
    print(f"   存活人数: {final_stats['alive_count']}")
    print(f"   死亡人数: {final_stats['dead_count']}")
    print(f"   存活率: {final_stats['survival_rate']:.1%}")
    
    if 'death_causes' in final_stats:
        print(f"   死亡原因分布:")
        for cause, count in final_stats['death_causes'].items():
            print(f"     {cause}: {count}人")


def demo_survival_analysis():
    """演示生存分析功能"""
    print("\n" + "=" * 60)
    print("生存分析演示")
    print("=" * 60)
    
    # 创建测试人群（已经应用了死亡率）
    generator = PopulationGenerator(random_seed=42)
    
    population = generator.generate_population(
        size=500,
        age_distribution={
            "type": "uniform",
            "min_age": 60,
            "max_age": 80
        },
        gender_distribution={
            "male_ratio": 0.5,
            "female_ratio": 0.5
        },
        show_progress=False
    )
    
    print(f"👥 创建测试人群: {population.get_size()}个体")
    
    # 计算生存曲线
    survival_curve = population.calculate_survival_curve(
        time_points=[0, 5, 10, 15, 20],
        reference_year=2025
    )
    
    print(f"\n📈 生存曲线分析:")
    print(f"   总个体数: {survival_curve['total_individuals']}")
    print(f"   总事件数: {survival_curve['total_events']}")
    
    for i, time_point in enumerate(survival_curve['time_points']):
        survival_prob = survival_curve['survival_probabilities'][i]
        at_risk = survival_curve['at_risk'][i]
        print(f"   {time_point}年: 生存概率 {survival_prob:.3f}, 风险人数 {at_risk}")
    
    # 队列生存分析
    cohort_analysis = population.get_cohort_survival_analysis(
        cohort_definition="age_group",
        reference_year=2025
    )
    
    print(f"\n👥 队列生存分析 (按年龄组):")
    print(f"   队列数量: {cohort_analysis['total_cohorts']}")
    
    for cohort_name, cohort_stats in cohort_analysis['cohorts'].items():
        if cohort_stats['total_individuals'] > 0:
            print(f"   {cohort_name}: {cohort_stats['total_individuals']}人, "
                  f"存活率 {cohort_stats['survival_rate']:.1%}, "
                  f"平均年龄 {cohort_stats['mean_age']:.1f}岁")


def main():
    """主演示函数"""
    print("🧬 结直肠癌筛查微观模拟模型 - 生命表和死亡率建模演示")
    print("=" * 80)
    
    try:
        # 基本生命表使用
        life_table = demo_life_table_basic_usage()
        
        # 数据验证
        demo_data_validation()
        
        # 死亡率引擎
        demo_mortality_engine()
        
        # 人群死亡率应用
        demo_population_mortality()
        
        # 生存分析
        demo_survival_analysis()
        
        print("\n" + "=" * 80)
        print("✅ 演示完成！")
        print("\n💡 提示:")
        print("   - 生命表数据位于 data/life_tables/ 目录")
        print("   - API文档位于 docs/api/ 目录")
        print("   - 更多示例请参考测试文件")
        
    except Exception as e:
        print(f"\n❌ 演示过程中发生错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
