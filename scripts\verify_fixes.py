#!/usr/bin/env python3
"""
验证QA修复脚本

验证所有QA反馈的问题是否已正确修复。
"""

import sys
from pathlib import Path

def verify_organization_info():
    """验证组织信息统一性"""
    print("🔍 验证组织信息统一性...")
    
    issues = []
    expected_org = "深圳市南山区慢性病防治院"
    
    # 检查主应用程序文件
    main_py = Path("src/interfaces/desktop/main.py")
    if main_py.exists():
        content = main_py.read_text(encoding='utf-8')
        if expected_org in content:
            print(f"  ✅ {main_py}: 组织信息正确")
        else:
            issues.append(f"{main_py}: 组织信息不匹配")
    
    # 检查Windows安装程序
    installer_iss = Path("installer/windows/installer.iss")
    if installer_iss.exists():
        content = installer_iss.read_text(encoding='utf-8')
        if expected_org in content:
            print(f"  ✅ {installer_iss}: 组织信息正确")
        else:
            issues.append(f"{installer_iss}: 组织信息不匹配")
    
    # 检查Linux桌面文件
    desktop_file = Path("installer/linux/colorectal-cancer-simulator.desktop")
    if desktop_file.exists():
        content = desktop_file.read_text(encoding='utf-8')
        if expected_org in content:
            print(f"  ✅ {desktop_file}: 组织信息正确")
        else:
            issues.append(f"{desktop_file}: 组织信息不匹配")
    
    return issues


def verify_main_window_integration():
    """验证主窗口组件集成"""
    print("\n🔍 验证主窗口组件集成...")
    
    issues = []
    main_py = Path("src/interfaces/desktop/main.py")
    
    if not main_py.exists():
        issues.append("主应用程序文件不存在")
        return issues
    
    content = main_py.read_text(encoding='utf-8')
    
    # 检查是否导入了实际组件
    required_imports = [
        "from .windows.config_wizard import PopulationConfigWidget",
        "from .widgets.simulation_control import SimulationControlWidget"
    ]
    
    for import_line in required_imports:
        if import_line in content:
            print(f"  ✅ 已导入: {import_line.split()[-1]}")
        else:
            issues.append(f"缺少导入: {import_line}")
    
    # 检查是否创建了实际组件实例
    required_components = [
        "self.config_widget = PopulationConfigWidget()",
        "self.simulation_control = SimulationControlWidget()"
    ]
    
    for component in required_components:
        if component in content:
            print(f"  ✅ 已创建组件: {component.split('=')[0].strip()}")
        else:
            issues.append(f"缺少组件创建: {component}")
    
    # 检查信号连接
    if "_connect_component_signals" in content:
        print("  ✅ 已实现组件信号连接")
    else:
        issues.append("缺少组件信号连接")
    
    return issues


def verify_matplotlib_backend():
    """验证Matplotlib后端兼容性"""
    print("\n🔍 验证Matplotlib后端兼容性...")
    
    issues = []
    results_viewer = Path("src/interfaces/desktop/windows/results_viewer.py")
    
    if not results_viewer.exists():
        issues.append("结果查看器文件不存在")
        return issues
    
    content = results_viewer.read_text(encoding='utf-8')
    
    # 检查是否有后端兼容性处理
    if "backend_qtagg" in content and "backend_qt5agg" in content:
        print("  ✅ 已实现PyQt6/PyQt5后端兼容性")
    else:
        issues.append("缺少Matplotlib后端兼容性处理")
    
    # 检查是否有错误处理
    if "MATPLOTLIB_AVAILABLE" in content:
        print("  ✅ 已实现Matplotlib可用性检查")
    else:
        issues.append("缺少Matplotlib可用性检查")
    
    return issues


def verify_icon_resources():
    """验证图标资源"""
    print("\n🔍 验证图标资源...")
    
    issues = []
    icons_dir = Path("resources/icons")
    
    if not icons_dir.exists():
        issues.append("图标目录不存在")
        return issues
    
    # 检查必要的图标文件
    required_icons = [
        "app.png",
        "app.ico",
        "app_16.png",
        "app_32.png",
        "app_48.png",
        "app_64.png",
        "app_128.png",
        "app_256.png"
    ]
    
    for icon_file in required_icons:
        icon_path = icons_dir / icon_file
        if icon_path.exists():
            print(f"  ✅ 图标文件存在: {icon_file}")
        else:
            issues.append(f"缺少图标文件: {icon_file}")
    
    # 检查图标加载代码
    main_py = Path("src/interfaces/desktop/main.py")
    if main_py.exists():
        content = main_py.read_text(encoding='utf-8')
        if "resources/icons/app.png" in content:
            print("  ✅ 应用程序已配置图标加载")
        else:
            issues.append("应用程序未配置图标加载")
    
    return issues


def verify_import_consistency():
    """验证导入一致性"""
    print("\n🔍 验证导入一致性...")
    
    issues = []
    
    try:
        # 尝试导入主要模块
        sys.path.insert(0, str(Path.cwd()))
        
        from src.interfaces.desktop.main import Application, MainWindow
        print("  ✅ 主应用程序模块导入成功")
        
        from src.interfaces.desktop.windows.config_wizard import ConfigWizard, PopulationConfigWidget
        print("  ✅ 配置向导模块导入成功")
        
        from src.interfaces.desktop.widgets.simulation_control import SimulationControlWidget
        print("  ✅ 模拟控制模块导入成功")
        
        from src.interfaces.desktop.windows.results_viewer import ResultsWindow
        print("  ✅ 结果查看器模块导入成功")
        
        from src.interfaces.desktop.utils.validators import create_age_validator
        print("  ✅ 验证器模块导入成功")
        
    except ImportError as e:
        issues.append(f"导入错误: {e}")
    
    return issues


def main():
    """主验证函数"""
    print("🚀 开始验证QA修复...")
    print("=" * 50)
    
    all_issues = []
    
    # 执行所有验证
    all_issues.extend(verify_organization_info())
    all_issues.extend(verify_main_window_integration())
    all_issues.extend(verify_matplotlib_backend())
    all_issues.extend(verify_icon_resources())
    all_issues.extend(verify_import_consistency())
    
    print("\n" + "=" * 50)
    
    if not all_issues:
        print("🎉 所有QA问题已成功修复！")
        print("\n✅ 修复总结:")
        print("  - 组织信息已统一为'深圳市南山区慢性病防治院'")
        print("  - 主窗口已集成实际组件")
        print("  - Matplotlib后端兼容性已修复")
        print("  - 图标资源已添加")
        print("  - 导入一致性已验证")
        return 0
    else:
        print("❌ 发现以下问题需要修复:")
        for i, issue in enumerate(all_issues, 1):
            print(f"  {i}. {issue}")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
