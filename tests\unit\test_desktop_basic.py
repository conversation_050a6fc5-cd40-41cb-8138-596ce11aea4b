"""
桌面应用程序基本功能测试

测试不需要GUI的基本功能。
"""

import pytest
import sys
from unittest.mock import patch, MagicMock


def test_imports():
    """测试模块导入"""
    try:
        from src.interfaces.desktop.main import Application, MainWindow
        from src.interfaces.desktop.windows.config_wizard import PopulationConfigWidget
        assert True  # 如果能导入就说明基本结构正确
    except ImportError as e:
        pytest.fail(f"导入失败: {e}")


def test_application_constants():
    """测试应用程序常量"""
    # 这个测试不需要创建QApplication实例
    from src.interfaces.desktop.main import Application
    
    # 测试类是否存在
    assert Application is not None
    assert hasattr(Application, '__init__')


def test_main_window_constants():
    """测试主窗口常量"""
    from src.interfaces.desktop.main import MainWindow
    
    # 测试类是否存在
    assert MainWindow is not None
    assert hasattr(MainWindow, '__init__')


@patch('src.interfaces.desktop.main.QApplication')
def test_main_function_mock(mock_qapp):
    """测试main函数（使用mock）"""
    from src.interfaces.desktop.main import main
    
    # 设置mock
    mock_app = MagicMock()
    mock_app.exec.return_value = 0
    mock_qapp.return_value = mock_app
    
    # 调用main函数
    result = main()
    
    assert result == 0


def test_config_widget_import():
    """测试配置组件导入"""
    try:
        from src.interfaces.desktop.windows.config_wizard import PopulationConfigWidget
        assert PopulationConfigWidget is not None
    except ImportError as e:
        pytest.fail(f"配置组件导入失败: {e}")


def test_module_structure():
    """测试模块结构"""
    import src.interfaces.desktop
    import src.interfaces.desktop.main
    import src.interfaces.desktop.windows
    
    # 检查__all__定义
    assert hasattr(src.interfaces.desktop, '__all__')
    assert 'Application' in src.interfaces.desktop.__all__
    assert 'MainWindow' in src.interfaces.desktop.__all__
