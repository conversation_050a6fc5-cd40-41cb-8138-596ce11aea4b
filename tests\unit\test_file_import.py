"""
文件导入功能单元测试

测试PopulationConfigWidget的文件导入功能
"""

import pytest
import sys
import tempfile
import pandas as pd
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock
from PyQt6.QtWidgets import QApplication
from PyQt6.QtTest import QTest
from PyQt6.QtCore import Qt

# 添加src路径
sys.path.insert(0, "src")

from interfaces.desktop.windows.config_wizard import PopulationConfigWidget, PopulationConfig


@pytest.fixture(scope="session")
def qapp():
    """创建QApplication实例"""
    if not QApplication.instance():
        app = QApplication([])
        yield app
        app.quit()
    else:
        yield QApplication.instance()


@pytest.fixture
def config_widget(qapp):
    """创建PopulationConfigWidget实例"""
    return PopulationConfigWidget()


@pytest.fixture
def sample_csv_file():
    """创建示例CSV文件"""
    with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False, encoding='utf-8') as f:
        f.write("age,gender\n")
        f.write("45,M\n")
        f.write("50,F\n")
        f.write("55,M\n")
        f.write("60,F\n")
        f.write("65,M\n")
        f.write("70,F\n")
        f.write("75,M\n")
        f.write("80,F\n")
        
        temp_path = f.name
    
    yield temp_path
    
    # 清理
    Path(temp_path).unlink(missing_ok=True)


@pytest.fixture
def sample_excel_file():
    """创建示例Excel文件"""
    data = {
        'age': [45, 50, 55, 60, 65, 70, 75, 80],
        'gender': ['M', 'F', 'M', 'F', 'M', 'F', 'M', 'F']
    }
    df = pd.DataFrame(data)
    
    with tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False) as f:
        temp_path = f.name
    
    df.to_excel(temp_path, index=False)
    
    yield temp_path
    
    # 清理
    Path(temp_path).unlink(missing_ok=True)


@pytest.fixture
def chinese_csv_file():
    """创建中文CSV文件"""
    with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False, encoding='utf-8') as f:
        f.write("年龄,性别\n")
        f.write("45,男\n")
        f.write("50,女\n")
        f.write("55,男\n")
        f.write("60,女\n")
        f.write("65,男\n")
        f.write("70,女\n")

        temp_path = f.name

    yield temp_path

    # 清理
    Path(temp_path).unlink(missing_ok=True)


@pytest.fixture
def aggregated_csv_file():
    """创建聚合格式CSV文件"""
    with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False, encoding='utf-8') as f:
        f.write("age,gender,number\n")
        f.write("45,M,120\n")
        f.write("45,F,115\n")
        f.write("50,M,150\n")
        f.write("50,F,145\n")
        f.write("55,M,180\n")
        f.write("55,F,175\n")
        f.write("60,M,200\n")
        f.write("60,F,195\n")
        f.write("65,M,170\n")
        f.write("65,F,165\n")

        temp_path = f.name

    yield temp_path

    # 清理
    Path(temp_path).unlink(missing_ok=True)


@pytest.fixture
def chinese_aggregated_csv_file():
    """创建中文聚合格式CSV文件"""
    with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False, encoding='utf-8') as f:
        f.write("年龄,性别,人数\n")
        f.write("45,男,120\n")
        f.write("45,女,115\n")
        f.write("50,男,150\n")
        f.write("50,女,145\n")
        f.write("55,男,180\n")
        f.write("55,女,175\n")

        temp_path = f.name

    yield temp_path

    # 清理
    Path(temp_path).unlink(missing_ok=True)


class TestFileImportUI:
    """测试文件导入UI组件"""
    
    def test_file_import_ui_components(self, config_widget):
        """测试文件导入UI组件存在"""
        assert hasattr(config_widget, 'file_path_label')
        assert hasattr(config_widget, 'browse_file_button')
        assert hasattr(config_widget, 'use_imported_checkbox')
        assert hasattr(config_widget, 'import_preview_text')
    
    def test_initial_file_import_state(self, config_widget):
        """测试初始文件导入状态"""
        assert config_widget.file_path_label.text() == "未选择文件"
        assert config_widget.use_imported_checkbox.currentText() == "使用手动配置"
        assert not config_widget.config.use_imported_data
        assert config_widget.config.imported_file_path == ""
    
    def test_import_mode_toggle(self, config_widget):
        """测试导入模式切换"""
        # 初始状态：手动配置模式，控件应该启用
        assert config_widget.size_spinbox.isEnabled()
        assert config_widget.age_mean_spinbox.isEnabled()
        
        # 切换到导入模式
        config_widget.use_imported_checkbox.setCurrentText("使用导入文件数据")
        config_widget._on_import_mode_changed()
        
        # 控件应该禁用
        assert not config_widget.size_spinbox.isEnabled()
        assert not config_widget.age_mean_spinbox.isEnabled()
        assert config_widget.config.use_imported_data


class TestFileValidation:
    """测试文件验证功能"""
    
    def test_validate_csv_columns(self, config_widget, sample_csv_file):
        """测试CSV文件列验证"""
        df = pd.read_csv(sample_csv_file)
        column_mapping = config_widget._validate_file_columns(df)
        
        assert 'age' in column_mapping
        assert 'gender' in column_mapping
        assert column_mapping['age'] == 'age'
        assert column_mapping['gender'] == 'gender'
    
    def test_validate_chinese_columns(self, config_widget, chinese_csv_file):
        """测试中文列名验证"""
        df = pd.read_csv(chinese_csv_file)
        column_mapping = config_widget._validate_file_columns(df)
        
        assert 'age' in column_mapping
        assert 'gender' in column_mapping
        assert column_mapping['age'] == '年龄'
        assert column_mapping['gender'] == '性别'
    
    def test_missing_age_column_error(self, config_widget):
        """测试缺少年龄列的错误"""
        df = pd.DataFrame({'gender': ['M', 'F'], 'other': [1, 2]})
        
        with pytest.raises(ValueError, match="未找到年龄列"):
            config_widget._validate_file_columns(df)
    
    def test_missing_gender_column_error(self, config_widget):
        """测试缺少性别列的错误"""
        df = pd.DataFrame({'age': [45, 50], 'other': [1, 2]})
        
        with pytest.raises(ValueError, match="未找到性别列"):
            config_widget._validate_file_columns(df)


class TestDataProcessing:
    """测试数据处理功能"""

    def test_process_individual_data(self, config_widget, sample_csv_file):
        """测试个体数据处理"""
        df = pd.read_csv(sample_csv_file)
        column_mapping = {'age': 'age', 'gender': 'gender'}

        age_dist, gender_dist = config_widget._process_population_data(df, column_mapping)

        # 检查年龄分布
        assert isinstance(age_dist, dict)
        assert all(isinstance(age, int) for age in age_dist.keys())
        assert all(isinstance(count, (int, np.integer)) for count in age_dist.values())

        # 检查性别分布
        assert isinstance(gender_dist, dict)
        assert 'Male' in gender_dist or 'Female' in gender_dist
        assert sum(gender_dist.values()) == len(df)

    def test_process_aggregated_data(self, config_widget, aggregated_csv_file):
        """测试聚合数据处理"""
        df = pd.read_csv(aggregated_csv_file)
        column_mapping = {'age': 'age', 'gender': 'gender', 'count': 'number'}

        age_dist, gender_dist = config_widget._process_population_data(df, column_mapping)

        # 检查年龄分布
        assert isinstance(age_dist, dict)
        assert all(isinstance(age, int) for age in age_dist.keys())
        assert all(isinstance(count, (int, np.integer)) for count in age_dist.values())

        # 检查性别分布
        assert isinstance(gender_dist, dict)
        assert 'Male' in gender_dist
        assert 'Female' in gender_dist

        # 检查总人数是否等于原始数据中的人数总和
        expected_total = df['number'].sum()
        actual_total = sum(age_dist.values())
        assert actual_total == expected_total

    def test_detect_aggregated_format(self, config_widget, aggregated_csv_file):
        """测试聚合格式检测"""
        df = pd.read_csv(aggregated_csv_file)
        column_mapping = config_widget._validate_file_columns(df)

        # 应该检测到count列
        assert 'count' in column_mapping
        assert column_mapping['count'] == 'number'
    
    def test_process_chinese_gender_data(self, config_widget, chinese_csv_file):
        """测试中文性别数据处理"""
        df = pd.read_csv(chinese_csv_file)
        column_mapping = {'age': '年龄', 'gender': '性别'}
        
        age_dist, gender_dist = config_widget._process_population_data(df, column_mapping)
        
        # 检查性别标准化
        assert 'Male' in gender_dist
        assert 'Female' in gender_dist
        assert sum(gender_dist.values()) == len(df)
    
    def test_invalid_age_data_error(self, config_widget):
        """测试无效年龄数据错误"""
        df = pd.DataFrame({'age': ['abc', 'def'], 'gender': ['M', 'F']})
        column_mapping = {'age': 'age', 'gender': 'gender'}
        
        with pytest.raises(ValueError, match="年龄列包含无效数据"):
            config_widget._process_population_data(df, column_mapping)
    
    def test_age_range_validation(self, config_widget):
        """测试年龄范围验证"""
        df = pd.DataFrame({'age': [150, 200], 'gender': ['M', 'F']})
        column_mapping = {'age': 'age', 'gender': 'gender'}
        
        with pytest.raises(ValueError, match="年龄数据超出合理范围"):
            config_widget._process_population_data(df, column_mapping)
    
    def test_invalid_gender_data_error(self, config_widget):
        """测试无效性别数据错误"""
        df = pd.DataFrame({'age': [45, 50], 'gender': ['X', 'Y']})
        column_mapping = {'age': 'age', 'gender': 'gender'}

        with pytest.raises(ValueError, match="无效的性别值"):
            config_widget._process_population_data(df, column_mapping)

    def test_invalid_count_data_error(self, config_widget):
        """测试无效人数数据错误"""
        df = pd.DataFrame({'age': [45, 50], 'gender': ['M', 'F'], 'number': ['abc', 'def']})
        column_mapping = {'age': 'age', 'gender': 'gender', 'count': 'number'}

        with pytest.raises(ValueError, match="人数列包含无效数据"):
            config_widget._process_population_data(df, column_mapping)

    def test_negative_count_error(self, config_widget):
        """测试负数人数错误"""
        df = pd.DataFrame({'age': [45, 50], 'gender': ['M', 'F'], 'number': [-10, 0]})
        column_mapping = {'age': 'age', 'gender': 'gender', 'count': 'number'}

        with pytest.raises(ValueError, match="人数必须为正整数"):
            config_widget._process_population_data(df, column_mapping)

    def test_chinese_aggregated_data(self, config_widget, chinese_aggregated_csv_file):
        """测试中文聚合数据处理"""
        df = pd.read_csv(chinese_aggregated_csv_file)
        column_mapping = config_widget._validate_file_columns(df)

        age_dist, gender_dist = config_widget._process_population_data(df, column_mapping)

        # 检查性别标准化
        assert 'Male' in gender_dist
        assert 'Female' in gender_dist

        # 检查总人数
        expected_total = df['人数'].sum()
        actual_total = sum(age_dist.values())
        assert actual_total == expected_total


class TestFileLoading:
    """测试文件加载功能"""
    
    def test_load_csv_file(self, config_widget, sample_csv_file):
        """测试加载CSV文件"""
        config_widget._load_population_file(sample_csv_file)
        
        # 检查配置更新
        assert config_widget.config.imported_file_path == sample_csv_file
        assert config_widget.config.imported_age_distribution is not None
        assert config_widget.config.imported_gender_distribution is not None
        
        # 检查UI更新
        assert "已选择:" in config_widget.file_path_label.text()
    
    def test_load_excel_file(self, config_widget, sample_excel_file):
        """测试加载Excel文件"""
        config_widget._load_population_file(sample_excel_file)
        
        # 检查配置更新
        assert config_widget.config.imported_file_path == sample_excel_file
        assert config_widget.config.imported_age_distribution is not None
        assert config_widget.config.imported_gender_distribution is not None
    
    def test_unsupported_file_format_error(self, config_widget):
        """测试不支持的文件格式错误"""
        with tempfile.NamedTemporaryFile(suffix='.txt', delete=False) as f:
            temp_path = f.name
        
        try:
            with pytest.raises(ValueError, match="不支持的文件格式"):
                config_widget._load_population_file(temp_path)
        finally:
            Path(temp_path).unlink(missing_ok=True)


class TestConfigurationUpdate:
    """测试配置更新功能"""
    
    def test_update_config_from_imported_data(self, config_widget, sample_csv_file):
        """测试从导入数据更新配置"""
        # 加载文件
        config_widget._load_population_file(sample_csv_file)
        
        # 切换到导入模式
        config_widget.config.use_imported_data = True
        config_widget._update_config_from_imported_data()
        
        # 检查配置更新
        assert config_widget.config.size > 0
        assert config_widget.config.age_min > 0
        assert config_widget.config.age_max > config_widget.config.age_min
        assert 0 <= config_widget.config.male_ratio <= 1
        assert config_widget.config.distribution_type == "imported"
    
    def test_preview_generation_with_imported_data(self, config_widget, sample_csv_file):
        """测试使用导入数据生成预览"""
        # 加载文件并切换到导入模式
        config_widget._load_population_file(sample_csv_file)
        config_widget.config.use_imported_data = True
        
        # 生成预览
        config_widget._generate_preview()
        
        preview_text = config_widget.preview_text.toPlainText()
        assert "基于导入文件" in preview_text
        assert "实际数据分布" in preview_text


class TestIntegration:
    """测试集成功能"""
    
    @patch('PyQt6.QtWidgets.QFileDialog.getOpenFileName')
    def test_browse_file_workflow(self, mock_dialog, config_widget, sample_csv_file):
        """测试浏览文件工作流"""
        # 模拟文件对话框返回
        mock_dialog.return_value = (sample_csv_file, "CSV文件 (*.csv)")
        
        # 触发浏览文件
        config_widget._browse_population_file()
        
        # 检查文件是否加载
        assert config_widget.config.imported_file_path == sample_csv_file
        assert "已选择:" in config_widget.file_path_label.text()
    
    @patch('PyQt6.QtWidgets.QMessageBox.question')
    def test_auto_switch_to_import_mode(self, mock_question, config_widget, sample_csv_file):
        """测试自动切换到导入模式"""
        # 模拟用户选择"是"
        mock_question.return_value = QMessageBox.StandardButton.Yes
        
        # 加载文件
        config_widget._load_population_file(sample_csv_file)
        
        # 检查是否切换到导入模式
        assert config_widget.use_imported_checkbox.currentText() == "使用导入文件数据"
    
    def test_complete_import_workflow(self, config_widget, sample_csv_file):
        """测试完整的导入工作流"""
        # 1. 加载文件
        config_widget._load_population_file(sample_csv_file)
        
        # 2. 切换到导入模式
        config_widget.use_imported_checkbox.setCurrentText("使用导入文件数据")
        config_widget._on_import_mode_changed()
        
        # 3. 检查最终状态
        assert config_widget.config.use_imported_data
        assert config_widget.config.imported_age_distribution is not None
        assert config_widget.config.size > 0
        assert not config_widget.size_spinbox.isEnabled()  # 手动控件应该禁用
        
        # 4. 检查预览
        preview_text = config_widget.preview_text.toPlainText()
        assert "基于导入文件" in preview_text


if __name__ == "__main__":
    pytest.main([__file__])
