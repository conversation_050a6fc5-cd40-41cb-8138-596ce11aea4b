"""
人群配置管理模块

实现PopulationConfig类，用于加载、验证和管理人群生成配置。
支持YAML和JSON格式的配置文件。
"""

import json
import yaml
from pathlib import Path
from typing import Dict, Any, Optional, Union
from dataclasses import dataclass, asdict

from ...utils import ValidationError, ParameterValidationError


@dataclass
class AgeDistributionConfig:
    """年龄分布配置"""
    type: str = "uniform"  # normal, uniform, custom
    mean: Optional[float] = None
    std: Optional[float] = None
    min_age: float = 18
    max_age: float = 100
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {k: v for k, v in asdict(self).items() if v is not None}


@dataclass
class GenderDistributionConfig:
    """性别分布配置"""
    male_ratio: float = 0.5
    female_ratio: float = 0.5
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return asdict(self)


@dataclass
class PathwayDistributionConfig:
    """疾病通路分布配置"""
    adenoma_carcinoma_ratio: float = 0.85
    serrated_adenoma_ratio: float = 0.15
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return asdict(self)


@dataclass
class PopulationConfigData:
    """人群配置数据"""
    name: str
    description: str
    size: int
    random_seed: Optional[int] = None
    birth_year_base: int = 2025
    age_distribution: Optional[AgeDistributionConfig] = None
    gender_distribution: Optional[GenderDistributionConfig] = None
    pathway_distribution: Optional[PathwayDistributionConfig] = None
    
    def __post_init__(self):
        """初始化后处理"""
        if self.age_distribution is None:
            self.age_distribution = AgeDistributionConfig()
        if self.gender_distribution is None:
            self.gender_distribution = GenderDistributionConfig()
        if self.pathway_distribution is None:
            self.pathway_distribution = PathwayDistributionConfig()
    
    def to_generation_params(self) -> Dict[str, Any]:
        """转换为生成器参数"""
        return {
            "size": self.size,
            "age_distribution": self.age_distribution.to_dict(),
            "gender_distribution": self.gender_distribution.to_dict(),
            "pathway_distribution": self.pathway_distribution.to_dict(),
            "birth_year_base": self.birth_year_base
        }


class PopulationConfig:
    """
    人群配置管理类
    
    负责加载、验证和管理人群生成配置文件，
    支持YAML和JSON格式。
    """
    
    def __init__(self, config_dir: Optional[Union[str, Path]] = None):
        """
        初始化配置管理器
        
        Args:
            config_dir: 配置文件目录，默认为data/population_configs/
        """
        if config_dir is None:
            config_dir = Path("data/population_configs")
        
        self.config_dir = Path(config_dir)
        self._loaded_configs: Dict[str, PopulationConfigData] = {}
    
    def load_config(self, config_path: Union[str, Path]) -> PopulationConfigData:
        """
        加载配置文件

        Args:
            config_path: 配置文件路径（可以是相对于config_dir的路径）

        Returns:
            加载的配置数据

        Raises:
            ValidationError: 配置文件格式错误或验证失败时
        """
        config_path = Path(config_path)

        # 如果不是绝对路径，则相对于config_dir解析
        if not config_path.is_absolute():
            config_path = self.config_dir / config_path

        if not config_path.exists():
            raise ValidationError(
                f"配置文件不存在: {config_path}",
                "config_path",
                str(config_path)
            )
        
        try:
            # 根据文件扩展名选择解析器
            if config_path.suffix.lower() in ['.yaml', '.yml']:
                with open(config_path, 'r', encoding='utf-8') as f:
                    raw_config = yaml.safe_load(f)
            elif config_path.suffix.lower() == '.json':
                with open(config_path, 'r', encoding='utf-8') as f:
                    raw_config = json.load(f)
            else:
                raise ValidationError(
                    f"不支持的配置文件格式: {config_path.suffix}",
                    "config_path",
                    str(config_path)
                )
        
        except (yaml.YAMLError, json.JSONDecodeError) as e:
            raise ValidationError(
                f"配置文件解析失败: {e}",
                "config_content",
                str(config_path)
            )
        
        # 解析配置数据
        config_data = self._parse_config_data(raw_config, config_path)
        
        # 验证配置
        self._validate_config(config_data)
        
        # 缓存配置
        self._loaded_configs[config_data.name] = config_data
        
        return config_data
    
    def _parse_config_data(
        self, 
        raw_config: Dict[str, Any], 
        config_path: Path
    ) -> PopulationConfigData:
        """解析原始配置数据"""
        try:
            # 获取主配置部分
            if "population_config" in raw_config:
                config = raw_config["population_config"]
            else:
                config = raw_config
            
            # 解析年龄分布配置
            age_dist_config = None
            if "age_distribution" in config:
                age_dist = config["age_distribution"]
                age_dist_config = AgeDistributionConfig(
                    type=age_dist.get("type", "normal"),
                    mean=age_dist.get("mean"),
                    std=age_dist.get("std"),
                    min_age=age_dist.get("min_age", 18),
                    max_age=age_dist.get("max_age", 100)
                )
            
            # 解析性别分布配置
            gender_dist_config = None
            if "gender_distribution" in config:
                gender_dist = config["gender_distribution"]
                gender_dist_config = GenderDistributionConfig(
                    male_ratio=gender_dist.get("male_ratio", 0.5),
                    female_ratio=gender_dist.get("female_ratio", 0.5)
                )
            
            # 解析疾病通路分布配置
            pathway_dist_config = None
            if "pathway_distribution" in config:
                pathway_dist = config["pathway_distribution"]
                pathway_dist_config = PathwayDistributionConfig(
                    adenoma_carcinoma_ratio=pathway_dist.get("adenoma_carcinoma_ratio", 0.85),
                    serrated_adenoma_ratio=pathway_dist.get("serrated_adenoma_ratio", 0.15)
                )
            
            # 创建配置数据对象
            config_data = PopulationConfigData(
                name=config.get("name", config_path.stem),
                description=config.get("description", ""),
                size=config.get("size", 1000),
                random_seed=config.get("random_seed"),
                birth_year_base=config.get("birth_year_base", 2025),
                age_distribution=age_dist_config,
                gender_distribution=gender_dist_config,
                pathway_distribution=pathway_dist_config
            )
            
            return config_data
            
        except (KeyError, TypeError, ValueError) as e:
            raise ValidationError(
                f"配置文件格式错误: {e}",
                "config_structure",
                str(config_path)
            )
    
    def _validate_config(self, config: PopulationConfigData) -> None:
        """验证配置数据"""
        # 验证基本参数
        if not config.name:
            raise ValidationError("配置名称不能为空", "name", config.name)
        
        if config.size <= 0:
            raise ParameterValidationError(
                "人群规模必须大于0",
                "size",
                config.size
            )
        
        if config.size > 10_000_000:
            raise ParameterValidationError(
                "人群规模不能超过1000万",
                "size",
                config.size
            )
        
        # 验证年龄分布
        if config.age_distribution:
            age_dist = config.age_distribution
            
            if age_dist.min_age < 0 or age_dist.max_age > 150:
                raise ValidationError(
                    "年龄范围必须在0-150岁之间",
                    "age_distribution",
                    {"min_age": age_dist.min_age, "max_age": age_dist.max_age}
                )
            
            if age_dist.min_age >= age_dist.max_age:
                raise ValidationError(
                    "最小年龄必须小于最大年龄",
                    "age_distribution",
                    {"min_age": age_dist.min_age, "max_age": age_dist.max_age}
                )
            
            if age_dist.type == "normal":
                if age_dist.mean is None or age_dist.std is None:
                    raise ValidationError(
                        "正态分布需要指定均值和标准差",
                        "age_distribution",
                        {"mean": age_dist.mean, "std": age_dist.std}
                    )
                
                if age_dist.std <= 0:
                    raise ValidationError(
                        "标准差必须大于0",
                        "age_distribution.std",
                        age_dist.std
                    )
        
        # 验证性别分布
        if config.gender_distribution:
            gender_dist = config.gender_distribution
            
            if gender_dist.male_ratio < 0 or gender_dist.male_ratio > 1:
                raise ValidationError(
                    "男性比例必须在0-1之间",
                    "gender_distribution.male_ratio",
                    gender_dist.male_ratio
                )
            
            if gender_dist.female_ratio < 0 or gender_dist.female_ratio > 1:
                raise ValidationError(
                    "女性比例必须在0-1之间",
                    "gender_distribution.female_ratio",
                    gender_dist.female_ratio
                )
            
            total_ratio = gender_dist.male_ratio + gender_dist.female_ratio
            if abs(total_ratio - 1.0) > 1e-6:
                raise ValidationError(
                    "男女比例之和必须等于1.0",
                    "gender_distribution",
                    {"male_ratio": gender_dist.male_ratio, "female_ratio": gender_dist.female_ratio}
                )
        
        # 验证疾病通路分布
        if config.pathway_distribution:
            pathway_dist = config.pathway_distribution
            
            if pathway_dist.adenoma_carcinoma_ratio < 0 or pathway_dist.adenoma_carcinoma_ratio > 1:
                raise ValidationError(
                    "腺瘤-癌变通路比例必须在0-1之间",
                    "pathway_distribution.adenoma_carcinoma_ratio",
                    pathway_dist.adenoma_carcinoma_ratio
                )
            
            if pathway_dist.serrated_adenoma_ratio < 0 or pathway_dist.serrated_adenoma_ratio > 1:
                raise ValidationError(
                    "锯齿状腺瘤通路比例必须在0-1之间",
                    "pathway_distribution.serrated_adenoma_ratio",
                    pathway_dist.serrated_adenoma_ratio
                )
            
            total_ratio = pathway_dist.adenoma_carcinoma_ratio + pathway_dist.serrated_adenoma_ratio
            if abs(total_ratio - 1.0) > 1e-6:
                raise ValidationError(
                    "疾病通路比例之和必须等于1.0",
                    "pathway_distribution",
                    {"adenoma_carcinoma_ratio": pathway_dist.adenoma_carcinoma_ratio, 
                     "serrated_adenoma_ratio": pathway_dist.serrated_adenoma_ratio}
                )
    
    def save_config(
        self, 
        config: PopulationConfigData, 
        file_path: Union[str, Path],
        format: str = "yaml"
    ) -> None:
        """
        保存配置到文件
        
        Args:
            config: 配置数据
            file_path: 保存路径
            format: 文件格式，'yaml' 或 'json'
        """
        file_path = Path(file_path)
        file_path.parent.mkdir(parents=True, exist_ok=True)
        
        # 构建配置字典
        config_dict = {
            "population_config": {
                "name": config.name,
                "description": config.description,
                "size": config.size,
                "random_seed": config.random_seed,
                "birth_year_base": config.birth_year_base,
                "age_distribution": config.age_distribution.to_dict() if config.age_distribution else None,
                "gender_distribution": config.gender_distribution.to_dict() if config.gender_distribution else None,
                "pathway_distribution": config.pathway_distribution.to_dict() if config.pathway_distribution else None
            }
        }
        
        # 移除None值
        config_dict["population_config"] = {
            k: v for k, v in config_dict["population_config"].items() 
            if v is not None
        }
        
        # 保存文件
        if format.lower() == "yaml":
            with open(file_path, 'w', encoding='utf-8') as f:
                yaml.dump(config_dict, f, default_flow_style=False, allow_unicode=True)
        elif format.lower() == "json":
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(config_dict, f, indent=2, ensure_ascii=False)
        else:
            raise ValidationError(
                f"不支持的保存格式: {format}",
                "format",
                format
            )
    
    def get_loaded_configs(self) -> Dict[str, PopulationConfigData]:
        """获取已加载的配置"""
        return self._loaded_configs.copy()
    
    def create_default_config(
        self,
        name: str = "默认中国成人筛查人群",
        description: str = "50-75岁中国成人结直肠癌筛查目标人群"
    ) -> PopulationConfigData:
        """
        创建默认配置
        
        Args:
            name: 配置名称
            description: 配置描述
            
        Returns:
            默认配置数据
        """
        return PopulationConfigData(
            name=name,
            description=description,
            size=100000,
            random_seed=42,
            birth_year_base=2025,
            age_distribution=AgeDistributionConfig(
                type="normal",
                mean=62.5,
                std=7.2,
                min_age=50,
                max_age=75
            ),
            gender_distribution=GenderDistributionConfig(
                male_ratio=0.52,
                female_ratio=0.48
            ),
            pathway_distribution=PathwayDistributionConfig(
                adenoma_carcinoma_ratio=0.85,
                serrated_adenoma_ratio=0.15
            )
        )
