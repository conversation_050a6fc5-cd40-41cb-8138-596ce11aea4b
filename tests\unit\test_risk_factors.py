"""
风险因素管理系统单元测试

测试风险因素枚举、数据结构和档案管理功能。
"""

import pytest
import json
from datetime import datetime
from pathlib import Path
from tempfile import TemporaryDirectory

from src.modules.disease.risk_factors import (
    RiskFactorType,
    RiskFactorCategory,
    RiskFactor,
    RiskFactorProfile
)


class TestRiskFactorType:
    """测试风险因素类型枚举"""
    
    def test_boolean_factors(self):
        """测试布尔型风险因素识别"""
        boolean_factors = RiskFactorType.get_boolean_factors()
        expected = {
            RiskFactorType.FAMILY_HISTORY,
            RiskFactorType.IBD,
            RiskFactorType.DIABETES,
            RiskFactorType.SMOKING
        }
        assert boolean_factors == expected
    
    def test_continuous_factors(self):
        """测试连续型风险因素识别"""
        continuous_factors = RiskFactorType.get_continuous_factors()
        expected = {
            RiskFactorType.BMI,
            RiskFactorType.SEDENTARY_LIFESTYLE,
            RiskFactorType.ALCOHOL_CONSUMPTION,
            RiskFactorType.DIET_QUALITY
        }
        assert continuous_factors == expected
    
    def test_is_boolean(self):
        """测试布尔型判断方法"""
        assert RiskFactorType.FAMILY_HISTORY.is_boolean()
        assert RiskFactorType.IBD.is_boolean()
        assert not RiskFactorType.BMI.is_boolean()
        assert not RiskFactorType.SEDENTARY_LIFESTYLE.is_boolean()
    
    def test_is_continuous(self):
        """测试连续型判断方法"""
        assert RiskFactorType.BMI.is_continuous()
        assert RiskFactorType.SEDENTARY_LIFESTYLE.is_continuous()
        assert not RiskFactorType.FAMILY_HISTORY.is_continuous()
        assert not RiskFactorType.IBD.is_continuous()


class TestRiskFactorCategory:
    """测试风险因素分类"""
    
    def test_get_category_for_factor(self):
        """测试获取风险因素分类"""
        assert RiskFactorCategory.get_category_for_factor(
            RiskFactorType.FAMILY_HISTORY
        ) == RiskFactorCategory.GENETIC
        
        assert RiskFactorCategory.get_category_for_factor(
            RiskFactorType.IBD
        ) == RiskFactorCategory.DISEASE_RELATED
        
        assert RiskFactorCategory.get_category_for_factor(
            RiskFactorType.BMI
        ) == RiskFactorCategory.LIFESTYLE


class TestRiskFactor:
    """测试风险因素数据类"""
    
    def test_create_boolean_risk_factor(self):
        """测试创建布尔型风险因素"""
        factor = RiskFactor(
            factor_type=RiskFactorType.FAMILY_HISTORY,
            value=True,
            weight=2.5
        )
        
        assert factor.factor_type == RiskFactorType.FAMILY_HISTORY
        assert factor.value is True
        assert factor.weight == 2.5
        assert factor.category == RiskFactorCategory.GENETIC
    
    def test_create_continuous_risk_factor(self):
        """测试创建连续型风险因素"""
        factor = RiskFactor(
            factor_type=RiskFactorType.BMI,
            value=25.5,
            weight=1.2
        )
        
        assert factor.factor_type == RiskFactorType.BMI
        assert factor.value == 25.5
        assert factor.weight == 1.2
        assert factor.category == RiskFactorCategory.LIFESTYLE
    
    def test_boolean_value_validation(self):
        """测试布尔型值验证"""
        # 正确的布尔值
        factor = RiskFactor(
            factor_type=RiskFactorType.FAMILY_HISTORY,
            value=True
        )
        assert factor.value is True
        
        # 错误的值类型
        with pytest.raises(ValueError, match="布尔型风险因素.*的值必须是布尔类型"):
            RiskFactor(
                factor_type=RiskFactorType.FAMILY_HISTORY,
                value="yes"
            )
    
    def test_continuous_value_validation(self):
        """测试连续型值验证"""
        # 正确的数值
        factor = RiskFactor(
            factor_type=RiskFactorType.BMI,
            value=25.0
        )
        assert factor.value == 25.0
        
        # 错误的值类型
        with pytest.raises(ValueError, match="连续型风险因素.*的值必须是数值类型"):
            RiskFactor(
                factor_type=RiskFactorType.BMI,
                value="25"
            )
    
    def test_bmi_range_validation(self):
        """测试BMI范围验证"""
        # 正常范围
        factor = RiskFactor(
            factor_type=RiskFactorType.BMI,
            value=25.0
        )
        assert factor.value == 25.0
        
        # 超出范围
        with pytest.raises(ValueError, match="BMI值必须在10.0-60.0范围内"):
            RiskFactor(
                factor_type=RiskFactorType.BMI,
                value=70.0
            )
        
        with pytest.raises(ValueError, match="BMI值必须在10.0-60.0范围内"):
            RiskFactor(
                factor_type=RiskFactorType.BMI,
                value=5.0
            )
    
    def test_sedentary_lifestyle_range_validation(self):
        """测试久坐时间范围验证"""
        # 正常范围
        factor = RiskFactor(
            factor_type=RiskFactorType.SEDENTARY_LIFESTYLE,
            value=8.0
        )
        assert factor.value == 8.0
        
        # 超出范围
        with pytest.raises(ValueError, match="久坐时间必须在0-24小时范围内"):
            RiskFactor(
                factor_type=RiskFactorType.SEDENTARY_LIFESTYLE,
                value=25.0
            )
    
    def test_weight_validation(self):
        """测试权重验证"""
        # 正常权重
        factor = RiskFactor(
            factor_type=RiskFactorType.FAMILY_HISTORY,
            value=True,
            weight=2.5
        )
        assert factor.weight == 2.5
        
        # 负权重
        with pytest.raises(ValueError, match="权重不能为负数"):
            RiskFactor(
                factor_type=RiskFactorType.FAMILY_HISTORY,
                value=True,
                weight=-1.0
            )
    
    def test_update_value(self):
        """测试更新风险因素值"""
        factor = RiskFactor(
            factor_type=RiskFactorType.BMI,
            value=25.0
        )
        
        old_updated = factor.last_updated
        factor.update_value(27.5, "manual_update")
        
        assert factor.value == 27.5
        assert factor.source == "manual_update"
        assert factor.last_updated > old_updated
    
    def test_update_value_validation_failure(self):
        """测试更新值验证失败时的回滚"""
        factor = RiskFactor(
            factor_type=RiskFactorType.BMI,
            value=25.0
        )
        
        original_value = factor.value
        
        with pytest.raises(ValueError):
            factor.update_value(70.0)  # 超出范围
        
        # 值应该回滚到原值
        assert factor.value == original_value
    
    def test_to_dict(self):
        """测试转换为字典"""
        factor = RiskFactor(
            factor_type=RiskFactorType.FAMILY_HISTORY,
            value=True,
            weight=2.5,
            source="test",
            confidence_interval=(2.1, 3.0),
            reference="Test et al. 2023"
        )
        
        data = factor.to_dict()
        
        assert data["factor_type"] == "family_history"
        assert data["value"] is True
        assert data["weight"] == 2.5
        assert data["source"] == "test"
        assert data["confidence_interval"] == (2.1, 3.0)
        assert data["reference"] == "Test et al. 2023"
        assert data["category"] == "genetic"
    
    def test_from_dict(self):
        """测试从字典创建"""
        data = {
            "factor_type": "family_history",
            "value": True,
            "weight": 2.5,
            "last_updated": "2023-01-01T12:00:00",
            "source": "test",
            "confidence_interval": [2.1, 3.0],
            "reference": "Test et al. 2023"
        }
        
        factor = RiskFactor.from_dict(data)
        
        assert factor.factor_type == RiskFactorType.FAMILY_HISTORY
        assert factor.value is True
        assert factor.weight == 2.5
        assert factor.source == "test"
        assert factor.confidence_interval == [2.1, 3.0]
        assert factor.reference == "Test et al. 2023"


class TestRiskFactorProfile:
    """测试风险因素档案管理"""
    
    def test_create_profile(self):
        """测试创建档案"""
        profile = RiskFactorProfile("test-individual-123")
        
        assert profile.individual_id == "test-individual-123"
        assert len(profile.risk_factors) == 0
        assert isinstance(profile.created_at, datetime)
        assert isinstance(profile.updated_at, datetime)
    
    def test_add_risk_factor(self):
        """测试添加风险因素"""
        profile = RiskFactorProfile("test-individual-123")
        
        factor = RiskFactor(
            factor_type=RiskFactorType.FAMILY_HISTORY,
            value=True,
            weight=2.5
        )
        
        success = profile.add_risk_factor(factor)
        
        assert success is True
        assert len(profile.risk_factors) == 1
        assert RiskFactorType.FAMILY_HISTORY in profile.risk_factors
    
    def test_remove_risk_factor(self):
        """测试移除风险因素"""
        profile = RiskFactorProfile("test-individual-123")
        
        factor = RiskFactor(
            factor_type=RiskFactorType.FAMILY_HISTORY,
            value=True
        )
        profile.add_risk_factor(factor)
        
        success = profile.remove_risk_factor(RiskFactorType.FAMILY_HISTORY)
        
        assert success is True
        assert len(profile.risk_factors) == 0
        assert RiskFactorType.FAMILY_HISTORY not in profile.risk_factors
    
    def test_get_risk_factor(self):
        """测试获取风险因素"""
        profile = RiskFactorProfile("test-individual-123")
        
        factor = RiskFactor(
            factor_type=RiskFactorType.BMI,
            value=25.0
        )
        profile.add_risk_factor(factor)
        
        retrieved_factor = profile.get_risk_factor(RiskFactorType.BMI)
        
        assert retrieved_factor is not None
        assert retrieved_factor.factor_type == RiskFactorType.BMI
        assert retrieved_factor.value == 25.0
    
    def test_has_risk_factor(self):
        """测试检查风险因素存在"""
        profile = RiskFactorProfile("test-individual-123")
        
        factor = RiskFactor(
            factor_type=RiskFactorType.DIABETES,
            value=True
        )
        profile.add_risk_factor(factor)
        
        assert profile.has_risk_factor(RiskFactorType.DIABETES) is True
        assert profile.has_risk_factor(RiskFactorType.FAMILY_HISTORY) is False
    
    def test_get_factors_by_category(self):
        """测试按分类获取风险因素"""
        profile = RiskFactorProfile("test-individual-123")
        
        # 添加不同分类的风险因素
        genetic_factor = RiskFactor(
            factor_type=RiskFactorType.FAMILY_HISTORY,
            value=True
        )
        lifestyle_factor = RiskFactor(
            factor_type=RiskFactorType.BMI,
            value=25.0
        )
        disease_factor = RiskFactor(
            factor_type=RiskFactorType.IBD,
            value=True
        )
        
        profile.add_risk_factor(genetic_factor)
        profile.add_risk_factor(lifestyle_factor)
        profile.add_risk_factor(disease_factor)
        
        genetic_factors = profile.get_factors_by_category(RiskFactorCategory.GENETIC)
        lifestyle_factors = profile.get_factors_by_category(RiskFactorCategory.LIFESTYLE)
        disease_factors = profile.get_factors_by_category(RiskFactorCategory.DISEASE_RELATED)
        
        assert len(genetic_factors) == 1
        assert len(lifestyle_factors) == 1
        assert len(disease_factors) == 1
        
        assert genetic_factors[0].factor_type == RiskFactorType.FAMILY_HISTORY
        assert lifestyle_factors[0].factor_type == RiskFactorType.BMI
        assert disease_factors[0].factor_type == RiskFactorType.IBD
    
    def test_get_boolean_factors(self):
        """测试获取布尔型风险因素"""
        profile = RiskFactorProfile("test-individual-123")
        
        profile.add_risk_factor(RiskFactor(
            factor_type=RiskFactorType.FAMILY_HISTORY,
            value=True
        ))
        profile.add_risk_factor(RiskFactor(
            factor_type=RiskFactorType.DIABETES,
            value=False
        ))
        profile.add_risk_factor(RiskFactor(
            factor_type=RiskFactorType.BMI,
            value=25.0
        ))
        
        boolean_factors = profile.get_boolean_factors()
        
        assert len(boolean_factors) == 2
        assert boolean_factors[RiskFactorType.FAMILY_HISTORY] is True
        assert boolean_factors[RiskFactorType.DIABETES] is False
        assert RiskFactorType.BMI not in boolean_factors
    
    def test_get_continuous_factors(self):
        """测试获取连续型风险因素"""
        profile = RiskFactorProfile("test-individual-123")
        
        profile.add_risk_factor(RiskFactor(
            factor_type=RiskFactorType.BMI,
            value=25.0
        ))
        profile.add_risk_factor(RiskFactor(
            factor_type=RiskFactorType.SEDENTARY_LIFESTYLE,
            value=8.5
        ))
        profile.add_risk_factor(RiskFactor(
            factor_type=RiskFactorType.FAMILY_HISTORY,
            value=True
        ))
        
        continuous_factors = profile.get_continuous_factors()
        
        assert len(continuous_factors) == 2
        assert continuous_factors[RiskFactorType.BMI] == 25.0
        assert continuous_factors[RiskFactorType.SEDENTARY_LIFESTYLE] == 8.5
        assert RiskFactorType.FAMILY_HISTORY not in continuous_factors
    
    def test_update_risk_factor(self):
        """测试更新风险因素"""
        profile = RiskFactorProfile("test-individual-123")
        
        factor = RiskFactor(
            factor_type=RiskFactorType.BMI,
            value=25.0
        )
        profile.add_risk_factor(factor)
        
        success = profile.update_risk_factor(
            RiskFactorType.BMI,
            27.5,
            "manual_update"
        )
        
        assert success is True
        updated_factor = profile.get_risk_factor(RiskFactorType.BMI)
        assert updated_factor.value == 27.5
        assert updated_factor.source == "manual_update"
    
    def test_get_summary(self):
        """测试获取档案摘要"""
        profile = RiskFactorProfile("test-individual-123")
        
        # 添加不同类型的风险因素
        profile.add_risk_factor(RiskFactor(
            factor_type=RiskFactorType.FAMILY_HISTORY,
            value=True
        ))
        profile.add_risk_factor(RiskFactor(
            factor_type=RiskFactorType.BMI,
            value=25.0
        ))
        profile.add_risk_factor(RiskFactor(
            factor_type=RiskFactorType.IBD,
            value=True
        ))
        
        summary = profile.get_summary()
        
        assert summary["individual_id"] == "test-individual-123"
        assert summary["total_factors"] == 3
        assert summary["boolean_factors"] == 2
        assert summary["continuous_factors"] == 1
        assert summary["factors_by_category"]["genetic"] == 1
        assert summary["factors_by_category"]["lifestyle"] == 1
        assert summary["factors_by_category"]["disease_related"] == 1
    
    def test_to_dict_and_serialization(self):
        """测试字典转换和序列化"""
        profile = RiskFactorProfile("test-individual-123")
        
        factor = RiskFactor(
            factor_type=RiskFactorType.FAMILY_HISTORY,
            value=True,
            weight=2.5
        )
        profile.add_risk_factor(factor)
        
        data = profile.to_dict()
        
        assert data["individual_id"] == "test-individual-123"
        assert "risk_factors" in data
        assert "family_history" in data["risk_factors"]
        assert data["risk_factors"]["family_history"]["value"] is True
    
    def test_file_operations(self):
        """测试文件保存和加载"""
        with TemporaryDirectory() as temp_dir:
            file_path = Path(temp_dir) / "test_profile.json"
            
            # 创建档案
            original_profile = RiskFactorProfile("test-individual-123")
            original_profile.add_risk_factor(RiskFactor(
                factor_type=RiskFactorType.FAMILY_HISTORY,
                value=True,
                weight=2.5
            ))
            original_profile.add_risk_factor(RiskFactor(
                factor_type=RiskFactorType.BMI,
                value=25.0,
                weight=1.2
            ))
            
            # 保存到文件
            success = original_profile.save_to_file(file_path)
            assert success is True
            assert file_path.exists()
            
            # 从文件加载
            loaded_profile = RiskFactorProfile.load_from_file(file_path)
            assert loaded_profile is not None
            assert loaded_profile.individual_id == "test-individual-123"
            assert len(loaded_profile.risk_factors) == 2
            
            # 验证风险因素
            family_history = loaded_profile.get_risk_factor(RiskFactorType.FAMILY_HISTORY)
            assert family_history is not None
            assert family_history.value is True
            assert family_history.weight == 2.5
            
            bmi = loaded_profile.get_risk_factor(RiskFactorType.BMI)
            assert bmi is not None
            assert bmi.value == 25.0
            assert bmi.weight == 1.2
    
    def test_magic_methods(self):
        """测试魔术方法"""
        profile = RiskFactorProfile("test-individual-123")
        
        factor = RiskFactor(
            factor_type=RiskFactorType.FAMILY_HISTORY,
            value=True
        )
        profile.add_risk_factor(factor)
        
        # __len__
        assert len(profile) == 1
        
        # __contains__
        assert RiskFactorType.FAMILY_HISTORY in profile
        assert RiskFactorType.BMI not in profile
        
        # __repr__
        repr_str = repr(profile)
        assert "RiskFactorProfile" in repr_str
        assert "test-individual-123" in repr_str
        assert "factors=1" in repr_str
