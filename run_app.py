#!/usr/bin/env python3
"""
应用程序启动脚本

用于开发和测试桌面应用程序
"""

import sys
import os
from pathlib import Path

# 添加src目录到Python路径
project_root = Path(__file__).parent
src_path = project_root / "src"
sys.path.insert(0, str(src_path))

def main():
    """主函数"""
    try:
        # 导入并运行应用程序
        from interfaces.desktop.main import main as app_main
        return app_main()
        
    except ImportError as e:
        print(f"导入错误: {e}")
        print("请确保已安装所有依赖包:")
        print("pip install -r requirements.txt")
        return 1
        
    except Exception as e:
        print(f"应用程序启动失败: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
