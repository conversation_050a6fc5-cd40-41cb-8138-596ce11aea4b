# 风险因素管理系统 API 参考

## 概述

本文档提供风险因素管理系统的完整API参考，包括所有类、方法和函数的详细说明。

## 核心模块

### src.modules.disease.risk_factors

#### RiskFactorType (枚举)

风险因素类型枚举，定义所有支持的风险因素。

```python
class RiskFactorType(Enum):
    # 遗传因素
    FAMILY_HISTORY = "family_history"
    
    # 疾病相关因素
    IBD = "inflammatory_bowel_disease"
    DIABETES = "diabetes_mellitus"
    
    # 生活方式因素
    BMI = "body_mass_index"
    SMOKING = "smoking_status"
    ALCOHOL_CONSUMPTION = "alcohol_consumption"
    SEDENTARY_LIFESTYLE = "sedentary_lifestyle"
    DIET_QUALITY = "diet_quality"
    PHYSICAL_ACTIVITY = "physical_activity"
```

**类方法**:
- `get_genetic_factors() -> Set[RiskFactorType]`: 获取遗传相关风险因素
- `get_disease_factors() -> Set[RiskFactorType]`: 获取疾病相关风险因素
- `get_lifestyle_factors() -> Set[RiskFactorType]`: 获取生活方式相关风险因素
- `get_boolean_factors() -> Set[RiskFactorType]`: 获取布尔型风险因素
- `get_continuous_factors() -> Set[RiskFactorType]`: 获取连续值风险因素

**实例方法**:
- `is_genetic() -> bool`: 判断是否为遗传因素
- `is_disease_related() -> bool`: 判断是否为疾病相关因素
- `is_lifestyle() -> bool`: 判断是否为生活方式因素
- `is_boolean() -> bool`: 判断是否为布尔型因素
- `is_continuous() -> bool`: 判断是否为连续值因素

#### RiskLevel (枚举)

风险等级枚举。

```python
class RiskLevel(Enum):
    LOW = "low"
    MODERATE = "moderate"
    HIGH = "high"
    VERY_HIGH = "very_high"
```

#### RiskFactor (数据类)

表示单个风险因素的数据类。

```python
@dataclass
class RiskFactor:
    factor_type: RiskFactorType
    value: Union[bool, float, int, str]
    weight: float = 1.0
    last_updated: datetime = field(default_factory=datetime.now)
    source: str = "default"
    confidence: float = 1.0
```

**方法**:
- `update_value(new_value, source="update") -> None`: 更新风险因素值
- `to_dict() -> Dict[str, Any]`: 转换为字典格式
- `from_dict(data: Dict[str, Any]) -> RiskFactor`: 从字典创建实例

#### RiskFactorProfile (数据类)

管理个体的风险因素集合。

```python
@dataclass
class RiskFactorProfile:
    individual_id: str
    factors: Dict[RiskFactorType, RiskFactor] = field(default_factory=dict)
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)
```

**方法**:
- `add_factor(risk_factor: RiskFactor) -> None`: 添加风险因素
- `remove_factor(factor_type: RiskFactorType) -> None`: 移除风险因素
- `get_factor(factor_type: RiskFactorType) -> Optional[RiskFactor]`: 获取风险因素
- `has_factor(factor_type: RiskFactorType) -> bool`: 检查是否存在风险因素
- `get_factor_value(factor_type: RiskFactorType, default=None) -> Any`: 获取风险因素值
- `update_factor_value(factor_type: RiskFactorType, value: Any, source="update") -> None`: 更新风险因素值
- `get_factors_by_category(category: str) -> Dict[RiskFactorType, RiskFactor]`: 按类别获取风险因素
- `to_dict() -> Dict[str, Any]`: 转换为字典格式
- `from_dict(data: Dict[str, Any]) -> RiskFactorProfile`: 从字典创建实例

### src.modules.disease.risk_weights

#### RiskFactorWeights (类)

风险因素权重管理器。

```python
class RiskFactorWeights:
    def __init__(self, config_path: Optional[Union[str, Path]] = None)
```

**方法**:
- `get_weight(factor_type: RiskFactorType) -> float`: 获取基础权重
- `get_weight_config(factor_type: RiskFactorType) -> Optional[RiskFactorWeight]`: 获取完整权重配置
- `calculate_factor_weight(factor_type: RiskFactorType, value: Any, age: Optional[int] = None) -> float`: 计算特定值的权重
- `get_age_adjustment_factor(age: int) -> float`: 获取年龄调整因子
- `get_gender_adjustment_factor(gender: str) -> float`: 获取性别调整因子
- `classify_risk_level(risk_score: float) -> RiskLevel`: 分类风险等级

### src.modules.disease.risk_calculator

#### RiskScore (数据类)

风险评分结果。

```python
@dataclass
class RiskScore:
    individual_id: str
    total_score: float
    risk_level: RiskLevel
    component_scores: Dict[RiskFactorType, float] = field(default_factory=dict)
    age_adjustment: float = 1.0
    gender_adjustment: float = 1.0
    calculation_date: datetime = field(default_factory=datetime.now)
    algorithm_version: str = "1.0"
```

**方法**:
- `to_dict() -> Dict[str, Any]`: 转换为字典格式

#### RiskTrend (数据类)

风险趋势分析。

```python
@dataclass
class RiskTrend:
    individual_id: str
    scores: List[RiskScore] = field(default_factory=list)
    trend_direction: str = "stable"
    trend_magnitude: float = 0.0
```

**方法**:
- `add_score(score: RiskScore) -> None`: 添加新的风险评分

#### RiskCalculator (类)

风险评分计算器。

```python
class RiskCalculator:
    def __init__(self, weights: Optional[RiskFactorWeights] = None)
```

**方法**:
- `calculate_risk_score(individual_id: str, risk_profile: RiskFactorProfile, age: int, gender: str, algorithm: str = "multiplicative") -> RiskScore`: 计算综合风险评分
- `get_risk_trend(individual_id: str) -> Optional[RiskTrend]`: 获取风险趋势
- `compare_risk_profiles(profile1: RiskFactorProfile, profile2: RiskFactorProfile, age: int, gender: str) -> Dict[str, Any]`: 比较风险配置文件
- `identify_high_impact_factors(risk_profile: RiskFactorProfile, age: int, gender: str) -> List[Tuple[RiskFactorType, float]]`: 识别高影响风险因素
- `simulate_risk_reduction(risk_profile: RiskFactorProfile, age: int, gender: str, interventions: Dict[RiskFactorType, Any]) -> Dict[str, Any]`: 模拟风险干预效果
- `get_risk_summary(individual_id: str) -> Optional[Dict[str, Any]]`: 获取风险摘要

## 验证模块

### src.utils.validators

#### 异常类

```python
class RiskFactorValidationError(ValidationError):
    """风险因素验证错误"""
```

#### 验证函数

**基础验证**:
- `validate_bmi(bmi: Union[int, float], field_name: str = "bmi") -> float`: 验证BMI值
- `validate_alcohol_consumption(alcohol: Union[int, float], field_name: str = "alcohol_consumption") -> float`: 验证酒精消费量
- `validate_sedentary_hours(hours: Union[int, float], field_name: str = "sedentary_lifestyle") -> float`: 验证久坐时间
- `validate_diet_quality_score(score: Union[int, float], field_name: str = "diet_quality") -> float`: 验证饮食质量评分
- `validate_physical_activity_hours(hours: Union[int, float], field_name: str = "physical_activity") -> float`: 验证体力活动时间

**高级验证**:
- `validate_risk_factor_value(factor_type, value, field_name: Optional[str] = None)`: 根据类型验证风险因素值
- `validate_risk_factor_profile(risk_profile, age: int, gender: str) -> List[str]`: 验证配置文件完整性
- `validate_risk_factor_data_completeness(risk_profile) -> Dict[str, Any]`: 检查数据完整性

## Individual类扩展

### 风险因素相关方法

Individual类已扩展以支持风险因素管理：

```python
class Individual:
    # 属性
    @property
    def risk_factor_profile(self) -> RiskFactorProfile
    
    # 方法
    def set_risk_factor_profile(self, profile: RiskFactorProfile) -> None
    def add_risk_factor(self, risk_factor: RiskFactor) -> None
    def remove_risk_factor(self, factor_type: RiskFactorType) -> None
    def get_risk_factor(self, factor_type: RiskFactorType) -> Optional[RiskFactor]
    def has_risk_factor(self, factor_type: RiskFactorType) -> bool
    def get_risk_factor_value(self, factor_type: RiskFactorType, default=None) -> Any
    def update_risk_factor_value(self, factor_type: RiskFactorType, value: Any, source: str = "update") -> None
    def get_risk_factors_by_category(self, category: str) -> Dict[RiskFactorType, RiskFactor]
```

## 使用示例

### 基本使用

```python
from src.modules.disease import RiskFactorType, RiskFactor, RiskFactorProfile
from src.modules.disease import RiskCalculator
from src.core import Individual, Gender

# 创建个体
individual = Individual(birth_year=1970, gender=Gender.MALE)

# 添加风险因素
individual.add_risk_factor(RiskFactor(
    factor_type=RiskFactorType.FAMILY_HISTORY,
    value=True
))

# 计算风险评分
calculator = RiskCalculator()
risk_score = calculator.calculate_risk_score(
    individual_id=individual.individual_id,
    risk_profile=individual.risk_factor_profile,
    age=individual.get_current_age(),
    gender=individual.gender.value
)
```

### 高级使用

```python
# 自定义权重配置
from src.modules.disease import RiskFactorWeights

weights = RiskFactorWeights("custom_weights.yaml")
calculator = RiskCalculator(weights=weights)

# 风险干预模拟
interventions = {
    RiskFactorType.BMI: 24.0,
    RiskFactorType.PHYSICAL_ACTIVITY: 5.0
}

result = calculator.simulate_risk_reduction(
    risk_profile=individual.risk_factor_profile,
    age=54,
    gender="male",
    interventions=interventions
)
```

## 错误处理

所有函数都可能抛出以下异常：

- `RiskFactorValidationError`: 风险因素验证错误
- `ValidationError`: 一般验证错误
- `ValueError`: 参数值错误
- `FileNotFoundError`: 配置文件不存在
- `KeyError`: 缺少必需的配置项

建议使用try-except块处理这些异常：

```python
try:
    risk_score = calculator.calculate_risk_score(...)
except RiskFactorValidationError as e:
    print(f"风险因素验证错误: {e}")
except Exception as e:
    print(f"其他错误: {e}")
```

## 配置文件格式

### YAML权重配置文件

```yaml
metadata:
  version: "1.0"
  description: "风险因素权重配置"

risk_factor_weights:
  family_history:
    weight: 2.5
    confidence: 0.95
    description: "一级亲属结直肠癌家族史"
  
  body_mass_index:
    weights:
      normal: 1.0
      overweight: 1.2
      obese_1: 1.5
```

### JSON配置文件导出

```json
{
  "individual_id": "patient_001",
  "factors": {
    "family_history": {
      "factor_type": "family_history",
      "value": true,
      "weight": 1.0,
      "last_updated": "2024-01-01T00:00:00",
      "source": "patient_report",
      "confidence": 1.0
    }
  }
}
```

## 版本兼容性

- **当前版本**: 1.0
- **Python要求**: ≥3.8
- **依赖包**: pyyaml, dataclasses (Python 3.7需要)

## 性能考虑

- 权重配置加载：一次性加载，建议缓存
- 风险评分计算：O(n)复杂度，n为风险因素数量
- 历史数据：定期清理以节省内存
- 批量处理：支持多个体并行计算
