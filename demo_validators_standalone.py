#!/usr/bin/env python3
"""
独立验证器演示

演示表单验证器功能，不依赖PyQt6
"""

import sys
from pathlib import Path
from typing import Any, Optional, Union, Callable, List
from dataclasses import dataclass
from abc import ABC, abstractmethod
import re

# 复制验证器代码（独立版本）
@dataclass
class ValidationResult:
    """验证结果数据类"""
    is_valid: bool
    error_message: str = ""
    warning_message: str = ""
    
    @classmethod
    def success(cls) -> "ValidationResult":
        """创建成功的验证结果"""
        return cls(is_valid=True)
    
    @classmethod
    def error(cls, message: str) -> "ValidationResult":
        """创建错误的验证结果"""
        return cls(is_valid=False, error_message=message)
    
    @classmethod
    def warning(cls, message: str) -> "ValidationResult":
        """创建警告的验证结果"""
        return cls(is_valid=True, warning_message=message)


class ValidationError(Exception):
    """验证错误异常"""
    def __init__(self, message: str, field_name: str = ""):
        super().__init__(message)
        self.field_name = field_name


class InputValidator(ABC):
    """输入验证器基类"""
    
    def __init__(self, field_name: str = "", error_message: str = ""):
        self.field_name = field_name
        self.error_message = error_message
    
    @abstractmethod
    def validate(self, value: Any) -> ValidationResult:
        """验证输入值"""
        pass
    
    def __call__(self, value: Any) -> ValidationResult:
        """使验证器可调用"""
        return self.validate(value)


class RequiredFieldValidator(InputValidator):
    """必填字段验证器"""
    
    def __init__(self, field_name: str = "", error_message: str = ""):
        super().__init__(field_name, error_message or "此字段为必填项")
    
    def validate(self, value: Any) -> ValidationResult:
        """验证必填字段"""
        if value is None:
            return ValidationResult.error(self.error_message)
        
        if isinstance(value, str) and not value.strip():
            return ValidationResult.error(self.error_message)
        
        if isinstance(value, (list, dict, tuple)) and len(value) == 0:
            return ValidationResult.error(self.error_message)
        
        return ValidationResult.success()


class NumericRangeValidator(InputValidator):
    """数字范围验证器"""
    
    def __init__(
        self,
        min_value: Optional[Union[int, float]] = None,
        max_value: Optional[Union[int, float]] = None,
        field_name: str = "",
        error_message: str = ""
    ):
        self.min_value = min_value
        self.max_value = max_value
        
        # 生成默认错误消息
        if not error_message:
            if min_value is not None and max_value is not None:
                error_message = f"值必须在 {min_value} 到 {max_value} 之间"
            elif min_value is not None:
                error_message = f"值必须大于等于 {min_value}"
            elif max_value is not None:
                error_message = f"值必须小于等于 {max_value}"
            else:
                error_message = "数值无效"
        
        super().__init__(field_name, error_message)
    
    def validate(self, value: Any) -> ValidationResult:
        """验证数字范围"""
        try:
            # 尝试转换为数字
            if isinstance(value, str):
                if not value.strip():
                    return ValidationResult.error("请输入数值")
                numeric_value = float(value)
            elif isinstance(value, (int, float)):
                numeric_value = float(value)
            else:
                return ValidationResult.error("输入值必须是数字")
            
            # 检查范围
            if self.min_value is not None and numeric_value < self.min_value:
                return ValidationResult.error(self.error_message)
            
            if self.max_value is not None and numeric_value > self.max_value:
                return ValidationResult.error(self.error_message)
            
            # 生成警告（如果接近边界）
            warning_message = ""
            if self.min_value is not None and self.max_value is not None:
                range_size = self.max_value - self.min_value
                if numeric_value <= self.min_value + range_size * 0.1:
                    warning_message = f"值接近下限 ({self.min_value})"
                elif numeric_value >= self.max_value - range_size * 0.1:
                    warning_message = f"值接近上限 ({self.max_value})"
            
            if warning_message:
                return ValidationResult.warning(warning_message)
            
            return ValidationResult.success()
            
        except (ValueError, TypeError):
            return ValidationResult.error("请输入有效的数字")


class PercentageValidator(NumericRangeValidator):
    """百分比验证器"""
    
    def __init__(self, field_name: str = "", error_message: str = ""):
        super().__init__(
            min_value=0.0,
            max_value=100.0,
            field_name=field_name,
            error_message=error_message or "百分比必须在 0% 到 100% 之间"
        )


def demo_validators():
    """演示验证器功能"""
    print("=== 表单验证器功能演示 ===\n")
    
    # 1. 数字范围验证器演示
    print("1. 数字范围验证器 (年龄: 18-100)")
    age_validator = NumericRangeValidator(18, 100, "age", "年龄必须在18-100岁之间")
    
    test_ages = [25, 150, "abc", "", 65.5, 17, 101, 19, 99]
    for age in test_ages:
        result = age_validator.validate(age)
        status = "✅" if result.is_valid else "❌"
        message = result.error_message or result.warning_message or "有效"
        print(f"  输入: {repr(age):>8} -> {status} {message}")
    
    # 2. 百分比验证器演示
    print("\n2. 百分比验证器 (0-100%)")
    percent_validator = PercentageValidator()
    
    test_percentages = [50, 120, -10, 85.5, 0, 100, 5, 95]
    for percent in test_percentages:
        result = percent_validator.validate(percent)
        status = "✅" if result.is_valid else "❌"
        message = result.error_message or result.warning_message or "有效"
        print(f"  输入: {percent:>8}% -> {status} {message}")
    
    # 3. 必填字段验证器演示
    print("\n3. 必填字段验证器")
    required_validator = RequiredFieldValidator("name", "姓名为必填项")
    
    test_values = ["张三", "", None, "   ", "李四", [], {}, "  有效名称  "]
    for value in test_values:
        result = required_validator.validate(value)
        status = "✅" if result.is_valid else "❌"
        message = result.error_message or "有效"
        display_value = repr(value) if value is not None else "None"
        print(f"  输入: {display_value:>15} -> {status} {message}")
    
    # 4. 人群规模验证器演示
    print("\n4. 人群规模验证器 (1-1,000,000)")
    population_validator = NumericRangeValidator(
        1, 1000000, "population_size", "人群规模必须在 1 到 1,000,000 之间"
    )
    
    test_populations = [10000, 0, 1000001, "50000", 500, 999999]
    for pop in test_populations:
        result = population_validator.validate(pop)
        status = "✅" if result.is_valid else "❌"
        message = result.error_message or result.warning_message or "有效"
        print(f"  输入: {str(pop):>10} -> {status} {message}")


def demo_validation_workflow():
    """演示验证工作流"""
    print("\n=== 验证工作流演示 ===\n")
    
    # 模拟用户输入的人群配置数据
    user_input = {
        "population_size": "50000",
        "age_mean": "65.5",
        "age_std": "12.0",
        "age_min": "45",
        "age_max": "85",
        "male_ratio": "52",  # 百分比形式
        "name": "测试配置"
    }
    
    # 创建验证器
    validators = {
        "population_size": NumericRangeValidator(1, 1000000, error_message="人群规模无效"),
        "age_mean": NumericRangeValidator(18, 100, error_message="年龄均值无效"),
        "age_std": NumericRangeValidator(1, 30, error_message="年龄标准差无效"),
        "age_min": NumericRangeValidator(18, 100, error_message="最小年龄无效"),
        "age_max": NumericRangeValidator(18, 100, error_message="最大年龄无效"),
        "male_ratio": PercentageValidator(error_message="性别比例无效"),
        "name": RequiredFieldValidator(error_message="配置名称为必填项")
    }
    
    print("验证用户输入:")
    print(f"输入数据: {user_input}")
    print("\n验证结果:")
    
    all_valid = True
    warnings = []
    
    for field, value in user_input.items():
        if field in validators:
            validator = validators[field]
            result = validator.validate(value)
            
            status = "✅" if result.is_valid else "❌"
            message = result.error_message or result.warning_message or "有效"
            
            print(f"  {field:>15}: {status} {message}")
            
            if not result.is_valid:
                all_valid = False
            elif result.warning_message:
                warnings.append(f"{field}: {result.warning_message}")
    
    print(f"\n总体验证结果: {'✅ 通过' if all_valid else '❌ 失败'}")
    
    if warnings:
        print("警告信息:")
        for warning in warnings:
            print(f"  ⚠️ {warning}")


def demo_edge_cases():
    """演示边界情况"""
    print("\n=== 边界情况演示 ===\n")
    
    # 测试边界值
    age_validator = NumericRangeValidator(18, 100)
    
    edge_cases = [
        (18, "最小边界值"),
        (100, "最大边界值"),
        (17.9, "略小于最小值"),
        (100.1, "略大于最大值"),
        (19, "接近最小值（可能有警告）"),
        (99, "接近最大值（可能有警告）")
    ]
    
    print("年龄验证边界情况:")
    for value, description in edge_cases:
        result = age_validator.validate(value)
        status = "✅" if result.is_valid else "❌"
        message = result.error_message or result.warning_message or "有效"
        print(f"  {value:>6} ({description:>15}): {status} {message}")


def main():
    """主演示函数"""
    print("结直肠癌筛查模拟器 - 表单验证器演示")
    print("=" * 60)
    
    demo_validators()
    demo_validation_workflow()
    demo_edge_cases()
    
    print("\n" + "=" * 60)
    print("验证器演示完成！")
    print("\n这些验证器将在桌面应用中提供实时输入验证功能")


if __name__ == "__main__":
    main()
