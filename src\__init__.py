"""
结直肠癌筛查微观模拟模型

这是一个用于评估不同筛查策略成本效益的微观模拟模型。
模型支持多种筛查工具和策略的比较分析。
"""

__version__ = "0.1.0"
__author__ = "Development Team"
__email__ = "<EMAIL>"

# 导出主要模块
from .core import (
    DiseaseState, PathwayType, CancerStage, Gender,
    ScreeningResult, ScreeningTool,
    Individual, HealthEvent,
    Population, PopulationStatistics,
    SimulationState, SimulationEvent, EventQueue
)

__all__ = [
    "__version__",
    "__author__",
    "__email__",
    # 核心类和枚举
    "DiseaseState",
    "PathwayType",
    "CancerStage",
    "Gender",
    "ScreeningResult",
    "ScreeningTool",
    "Individual",
    "HealthEvent",
    "Population",
    "PopulationStatistics",
    "SimulationState",
    "SimulationEvent",
    "EventQueue",
]
