"""
人群生成报告模块

提供人群生成结果的统计报告和可视化功能。
"""

import json
from typing import Dict, Any, Optional, List
from pathlib import Path
import numpy as np

try:
    import matplotlib.pyplot as plt
    HAS_MATPLOTLIB = True
except ImportError:
    HAS_MATPLOTLIB = False

from ...core import Population
from .population_generator import GenerationSummary


class PopulationReporter:
    """
    人群生成报告器
    
    生成人群统计报告，包括文本报告和可视化图表。
    """
    
    def __init__(self, output_dir: Optional[Path] = None):
        """
        初始化报告器
        
        Args:
            output_dir: 输出目录，默认为reports/population/
        """
        if output_dir is None:
            output_dir = Path("reports/population")
        
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
    
    def generate_summary_report(
        self,
        population: Population,
        summary: Optional[GenerationSummary] = None,
        save_to_file: bool = True
    ) -> str:
        """
        生成人群摘要报告
        
        Args:
            population: 人群对象
            summary: 生成摘要（可选）
            save_to_file: 是否保存到文件
            
        Returns:
            报告文本
        """
        stats = population.statistics.get_summary()
        
        # 构建报告内容
        report_lines = [
            "=" * 60,
            "人群生成摘要报告",
            "=" * 60,
            "",
            "基本信息:",
            f"  总人数: {stats['total_individuals']:,}",
            f"  存活率: {stats['survival_rate']:.1%}",
            "",
            "年龄统计:",
            f"  平均年龄: {stats['age_statistics']['mean']:.1f}岁",
            f"  年龄中位数: {stats['age_statistics']['median']:.1f}岁",
            f"  年龄标准差: {stats['age_statistics']['std']:.1f}岁",
            f"  年龄范围: {stats['age_statistics']['min']:.1f} - {stats['age_statistics']['max']:.1f}岁",
            "",
            "性别分布:",
        ]
        
        gender_dist = stats['gender_distribution']
        total = sum(gender_dist.values())
        for gender, count in gender_dist.items():
            percentage = count / total * 100 if total > 0 else 0
            report_lines.append(f"  {gender}: {count:,} ({percentage:.1f}%)")
        
        report_lines.extend([
            "",
            "疾病状态分布:",
        ])
        
        disease_dist = stats['disease_state_distribution']
        for state, count in disease_dist.items():
            percentage = count / total * 100 if total > 0 else 0
            report_lines.append(f"  {state}: {count:,} ({percentage:.1f}%)")
        
        if stats['pathway_distribution']:
            report_lines.extend([
                "",
                "疾病通路分布:",
            ])
            
            pathway_dist = stats['pathway_distribution']
            for pathway, count in pathway_dist.items():
                percentage = count / total * 100 if total > 0 else 0
                report_lines.append(f"  {pathway}: {count:,} ({percentage:.1f}%)")
        
        if summary:
            report_lines.extend([
                "",
                "生成信息:",
                f"  生成时间: {summary.generation_time:.2f}秒",
                f"  生成速度: {summary.total_individuals/summary.generation_time:.0f} 个体/秒",
            ])
            
            if summary.config_used:
                report_lines.extend([
                    "",
                    "配置参数:",
                ])
                for key, value in summary.config_used.items():
                    if isinstance(value, dict):
                        report_lines.append(f"  {key}:")
                        for sub_key, sub_value in value.items():
                            report_lines.append(f"    {sub_key}: {sub_value}")
                    else:
                        report_lines.append(f"  {key}: {value}")
        
        report_lines.append("=" * 60)
        
        report_text = "\n".join(report_lines)
        
        if save_to_file:
            report_file = self.output_dir / "population_summary.txt"
            with open(report_file, 'w', encoding='utf-8') as f:
                f.write(report_text)
        
        return report_text
    
    def generate_age_distribution_plot(
        self,
        population: Population,
        save_to_file: bool = True,
        show_plot: bool = False
    ) -> Optional[Path]:
        """
        生成年龄分布图

        Args:
            population: 人群对象
            save_to_file: 是否保存到文件
            show_plot: 是否显示图表

        Returns:
            保存的文件路径（如果保存）
        """
        if not HAS_MATPLOTLIB:
            print("警告: matplotlib未安装，无法生成图表")
            return None

        ages = [ind.get_current_age() for ind in population]

        if not ages:
            return None
        
        plt.figure(figsize=(10, 6))
        
        # 创建直方图
        plt.hist(ages, bins=30, alpha=0.7, color='skyblue', edgecolor='black')
        plt.axvline(np.mean(ages), color='red', linestyle='--', 
                   label=f'平均年龄: {np.mean(ages):.1f}岁')
        plt.axvline(np.median(ages), color='green', linestyle='--', 
                   label=f'年龄中位数: {np.median(ages):.1f}岁')
        
        plt.xlabel('年龄 (岁)')
        plt.ylabel('人数')
        plt.title(f'人群年龄分布 (n={len(ages):,})')
        plt.legend()
        plt.grid(True, alpha=0.3)
        
        # 添加统计信息文本
        stats_text = f'标准差: {np.std(ages):.1f}岁\n范围: {min(ages):.1f}-{max(ages):.1f}岁'
        plt.text(0.02, 0.98, stats_text, transform=plt.gca().transAxes, 
                verticalalignment='top', bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
        
        plt.tight_layout()
        
        if save_to_file:
            plot_file = self.output_dir / "age_distribution.png"
            plt.savefig(plot_file, dpi=300, bbox_inches='tight')
            if not show_plot:
                plt.close()
            return plot_file
        
        if show_plot:
            plt.show()
        else:
            plt.close()
        
        return None
    
    def generate_gender_distribution_plot(
        self,
        population: Population,
        save_to_file: bool = True,
        show_plot: bool = False
    ) -> Optional[Path]:
        """
        生成性别分布饼图

        Args:
            population: 人群对象
            save_to_file: 是否保存到文件
            show_plot: 是否显示图表

        Returns:
            保存的文件路径（如果保存）
        """
        if not HAS_MATPLOTLIB:
            print("警告: matplotlib未安装，无法生成图表")
            return None

        gender_stats = population.statistics.get_gender_distribution()

        if not gender_stats:
            return None
        
        plt.figure(figsize=(8, 8))
        
        labels = []
        sizes = []
        colors = ['lightblue', 'lightpink']
        
        for i, (gender, count) in enumerate(gender_stats.items()):
            labels.append(f'{gender}\n({count:,})')
            sizes.append(count)
        
        plt.pie(sizes, labels=labels, colors=colors[:len(sizes)], autopct='%1.1f%%', 
                startangle=90, textprops={'fontsize': 12})
        plt.title(f'性别分布 (总计: {sum(sizes):,})', fontsize=14, fontweight='bold')
        
        plt.axis('equal')
        
        if save_to_file:
            plot_file = self.output_dir / "gender_distribution.png"
            plt.savefig(plot_file, dpi=300, bbox_inches='tight')
            if not show_plot:
                plt.close()
            return plot_file
        
        if show_plot:
            plt.show()
        else:
            plt.close()
        
        return None
    
    def generate_comprehensive_report(
        self,
        population: Population,
        summary: Optional[GenerationSummary] = None,
        include_plots: bool = True
    ) -> Dict[str, Any]:
        """
        生成综合报告
        
        Args:
            population: 人群对象
            summary: 生成摘要（可选）
            include_plots: 是否包含图表
            
        Returns:
            报告信息字典
        """
        report_info = {
            "text_report": self.generate_summary_report(population, summary, save_to_file=True),
            "plots": {}
        }
        
        if include_plots:
            # 生成年龄分布图
            age_plot_path = self.generate_age_distribution_plot(population, save_to_file=True)
            if age_plot_path:
                report_info["plots"]["age_distribution"] = str(age_plot_path)
            
            # 生成性别分布图
            gender_plot_path = self.generate_gender_distribution_plot(population, save_to_file=True)
            if gender_plot_path:
                report_info["plots"]["gender_distribution"] = str(gender_plot_path)
        
        # 保存报告元数据
        metadata = {
            "population_size": population.get_size(),
            "generation_time": summary.generation_time if summary else None,
            "report_generated_at": str(np.datetime64('now')),
            "output_directory": str(self.output_dir),
            "files_generated": {
                "summary_report": str(self.output_dir / "population_summary.txt"),
                **report_info["plots"]
            }
        }
        
        metadata_file = self.output_dir / "report_metadata.json"
        with open(metadata_file, 'w', encoding='utf-8') as f:
            json.dump(metadata, f, indent=2, ensure_ascii=False)
        
        report_info["metadata"] = metadata
        
        return report_info
    
    def compare_populations(
        self,
        populations: List[Population],
        population_names: List[str],
        save_to_file: bool = True
    ) -> str:
        """
        比较多个人群的统计特征
        
        Args:
            populations: 人群列表
            population_names: 人群名称列表
            save_to_file: 是否保存到文件
            
        Returns:
            比较报告文本
        """
        if len(populations) != len(population_names):
            raise ValueError("人群数量和名称数量不匹配")
        
        report_lines = [
            "=" * 80,
            "人群比较报告",
            "=" * 80,
            ""
        ]
        
        # 基本信息比较
        report_lines.append("基本信息比较:")
        report_lines.append(f"{'人群名称':<20} {'总人数':<10} {'平均年龄':<10} {'存活率':<10}")
        report_lines.append("-" * 60)
        
        for pop, name in zip(populations, population_names):
            stats = pop.statistics.get_summary()
            report_lines.append(
                f"{name:<20} {stats['total_individuals']:<10,} "
                f"{stats['age_statistics']['mean']:<10.1f} "
                f"{stats['survival_rate']:<10.1%}"
            )
        
        # 性别分布比较
        report_lines.extend([
            "",
            "性别分布比较:",
            f"{'人群名称':<20} {'男性人数':<12} {'男性比例':<12} {'女性人数':<12} {'女性比例':<12}",
            "-" * 80
        ])
        
        for pop, name in zip(populations, population_names):
            gender_dist = pop.statistics.get_gender_distribution()
            total = sum(gender_dist.values())
            male_count = gender_dist.get('male', 0)
            female_count = gender_dist.get('female', 0)
            male_ratio = male_count / total if total > 0 else 0
            female_ratio = female_count / total if total > 0 else 0
            
            report_lines.append(
                f"{name:<20} {male_count:<12,} {male_ratio:<12.1%} "
                f"{female_count:<12,} {female_ratio:<12.1%}"
            )
        
        report_lines.append("=" * 80)
        
        report_text = "\n".join(report_lines)
        
        if save_to_file:
            comparison_file = self.output_dir / "population_comparison.txt"
            with open(comparison_file, 'w', encoding='utf-8') as f:
                f.write(report_text)
        
        return report_text
