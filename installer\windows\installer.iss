[Setup]
; 应用程序基本信息
AppName=结直肠癌筛查模拟器
AppVersion=1.0.0
AppPublisher=深圳市南山区慢性病防治院
AppPublisherURL=https://www.sznsmby.cn
AppSupportURL=https://www.sznsmby.cn/support
AppUpdatesURL=https://www.sznsmby.cn/updates
AppId={{A1B2C3D4-E5F6-7890-ABCD-EF1234567890}

; 默认安装路径
DefaultDirName={autopf}\ColorectalCancerSimulator
DefaultGroupName=结直肠癌筛查模拟器
AllowNoIcons=yes

; 输出配置
OutputDir=..\..\dist\windows
OutputBaseFilename=ColorectalCancerSimulator-1.0.0-Setup
SetupIconFile=..\..\resources\icons\app.ico
Compression=lzma
SolidCompression=yes
WizardStyle=modern

; 权限和兼容性
PrivilegesRequired=lowest
PrivilegesRequiredOverridesAllowed=dialog
ArchitecturesAllowed=x64
ArchitecturesInstallIn64BitMode=x64

; 许可协议
LicenseFile=..\..\LICENSE

; 安装文件
[Files]
Source: "..\..\dist\ColorectalCancerSimulator\*"; DestDir: "{app}"; Flags: ignoreversion recursesubdirs createallsubdirs

; 开始菜单快捷方式
[Icons]
Name: "{group}\结直肠癌筛查模拟器"; Filename: "{app}\ColorectalCancerSimulator.exe"
Name: "{group}\{cm:UninstallProgram,结直肠癌筛查模拟器}"; Filename: "{uninstallexe}"
Name: "{autodesktop}\结直肠癌筛查模拟器"; Filename: "{app}\ColorectalCancerSimulator.exe"; Tasks: desktopicon

; 可选任务
[Tasks]
Name: "desktopicon"; Description: "{cm:CreateDesktopIcon}"; GroupDescription: "{cm:AdditionalIcons}"; Flags: unchecked
Name: "quicklaunchicon"; Description: "{cm:CreateQuickLaunchIcon}"; GroupDescription: "{cm:AdditionalIcons}"; Flags: unchecked; OnlyBelowVersion: 6.1

; 运行程序
[Run]
Filename: "{app}\ColorectalCancerSimulator.exe"; Description: "{cm:LaunchProgram,结直肠癌筛查模拟器}"; Flags: nowait postinstall skipifsilent

; 卸载时清理
[UninstallDelete]
Type: filesandordirs; Name: "{app}\logs"
Type: filesandordirs; Name: "{app}\temp"
Type: filesandordirs; Name: "{app}\cache"

; 注册表项
[Registry]
Root: HKCU; Subkey: "Software\SZNSMBY\ColorectalCancerSimulator"; ValueType: string; ValueName: "InstallPath"; ValueData: "{app}"; Flags: uninsdeletekey
Root: HKCU; Subkey: "Software\SZNSMBY\ColorectalCancerSimulator"; ValueType: string; ValueName: "Version"; ValueData: "1.0.0"; Flags: uninsdeletekey

; 安装前检查
[Code]
function InitializeSetup(): Boolean;
begin
  Result := True;
  // 检查.NET Framework或其他依赖项
  // if not IsDotNetDetected('v4.7.2', 0) then begin
  //   MsgBox('需要安装 .NET Framework 4.7.2 或更高版本。', mbError, MB_OK);
  //   Result := False;
  // end;
end;

procedure InitializeWizard();
begin
  // 自定义安装向导界面
end;

function ShouldSkipPage(PageID: Integer): Boolean;
begin
  Result := False;
  // 根据条件跳过某些页面
end;
