"""
综合风险评分计算引擎

实现个体综合风险评分的计算，包括：
- 加权风险评分算法
- 年龄调整
- 性别调整
- 风险分层
- 历史跟踪
"""

import math
import logging
from datetime import datetime
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass, field
from enum import Enum

from .risk_factors import RiskFactorType, RiskFactorProfile
from .risk_weights import RiskFactorWeights
from ...core.enums import Gender


logger = logging.getLogger(__name__)


class RiskLevel(Enum):
    """风险等级枚举"""
    LOW = "low"
    MODERATE = "moderate"
    HIGH = "high"


@dataclass
class RiskScore:
    """风险评分结果"""
    individual_id: str
    base_score: float
    age_adjusted_score: float
    gender_adjusted_score: float
    final_score: float
    risk_level: RiskLevel
    contributing_factors: Dict[RiskFactorType, float]
    calculation_timestamp: datetime = field(default_factory=datetime.now)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "individual_id": self.individual_id,
            "base_score": self.base_score,
            "age_adjusted_score": self.age_adjusted_score,
            "gender_adjusted_score": self.gender_adjusted_score,
            "final_score": self.final_score,
            "risk_level": self.risk_level.value,
            "contributing_factors": {
                factor_type.value: score 
                for factor_type, score in self.contributing_factors.items()
            },
            "calculation_timestamp": self.calculation_timestamp.isoformat()
        }


class RiskCalculator:
    """综合风险评分计算器"""
    
    def __init__(self, weights: Optional[RiskFactorWeights] = None):
        """
        初始化风险计算器
        
        Args:
            weights: 风险因素权重配置，如果为None则使用默认配置
        """
        self.weights = weights or RiskFactorWeights()
        self.calculation_history: Dict[str, List[RiskScore]] = {}
    
    def calculate_risk_score(
        self, 
        individual_id: str,
        risk_profile: RiskFactorProfile,
        age: float,
        gender: Gender,
        algorithm: str = "multiplicative"
    ) -> RiskScore:
        """
        计算个体综合风险评分
        
        Args:
            individual_id: 个体ID
            risk_profile: 风险因素档案
            age: 年龄
            gender: 性别
            algorithm: 计算算法 ("multiplicative" 或 "additive")
            
        Returns:
            RiskScore: 风险评分结果
        """
        # 计算基础风险评分
        base_score, contributing_factors = self._calculate_base_score(
            risk_profile, algorithm
        )
        
        # 年龄调整
        age_adjusted_score = self._apply_age_adjustment(base_score, age)
        
        # 性别调整
        gender_adjusted_score = self._apply_gender_adjustment(
            age_adjusted_score, gender
        )
        
        # 确定风险等级
        risk_level = self._determine_risk_level(gender_adjusted_score)
        
        # 创建风险评分结果
        risk_score = RiskScore(
            individual_id=individual_id,
            base_score=base_score,
            age_adjusted_score=age_adjusted_score,
            gender_adjusted_score=gender_adjusted_score,
            final_score=gender_adjusted_score,
            risk_level=risk_level,
            contributing_factors=contributing_factors
        )
        
        # 记录历史
        self._record_score_history(individual_id, risk_score)
        
        logger.debug(f"计算风险评分完成: {individual_id}, 最终评分: {gender_adjusted_score:.3f}")
        
        return risk_score
    
    def _calculate_base_score(
        self, 
        risk_profile: RiskFactorProfile,
        algorithm: str
    ) -> Tuple[float, Dict[RiskFactorType, float]]:
        """
        计算基础风险评分
        
        Args:
            risk_profile: 风险因素档案
            algorithm: 计算算法
            
        Returns:
            Tuple[float, Dict]: (基础评分, 贡献因素字典)
        """
        contributing_factors = {}
        
        if algorithm == "multiplicative":
            base_score = 1.0
            
            for factor_type, risk_factor in risk_profile.risk_factors.items():
                if factor_type.is_boolean():
                    if risk_factor.value:
                        factor_weight = self.weights.get_weight_value(factor_type)
                        base_score *= factor_weight
                        contributing_factors[factor_type] = factor_weight
                    else:
                        contributing_factors[factor_type] = 1.0
                        
                elif factor_type.is_continuous():
                    factor_weight = self.weights.calculate_continuous_weight(
                        factor_type, risk_factor.value
                    )
                    base_score *= factor_weight
                    contributing_factors[factor_type] = factor_weight
                    
        elif algorithm == "additive":
            base_score = 0.0
            
            for factor_type, risk_factor in risk_profile.risk_factors.items():
                if factor_type.is_boolean():
                    if risk_factor.value:
                        factor_weight = self.weights.get_weight_value(factor_type)
                        base_score += factor_weight - 1.0  # 减去基线1.0
                        contributing_factors[factor_type] = factor_weight - 1.0
                    else:
                        contributing_factors[factor_type] = 0.0
                        
                elif factor_type.is_continuous():
                    factor_weight = self.weights.calculate_continuous_weight(
                        factor_type, risk_factor.value
                    )
                    base_score += factor_weight - 1.0  # 减去基线1.0
                    contributing_factors[factor_type] = factor_weight - 1.0
            
            base_score += 1.0  # 加回基线
            
        else:
            raise ValueError(f"不支持的算法: {algorithm}")
        
        return base_score, contributing_factors
    
    def _apply_age_adjustment(self, base_score: float, age: float) -> float:
        """应用年龄调整"""
        age_params = self.weights.get_age_adjustment_params()
        
        if not age_params.get("enabled", True):
            return base_score
        
        baseline_age = age_params.get("baseline_age", 50)
        per_year_multiplier = age_params.get("per_year_multiplier", 0.03)
        max_age_effect = age_params.get("max_age_effect", 2.0)
        
        if age <= baseline_age:
            return base_score
        
        age_multiplier = 1.0 + (age - baseline_age) * per_year_multiplier
        age_multiplier = min(age_multiplier, max_age_effect)
        
        return base_score * age_multiplier
    
    def _apply_gender_adjustment(self, age_adjusted_score: float, gender: Gender) -> float:
        """应用性别调整"""
        gender_params = self.weights.get_gender_adjustment_params()
        
        if not gender_params.get("enabled", True):
            return age_adjusted_score
        
        if gender == Gender.MALE:
            multiplier = gender_params.get("male_multiplier", 1.2)
        else:
            multiplier = gender_params.get("female_multiplier", 1.0)
        
        return age_adjusted_score * multiplier
    
    def _determine_risk_level(self, final_score: float) -> RiskLevel:
        """确定风险等级"""
        thresholds = self.weights.get_risk_stratification_thresholds()
        
        if final_score < thresholds["low_risk"]:
            return RiskLevel.LOW
        elif final_score < thresholds["high_risk"]:
            return RiskLevel.MODERATE
        else:
            return RiskLevel.HIGH
    
    def _record_score_history(self, individual_id: str, risk_score: RiskScore):
        """记录评分历史"""
        if individual_id not in self.calculation_history:
            self.calculation_history[individual_id] = []
        
        self.calculation_history[individual_id].append(risk_score)
        
        # 限制历史记录数量（保留最近100次）
        if len(self.calculation_history[individual_id]) > 100:
            self.calculation_history[individual_id] = self.calculation_history[individual_id][-100:]
    
    def get_score_history(self, individual_id: str) -> List[RiskScore]:
        """获取个体的评分历史"""
        return self.calculation_history.get(individual_id, []).copy()
    
    def get_latest_score(self, individual_id: str) -> Optional[RiskScore]:
        """获取个体的最新评分"""
        history = self.calculation_history.get(individual_id, [])
        return history[-1] if history else None
    
    def analyze_risk_trend(self, individual_id: str) -> Dict[str, Any]:
        """分析个体风险趋势"""
        history = self.get_score_history(individual_id)
        
        if len(history) < 2:
            return {
                "trend": "insufficient_data",
                "score_count": len(history),
                "message": "需要至少2次评分记录才能分析趋势"
            }
        
        # 计算趋势
        scores = [score.final_score for score in history]
        recent_scores = scores[-5:]  # 最近5次评分
        
        if len(recent_scores) >= 2:
            trend_slope = (recent_scores[-1] - recent_scores[0]) / (len(recent_scores) - 1)
            
            if trend_slope > 0.1:
                trend = "increasing"
            elif trend_slope < -0.1:
                trend = "decreasing"
            else:
                trend = "stable"
        else:
            trend = "stable"
        
        return {
            "trend": trend,
            "score_count": len(history),
            "latest_score": scores[-1],
            "earliest_score": scores[0],
            "score_change": scores[-1] - scores[0],
            "average_score": sum(scores) / len(scores),
            "min_score": min(scores),
            "max_score": max(scores),
            "recent_scores": recent_scores
        }
    
    def compare_risk_profiles(
        self, 
        profile1: RiskFactorProfile,
        profile2: RiskFactorProfile,
        age1: float,
        age2: float,
        gender1: Gender,
        gender2: Gender
    ) -> Dict[str, Any]:
        """比较两个风险档案"""
        score1 = self.calculate_risk_score("temp1", profile1, age1, gender1)
        score2 = self.calculate_risk_score("temp2", profile2, age2, gender2)
        
        # 清理临时记录
        self.calculation_history.pop("temp1", None)
        self.calculation_history.pop("temp2", None)
        
        return {
            "profile1_score": score1.final_score,
            "profile2_score": score2.final_score,
            "score_difference": score2.final_score - score1.final_score,
            "profile1_risk_level": score1.risk_level.value,
            "profile2_risk_level": score2.risk_level.value,
            "risk_level_change": score2.risk_level != score1.risk_level,
            "contributing_factors_comparison": self._compare_contributing_factors(
                score1.contributing_factors, 
                score2.contributing_factors
            )
        }
    
    def _compare_contributing_factors(
        self, 
        factors1: Dict[RiskFactorType, float],
        factors2: Dict[RiskFactorType, float]
    ) -> Dict[str, Any]:
        """比较贡献因素"""
        all_factors = set(factors1.keys()) | set(factors2.keys())
        
        factor_changes = {}
        for factor in all_factors:
            value1 = factors1.get(factor, 1.0)
            value2 = factors2.get(factor, 1.0)
            
            factor_changes[factor.value] = {
                "profile1_weight": value1,
                "profile2_weight": value2,
                "weight_change": value2 - value1
            }
        
        return factor_changes
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取计算器统计信息"""
        total_calculations = sum(len(history) for history in self.calculation_history.values())
        total_individuals = len(self.calculation_history)
        
        if total_calculations == 0:
            return {
                "total_calculations": 0,
                "total_individuals": 0,
                "average_calculations_per_individual": 0,
                "risk_level_distribution": {}
            }
        
        # 统计风险等级分布（基于最新评分）
        risk_levels = []
        for history in self.calculation_history.values():
            if history:
                risk_levels.append(history[-1].risk_level.value)
        
        risk_level_counts = {}
        for level in risk_levels:
            risk_level_counts[level] = risk_level_counts.get(level, 0) + 1
        
        return {
            "total_calculations": total_calculations,
            "total_individuals": total_individuals,
            "average_calculations_per_individual": total_calculations / total_individuals,
            "risk_level_distribution": risk_level_counts
        }
    
    def clear_history(self, individual_id: Optional[str] = None):
        """清理历史记录"""
        if individual_id:
            self.calculation_history.pop(individual_id, None)
        else:
            self.calculation_history.clear()
    
    def __repr__(self) -> str:
        """字符串表示"""
        total_calculations = sum(len(history) for history in self.calculation_history.values())
        return (
            f"RiskCalculator(individuals={len(self.calculation_history)}, "
            f"calculations={total_calculations})"
        )
