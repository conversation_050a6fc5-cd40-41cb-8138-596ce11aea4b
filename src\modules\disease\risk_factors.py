"""
风险因素管理系统

实现个体风险因素的定义、管理和计算功能，包括：
- 风险因素类型枚举
- 风险因素数据结构
- 个体风险因素档案管理
"""

import json
from datetime import datetime
from enum import Enum
from dataclasses import dataclass, field, asdict
from typing import Union, Dict, Any, List, Optional, Set
from pathlib import Path


class RiskFactorType(Enum):
    """风险因素类型枚举"""
    # 遗传因素
    FAMILY_HISTORY = "family_history"           # 家族史（布尔）
    
    # 疾病相关因素
    IBD = "inflammatory_bowel_disease"          # 炎症性肠病（布尔）
    DIABETES = "diabetes_mellitus"              # 糖尿病（布尔）
    
    # 生活方式因素
    BMI = "body_mass_index"                     # 体重指数（连续）
    SMOKING = "smoking_status"                  # 吸烟状态（布尔/分类）
    SEDENTARY_LIFESTYLE = "sedentary_lifestyle" # 久坐生活方式（连续/小时）
    ALCOHOL_CONSUMPTION = "alcohol_consumption" # 酒精消费（连续）
    DIET_QUALITY = "diet_quality"               # 饮食质量（评分）
    
    @classmethod
    def get_boolean_factors(cls) -> Set["RiskFactorType"]:
        """获取布尔型风险因素"""
        return {
            cls.FAMILY_HISTORY,
            cls.IBD,
            cls.DIABETES,
            cls.SMOKING
        }
    
    @classmethod
    def get_continuous_factors(cls) -> Set["RiskFactorType"]:
        """获取连续型风险因素"""
        return {
            cls.BMI,
            cls.SEDENTARY_LIFESTYLE,
            cls.ALCOHOL_CONSUMPTION,
            cls.DIET_QUALITY
        }
    
    def is_boolean(self) -> bool:
        """判断是否为布尔型风险因素"""
        return self in self.get_boolean_factors()
    
    def is_continuous(self) -> bool:
        """判断是否为连续型风险因素"""
        return self in self.get_continuous_factors()


class RiskFactorCategory(Enum):
    """风险因素分类"""
    GENETIC = "genetic"           # 遗传因素
    LIFESTYLE = "lifestyle"       # 生活方式因素
    DISEASE_RELATED = "disease_related"  # 疾病相关因素
    
    @classmethod
    def get_category_for_factor(cls, factor_type: RiskFactorType) -> "RiskFactorCategory":
        """获取风险因素的分类"""
        category_mapping = {
            RiskFactorType.FAMILY_HISTORY: cls.GENETIC,
            RiskFactorType.IBD: cls.DISEASE_RELATED,
            RiskFactorType.DIABETES: cls.DISEASE_RELATED,
            RiskFactorType.BMI: cls.LIFESTYLE,
            RiskFactorType.SMOKING: cls.LIFESTYLE,
            RiskFactorType.SEDENTARY_LIFESTYLE: cls.LIFESTYLE,
            RiskFactorType.ALCOHOL_CONSUMPTION: cls.LIFESTYLE,
            RiskFactorType.DIET_QUALITY: cls.LIFESTYLE,
        }
        return category_mapping[factor_type]


@dataclass
class RiskFactor:
    """风险因素数据类"""
    factor_type: RiskFactorType
    value: Union[bool, float, int, str]
    weight: float = 1.0
    last_updated: datetime = field(default_factory=datetime.now)
    source: str = "default"
    confidence_interval: Optional[tuple] = None
    reference: Optional[str] = None
    
    def __post_init__(self):
        """验证风险因素数据"""
        self._validate_value()
        self._validate_weight()
    
    def _validate_value(self):
        """验证风险因素值"""
        if self.factor_type.is_boolean():
            if not isinstance(self.value, bool):
                raise ValueError(f"布尔型风险因素 {self.factor_type.value} 的值必须是布尔类型")
        elif self.factor_type.is_continuous():
            if not isinstance(self.value, (int, float)):
                raise ValueError(f"连续型风险因素 {self.factor_type.value} 的值必须是数值类型")
            
            # 特定范围验证
            if self.factor_type == RiskFactorType.BMI:
                if not (10.0 <= self.value <= 60.0):
                    raise ValueError(f"BMI值必须在10.0-60.0范围内，当前值: {self.value}")
            elif self.factor_type == RiskFactorType.SEDENTARY_LIFESTYLE:
                if not (0 <= self.value <= 24):
                    raise ValueError(f"久坐时间必须在0-24小时范围内，当前值: {self.value}")
            elif self.factor_type == RiskFactorType.ALCOHOL_CONSUMPTION:
                if not (0 <= self.value <= 100):
                    raise ValueError(f"酒精消费必须在0-100单位/周范围内，当前值: {self.value}")
            elif self.factor_type == RiskFactorType.DIET_QUALITY:
                if not (0 <= self.value <= 100):
                    raise ValueError(f"饮食质量评分必须在0-100范围内，当前值: {self.value}")
    
    def _validate_weight(self):
        """验证权重值"""
        if not isinstance(self.weight, (int, float)):
            raise ValueError("权重必须是数值类型")
        if self.weight < 0:
            raise ValueError("权重不能为负数")
    
    @property
    def category(self) -> RiskFactorCategory:
        """获取风险因素分类"""
        return RiskFactorCategory.get_category_for_factor(self.factor_type)
    
    def update_value(self, new_value: Union[bool, float, int, str], source: str = "update"):
        """更新风险因素值"""
        old_value = self.value
        self.value = new_value
        self.last_updated = datetime.now()
        self.source = source
        
        try:
            self._validate_value()
        except ValueError:
            # 如果验证失败，恢复原值
            self.value = old_value
            raise
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "factor_type": self.factor_type.value,
            "value": self.value,
            "weight": self.weight,
            "last_updated": self.last_updated.isoformat(),
            "source": self.source,
            "confidence_interval": self.confidence_interval,
            "reference": self.reference,
            "category": self.category.value
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "RiskFactor":
        """从字典创建风险因素"""
        factor_type = RiskFactorType(data["factor_type"])
        last_updated = datetime.fromisoformat(data["last_updated"])
        
        return cls(
            factor_type=factor_type,
            value=data["value"],
            weight=data["weight"],
            last_updated=last_updated,
            source=data.get("source", "default"),
            confidence_interval=data.get("confidence_interval"),
            reference=data.get("reference")
        )


class RiskFactorProfile:
    """个体风险因素档案管理类"""
    
    def __init__(self, individual_id: str):
        """
        初始化风险因素档案
        
        Args:
            individual_id: 个体唯一标识符
        """
        self.individual_id = individual_id
        self.risk_factors: Dict[RiskFactorType, RiskFactor] = {}
        self.created_at = datetime.now()
        self.updated_at = datetime.now()
    
    def add_risk_factor(self, risk_factor: RiskFactor) -> bool:
        """
        添加风险因素
        
        Args:
            risk_factor: 风险因素对象
            
        Returns:
            bool: 是否成功添加
        """
        try:
            self.risk_factors[risk_factor.factor_type] = risk_factor
            self.updated_at = datetime.now()
            return True
        except Exception:
            return False
    
    def remove_risk_factor(self, factor_type: RiskFactorType) -> bool:
        """
        移除风险因素
        
        Args:
            factor_type: 风险因素类型
            
        Returns:
            bool: 是否成功移除
        """
        if factor_type in self.risk_factors:
            del self.risk_factors[factor_type]
            self.updated_at = datetime.now()
            return True
        return False
    
    def get_risk_factor(self, factor_type: RiskFactorType) -> Optional[RiskFactor]:
        """获取指定类型的风险因素"""
        return self.risk_factors.get(factor_type)
    
    def has_risk_factor(self, factor_type: RiskFactorType) -> bool:
        """检查是否存在指定类型的风险因素"""
        return factor_type in self.risk_factors
    
    def get_factors_by_category(self, category: RiskFactorCategory) -> List[RiskFactor]:
        """按分类获取风险因素"""
        return [
            factor for factor in self.risk_factors.values()
            if factor.category == category
        ]
    
    def get_boolean_factors(self) -> Dict[RiskFactorType, bool]:
        """获取所有布尔型风险因素的值"""
        return {
            factor_type: factor.value
            for factor_type, factor in self.risk_factors.items()
            if factor_type.is_boolean()
        }
    
    def get_continuous_factors(self) -> Dict[RiskFactorType, float]:
        """获取所有连续型风险因素的值"""
        return {
            factor_type: float(factor.value)
            for factor_type, factor in self.risk_factors.items()
            if factor_type.is_continuous()
        }
    
    def update_risk_factor(
        self, 
        factor_type: RiskFactorType, 
        new_value: Union[bool, float, int, str],
        source: str = "update"
    ) -> bool:
        """
        更新风险因素值
        
        Args:
            factor_type: 风险因素类型
            new_value: 新值
            source: 更新来源
            
        Returns:
            bool: 是否成功更新
        """
        if factor_type in self.risk_factors:
            try:
                self.risk_factors[factor_type].update_value(new_value, source)
                self.updated_at = datetime.now()
                return True
            except ValueError:
                return False
        return False
    
    def get_summary(self) -> Dict[str, Any]:
        """获取风险因素档案摘要"""
        return {
            "individual_id": self.individual_id,
            "total_factors": len(self.risk_factors),
            "factors_by_category": {
                category.value: len(self.get_factors_by_category(category))
                for category in RiskFactorCategory
            },
            "boolean_factors": len([f for f in self.risk_factors.values() if f.factor_type.is_boolean()]),
            "continuous_factors": len([f for f in self.risk_factors.values() if f.factor_type.is_continuous()]),
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat()
        }
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "individual_id": self.individual_id,
            "risk_factors": {
                factor_type.value: factor.to_dict()
                for factor_type, factor in self.risk_factors.items()
            },
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat()
        }
    
    def save_to_file(self, file_path: Path) -> bool:
        """保存到文件"""
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(self.to_dict(), f, ensure_ascii=False, indent=2)
            return True
        except Exception:
            return False
    
    @classmethod
    def load_from_file(cls, file_path: Path) -> Optional["RiskFactorProfile"]:
        """从文件加载"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            profile = cls(data["individual_id"])
            profile.created_at = datetime.fromisoformat(data["created_at"])
            profile.updated_at = datetime.fromisoformat(data["updated_at"])
            
            for factor_type_str, factor_data in data["risk_factors"].items():
                factor = RiskFactor.from_dict(factor_data)
                profile.risk_factors[factor.factor_type] = factor
            
            return profile
        except Exception:
            return None
    
    def __len__(self) -> int:
        """返回风险因素数量"""
        return len(self.risk_factors)
    
    def __contains__(self, factor_type: RiskFactorType) -> bool:
        """检查是否包含指定风险因素"""
        return factor_type in self.risk_factors
    
    def __repr__(self) -> str:
        """字符串表示"""
        return (
            f"RiskFactorProfile(individual_id={self.individual_id}, "
            f"factors={len(self.risk_factors)})"
        )
