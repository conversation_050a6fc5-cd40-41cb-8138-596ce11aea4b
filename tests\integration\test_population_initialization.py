"""
人群初始化集成测试

测试PopulationGenerator和PopulationConfig的集成功能。
"""

import pytest
import tempfile
import yaml
from pathlib import Path

from src.modules.population import PopulationGenerator, PopulationConfig
from src.core import Gender, PathwayType


class TestPopulationInitializationIntegration:
    """人群初始化集成测试"""
    
    def test_config_to_generator_workflow(self):
        """测试从配置文件到人群生成的完整工作流"""
        # 创建配置文件
        config_data = {
            "population_config": {
                "name": "集成测试人群",
                "description": "用于集成测试的人群配置",
                "size": 500,
                "random_seed": 42,
                "birth_year_base": 2025,
                "age_distribution": {
                    "type": "normal",
                    "mean": 60.0,
                    "std": 10.0,
                    "min_age": 45,
                    "max_age": 75
                },
                "gender_distribution": {
                    "male_ratio": 0.55,
                    "female_ratio": 0.45
                },
                "pathway_distribution": {
                    "adenoma_carcinoma_ratio": 0.8,
                    "serrated_adenoma_ratio": 0.2
                }
            }
        }
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            yaml.dump(config_data, f)
            temp_path = f.name
        
        try:
            # 加载配置
            config_manager = PopulationConfig()
            config = config_manager.load_config(temp_path)
            
            # 使用配置生成人群
            generator = PopulationGenerator(random_seed=config.random_seed)
            generation_params = config.to_generation_params()
            population = generator.generate_population(**generation_params, show_progress=False)
            
            # 验证生成结果
            assert population.get_size() == 500
            
            # 验证年龄分布
            ages = [ind.get_current_age() for ind in population]
            assert all(45 <= age <= 75 for age in ages)
            
            # 验证性别分布（允许一定误差）
            gender_stats = population.statistics.get_gender_distribution()
            male_ratio = gender_stats["male"] / 500
            assert 0.50 < male_ratio < 0.60  # 允许5%误差
            
            # 验证疾病通路分布
            pathway_stats = population.statistics.get_pathway_distribution()
            adenoma_ratio = pathway_stats.get("adenoma_carcinoma", 0) / 500
            assert 0.75 < adenoma_ratio < 0.85  # 允许5%误差
            
            # 验证生成摘要
            summary = generator.get_last_generation_summary()
            assert summary is not None
            assert summary.total_individuals == 500
            assert summary.config_used["size"] == 500
            
        finally:
            Path(temp_path).unlink()
    
    def test_predefined_config_files(self):
        """测试预定义配置文件"""
        config_manager = PopulationConfig()
        
        # 测试中国成人筛查人群配置
        china_config_path = Path("data/population_configs/china_adult_screening.yaml")
        if china_config_path.exists():
            config = config_manager.load_config(china_config_path)
            
            assert config.name == "中国成人筛查人群"
            assert config.size == 100000
            assert config.age_distribution.type == "normal"
            assert config.age_distribution.mean == 62.5
            assert config.age_distribution.min_age == 50
            assert config.age_distribution.max_age == 75
            
            # 生成小规模测试人群
            generator = PopulationGenerator(random_seed=42)
            test_params = config.to_generation_params()
            test_params["size"] = 100  # 减小规模用于测试
            
            population = generator.generate_population(**test_params, show_progress=False)
            assert population.get_size() == 100
        
        # 测试小规模测试人群配置
        test_config_path = Path("data/population_configs/small_test_population.yaml")
        if test_config_path.exists():
            config = config_manager.load_config(test_config_path)
            
            assert config.name == "小规模测试人群"
            assert config.size == 1000
            assert config.age_distribution.type == "uniform"
            
            # 生成人群
            generator = PopulationGenerator(random_seed=config.random_seed)
            generation_params = config.to_generation_params()
            population = generator.generate_population(**generation_params, show_progress=False)
            
            assert population.get_size() == 1000
            
            # 验证均匀分布特性
            ages = [ind.get_current_age() for ind in population]
            assert all(40 <= age <= 80 for age in ages)
    
    def test_batch_generation_with_multiple_configs(self):
        """测试使用多个配置进行批量生成"""
        # 创建多个配置文件
        configs_data = [
            {
                "population_config": {
                    "name": "年轻人群",
                    "description": "30-50岁年轻人群",
                    "size": 200,
                    "age_distribution": {
                        "type": "uniform",
                        "min_age": 30,
                        "max_age": 50
                    },
                    "gender_distribution": {
                        "male_ratio": 0.5,
                        "female_ratio": 0.5
                    }
                }
            },
            {
                "population_config": {
                    "name": "老年人群",
                    "description": "60-80岁老年人群",
                    "size": 300,
                    "age_distribution": {
                        "type": "normal",
                        "mean": 70,
                        "std": 5,
                        "min_age": 60,
                        "max_age": 80
                    },
                    "gender_distribution": {
                        "male_ratio": 0.4,
                        "female_ratio": 0.6
                    }
                }
            }
        ]
        
        temp_paths = []
        loaded_configs = []
        
        try:
            # 创建临时配置文件
            config_manager = PopulationConfig()
            
            for i, config_data in enumerate(configs_data):
                with tempfile.NamedTemporaryFile(mode='w', suffix=f'_config_{i}.yaml', delete=False) as f:
                    yaml.dump(config_data, f)
                    temp_paths.append(f.name)
                
                # 加载配置
                config = config_manager.load_config(f.name)
                loaded_configs.append(config)
            
            # 准备批量生成参数
            batch_params = []
            for config in loaded_configs:
                params = config.to_generation_params()
                params["show_progress"] = False
                batch_params.append(params)
            
            # 批量生成人群
            generator = PopulationGenerator(random_seed=42)
            populations = generator.generate_batch_populations(batch_params, show_progress=False)
            
            # 验证结果
            assert len(populations) == 2
            
            # 验证年轻人群
            young_pop = populations[0]
            assert young_pop.get_size() == 200
            young_ages = [ind.get_current_age() for ind in young_pop]
            assert all(30 <= age <= 50 for age in young_ages)
            
            # 验证老年人群
            old_pop = populations[1]
            assert old_pop.get_size() == 300
            old_ages = [ind.get_current_age() for ind in old_pop]
            assert all(60 <= age <= 80 for age in old_ages)
            
            # 验证性别分布差异
            young_gender = young_pop.statistics.get_gender_distribution()
            old_gender = old_pop.statistics.get_gender_distribution()
            
            young_male_ratio = young_gender["male"] / 200
            old_male_ratio = old_gender["male"] / 300
            
            # 年轻人群男女比例应该接近50%
            assert 0.45 < young_male_ratio < 0.55
            # 老年人群男性比例应该更低（接近40%）
            assert 0.35 < old_male_ratio < 0.45
            
        finally:
            # 清理临时文件
            for temp_path in temp_paths:
                Path(temp_path).unlink()
    
    def test_config_modification_and_regeneration(self):
        """测试配置修改和重新生成"""
        # 创建初始配置
        config_manager = PopulationConfig()
        initial_config = config_manager.create_default_config(
            name="可修改配置",
            description="用于测试修改的配置"
        )
        
        # 修改配置参数
        initial_config.size = 150
        initial_config.age_distribution.mean = 55.0
        initial_config.age_distribution.std = 8.0
        initial_config.gender_distribution.male_ratio = 0.6
        initial_config.gender_distribution.female_ratio = 0.4
        
        # 生成初始人群
        generator = PopulationGenerator(random_seed=42)
        initial_params = initial_config.to_generation_params()
        initial_population = generator.generate_population(**initial_params, show_progress=False)
        
        # 验证初始人群
        assert initial_population.get_size() == 150
        initial_gender_stats = initial_population.statistics.get_gender_distribution()
        initial_male_ratio = initial_gender_stats["male"] / 150
        
        # 修改配置并重新生成
        modified_config = initial_config
        modified_config.size = 200
        modified_config.gender_distribution.male_ratio = 0.3
        modified_config.gender_distribution.female_ratio = 0.7
        
        modified_params = modified_config.to_generation_params()
        modified_population = generator.generate_population(**modified_params, show_progress=False)
        
        # 验证修改后的人群
        assert modified_population.get_size() == 200
        modified_gender_stats = modified_population.statistics.get_gender_distribution()
        modified_male_ratio = modified_gender_stats["male"] / 200
        
        # 验证性别比例确实发生了变化
        assert abs(initial_male_ratio - modified_male_ratio) > 0.2  # 应该有显著差异
        assert modified_male_ratio < 0.4  # 修改后的男性比例应该更低
    
    def test_large_scale_generation_performance(self):
        """测试大规模人群生成性能"""
        config_manager = PopulationConfig()
        
        # 创建大规模配置
        large_config = config_manager.create_default_config(
            name="大规模性能测试",
            description="用于性能测试的大规模人群"
        )
        large_config.size = 50000  # 5万人群
        
        # 生成大规模人群
        generator = PopulationGenerator(random_seed=42)
        generation_params = large_config.to_generation_params()
        
        import time
        start_time = time.time()
        
        population = generator.generate_population(**generation_params, show_progress=False)
        
        generation_time = time.time() - start_time
        
        # 验证结果
        assert population.get_size() == 50000
        assert generation_time < 30  # 应该在30秒内完成
        
        # 验证统计特性
        summary = generator.get_last_generation_summary()
        assert abs(summary.generation_time - generation_time) < 0.1  # 允许小的时间差异
        assert summary.total_individuals == 50000
        
        # 验证人群质量
        stats = population.statistics.get_summary()
        age_stats = stats["age_statistics"]
        
        # 年龄统计应该接近配置值
        assert 60 < age_stats["mean"] < 65
        assert 6 < age_stats["std"] < 9
    
    def test_error_handling_in_integration(self):
        """测试集成过程中的错误处理"""
        # 测试配置文件错误
        invalid_config_data = {
            "population_config": {
                "name": "错误配置",
                "size": -100,  # 无效规模
                "age_distribution": {
                    "type": "normal",
                    "mean": 50,
                    "std": -5  # 无效标准差
                }
            }
        }
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            yaml.dump(invalid_config_data, f)
            temp_path = f.name
        
        try:
            config_manager = PopulationConfig()
            
            # 应该在加载配置时就发现错误
            with pytest.raises(Exception):  # 可能是ValidationError或ParameterValidationError
                config_manager.load_config(temp_path)
                
        finally:
            Path(temp_path).unlink()
    
    def test_reproducibility_across_sessions(self):
        """测试跨会话的可重现性"""
        # 创建配置
        config_data = {
            "population_config": {
                "name": "可重现性测试",
                "description": "测试跨会话可重现性",
                "size": 100,
                "random_seed": 12345,
                "age_distribution": {
                    "type": "normal",
                    "mean": 50,
                    "std": 10,
                    "min_age": 30,
                    "max_age": 70
                },
                "gender_distribution": {
                    "male_ratio": 0.6,
                    "female_ratio": 0.4
                }
            }
        }
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            yaml.dump(config_data, f)
            temp_path = f.name
        
        try:
            # 第一次生成
            config_manager1 = PopulationConfig()
            config1 = config_manager1.load_config(temp_path)
            generator1 = PopulationGenerator(random_seed=config1.random_seed)
            population1 = generator1.generate_population(**config1.to_generation_params(), show_progress=False)
            
            # 第二次生成（模拟新会话）
            config_manager2 = PopulationConfig()
            config2 = config_manager2.load_config(temp_path)
            generator2 = PopulationGenerator(random_seed=config2.random_seed)
            population2 = generator2.generate_population(**config2.to_generation_params(), show_progress=False)
            
            # 验证结果一致性
            ages1 = sorted([ind.get_current_age() for ind in population1])
            ages2 = sorted([ind.get_current_age() for ind in population2])
            
            # 年龄分布应该完全一致
            for age1, age2 in zip(ages1, ages2):
                assert abs(age1 - age2) < 0.01
            
            # 性别分布应该一致
            gender_stats1 = population1.statistics.get_gender_distribution()
            gender_stats2 = population2.statistics.get_gender_distribution()
            assert gender_stats1 == gender_stats2
            
        finally:
            Path(temp_path).unlink()
