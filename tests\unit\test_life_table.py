"""
生命表模块单元测试

测试LifeTable类的数据加载、验证和查询功能。
"""

import pytest
import pandas as pd
import numpy as np
from pathlib import Path
from unittest.mock import patch, mock_open
import tempfile
import os

from src.modules.population.life_table import LifeTable, LifeTableData, LifeTableType
from src.core import Gender
from src.utils import ValidationError


class TestLifeTableData:
    """测试LifeTableData数据结构"""
    
    def test_valid_life_table_data(self):
        """测试有效的生命表数据"""
        data = pd.DataFrame({
            'age': [0, 1, 2],
            'gender': ['male', 'female', 'male'],
            'mortality_rate': [0.005, 0.004, 0.003]
        })
        
        life_table_data = LifeTableData(
            name="test_table",
            description="测试生命表",
            source="test",
            year=2020,
            country="Test",
            data=data
        )
        
        assert life_table_data.name == "test_table"
        assert life_table_data.year == 2020
        assert len(life_table_data.data) == 3
    
    def test_invalid_data_type(self):
        """测试无效的数据类型"""
        with pytest.raises(ValidationError, match="生命表数据必须是pandas DataFrame"):
            LifeTableData(
                name="test",
                description="test",
                source="test",
                year=2020,
                country="test",
                data="invalid_data"
            )
    
    def test_missing_columns(self):
        """测试缺少必需列"""
        data = pd.DataFrame({
            'age': [0, 1, 2],
            'gender': ['male', 'female', 'male']
            # 缺少 mortality_rate 列
        })
        
        with pytest.raises(ValidationError, match="生命表数据缺少必需列"):
            LifeTableData(
                name="test",
                description="test",
                source="test",
                year=2020,
                country="test",
                data=data
            )
    
    def test_invalid_age_range(self):
        """测试无效的年龄范围"""
        data = pd.DataFrame({
            'age': [-1, 150, 2],  # 无效年龄
            'gender': ['male', 'female', 'male'],
            'mortality_rate': [0.005, 0.004, 0.003]
        })
        
        with pytest.raises(ValidationError, match="年龄范围必须在0-120之间"):
            LifeTableData(
                name="test",
                description="test",
                source="test",
                year=2020,
                country="test",
                data=data
            )
    
    def test_invalid_mortality_rate_range(self):
        """测试无效的死亡率范围"""
        data = pd.DataFrame({
            'age': [0, 1, 2],
            'gender': ['male', 'female', 'male'],
            'mortality_rate': [0.005, 1.5, 0.003]  # 死亡率超出范围
        })
        
        with pytest.raises(ValidationError, match="死亡率必须在0-1之间"):
            LifeTableData(
                name="test",
                description="test",
                source="test",
                year=2020,
                country="test",
                data=data
            )
    
    def test_invalid_gender_values(self):
        """测试无效的性别值"""
        data = pd.DataFrame({
            'age': [0, 1, 2],
            'gender': ['male', 'invalid', 'male'],  # 无效性别值
            'mortality_rate': [0.005, 0.004, 0.003]
        })
        
        with pytest.raises(ValidationError, match="无效的性别值"):
            LifeTableData(
                name="test",
                description="test",
                source="test",
                year=2020,
                country="test",
                data=data
            )


class TestLifeTable:
    """测试LifeTable类"""
    
    @pytest.fixture
    def sample_csv_data(self):
        """创建示例CSV数据"""
        return """age,gender,mortality_rate
0,male,0.005
0,female,0.004
1,male,0.003
1,female,0.002
50,male,0.010
50,female,0.008
100,male,0.200
100,female,0.180"""
    
    @pytest.fixture
    def life_table(self):
        """创建LifeTable实例"""
        return LifeTable()
    
    def test_initialization(self, life_table):
        """测试初始化"""
        assert life_table.data_dir == Path("data/life_tables")
        assert len(life_table._loaded_tables) == 0
        assert life_table._current_table is None
    
    def test_custom_data_dir(self):
        """测试自定义数据目录"""
        custom_dir = Path("custom/life_tables")
        life_table = LifeTable(data_dir=custom_dir)
        assert life_table.data_dir == custom_dir
    
    def test_load_csv_data(self, life_table, sample_csv_data):
        """测试加载CSV数据"""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False) as f:
            f.write(sample_csv_data)
            temp_path = f.name
        
        try:
            life_table_data = life_table.load_life_table(temp_path)
            
            assert life_table_data.name == Path(temp_path).stem
            assert len(life_table_data.data) == 8
            assert 'age' in life_table_data.data.columns
            assert 'gender' in life_table_data.data.columns
            assert 'mortality_rate' in life_table_data.data.columns
            
            # 验证当前表已设置
            assert life_table._current_table == life_table_data
            
        finally:
            os.unlink(temp_path)
    
    def test_load_nonexistent_file(self, life_table):
        """测试加载不存在的文件"""
        with pytest.raises(FileNotFoundError):
            life_table.load_life_table("nonexistent_file.csv")
    
    def test_unsupported_file_format(self, life_table):
        """测试不支持的文件格式"""
        with tempfile.NamedTemporaryFile(suffix='.txt') as f:
            with pytest.raises(ValidationError, match="不支持的文件格式"):
                life_table.load_life_table(f.name)
    
    def test_standardize_gender_values(self, life_table):
        """测试性别值标准化"""
        csv_data = """age,gender,mortality_rate
0,M,0.005
1,F,0.004
2,Male,0.003
3,Female,0.002"""
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False) as f:
            f.write(csv_data)
            temp_path = f.name
        
        try:
            life_table_data = life_table.load_life_table(temp_path)
            
            # 验证性别值已标准化
            genders = life_table_data.data['gender'].unique()
            assert set(genders) == {'male', 'female'}
            
        finally:
            os.unlink(temp_path)
    
    def test_get_mortality_rate_exact_match(self, life_table, sample_csv_data):
        """测试精确匹配的死亡率查询"""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False) as f:
            f.write(sample_csv_data)
            temp_path = f.name
        
        try:
            life_table.load_life_table(temp_path)
            
            # 测试精确匹配
            rate = life_table.get_mortality_rate(50, "male")
            assert rate == 0.010
            
            rate = life_table.get_mortality_rate(50, Gender.FEMALE)
            assert rate == 0.008
            
        finally:
            os.unlink(temp_path)
    
    def test_get_mortality_rate_interpolation(self, life_table, sample_csv_data):
        """测试插值计算的死亡率查询"""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False) as f:
            f.write(sample_csv_data)
            temp_path = f.name
        
        try:
            life_table.load_life_table(temp_path)
            
            # 测试插值（25岁，在1岁和50岁之间）
            rate = life_table.get_mortality_rate(25, "male")
            
            # 验证插值结果在合理范围内
            assert 0.003 < rate < 0.010
            
        finally:
            os.unlink(temp_path)
    
    def test_get_mortality_rate_boundary_values(self, life_table, sample_csv_data):
        """测试边界值的死亡率查询"""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False) as f:
            f.write(sample_csv_data)
            temp_path = f.name
        
        try:
            life_table.load_life_table(temp_path)
            
            # 测试低于最小年龄
            rate = life_table.get_mortality_rate(-5, "male")
            assert rate == 0.005  # 应该返回最小年龄的值
            
            # 测试高于最大年龄
            rate = life_table.get_mortality_rate(150, "male")
            assert rate == 0.200  # 应该返回最大年龄的值
            
        finally:
            os.unlink(temp_path)
    
    def test_get_mortality_rate_invalid_age(self, life_table, sample_csv_data):
        """测试无效年龄的死亡率查询"""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False) as f:
            f.write(sample_csv_data)
            temp_path = f.name
        
        try:
            life_table.load_life_table(temp_path)
            
            with pytest.raises(ValidationError, match="年龄必须在0-120之间"):
                life_table.get_mortality_rate(-10, "male")
            
            with pytest.raises(ValidationError, match="年龄必须在0-120之间"):
                life_table.get_mortality_rate(200, "male")
            
        finally:
            os.unlink(temp_path)
    
    def test_get_mortality_rate_invalid_gender(self, life_table, sample_csv_data):
        """测试无效性别的死亡率查询"""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False) as f:
            f.write(sample_csv_data)
            temp_path = f.name
        
        try:
            life_table.load_life_table(temp_path)
            
            with pytest.raises(ValidationError, match="无效的性别值"):
                life_table.get_mortality_rate(50, "invalid")
            
        finally:
            os.unlink(temp_path)
    
    def test_get_mortality_rate_no_table_loaded(self, life_table):
        """测试没有加载生命表时的查询"""
        with pytest.raises(ValueError, match="没有可用的生命表"):
            life_table.get_mortality_rate(50, "male")
    
    def test_get_survival_probability(self, life_table, sample_csv_data):
        """测试生存概率计算"""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False) as f:
            f.write(sample_csv_data)
            temp_path = f.name
        
        try:
            life_table.load_life_table(temp_path)
            
            survival_prob = life_table.get_survival_probability(50, "male")
            assert survival_prob == 0.990  # 1 - 0.010
            
        finally:
            os.unlink(temp_path)
    
    def test_get_life_expectancy(self, life_table, sample_csv_data):
        """测试预期寿命计算"""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False) as f:
            f.write(sample_csv_data)
            temp_path = f.name
        
        try:
            life_table.load_life_table(temp_path)
            
            life_expectancy = life_table.get_life_expectancy(50, "male")
            assert life_expectancy > 0
            assert isinstance(life_expectancy, float)
            
        finally:
            os.unlink(temp_path)
    
    def test_table_management(self, life_table, sample_csv_data):
        """测试生命表管理功能"""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False) as f:
            f.write(sample_csv_data)
            temp_path = f.name
        
        try:
            # 加载生命表
            life_table.load_life_table(temp_path, table_name="test_table")
            
            # 验证表已加载
            available_tables = life_table.get_available_tables()
            assert "test_table" in available_tables
            
            # 验证当前表
            current_table = life_table.get_current_table_name()
            assert current_table == "test_table"
            
            # 设置当前表
            life_table.set_current_table("test_table")
            assert life_table.get_current_table_name() == "test_table"
            
        finally:
            os.unlink(temp_path)
    
    def test_validate_table_data(self, life_table, sample_csv_data):
        """测试生命表数据验证"""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False) as f:
            f.write(sample_csv_data)
            temp_path = f.name
        
        try:
            life_table.load_life_table(temp_path)
            
            validation_result = life_table.validate_table_data()
            
            assert "table_name" in validation_result
            assert "total_records" in validation_result
            assert "age_range" in validation_result
            assert "genders" in validation_result
            assert "mortality_rate_range" in validation_result
            assert "missing_values" in validation_result
            assert "data_completeness" in validation_result
            assert "issues" in validation_result
            
            assert validation_result["total_records"] == 8
            assert set(validation_result["genders"]) == {"male", "female"}
            
        finally:
            os.unlink(temp_path)
