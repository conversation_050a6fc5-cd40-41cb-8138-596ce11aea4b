"""
生命表数据管理模块

实现LifeTable类，用于加载、验证和管理生命表数据。
支持CSV格式数据解析和多个生命表支持。
"""

import csv
import json
import numpy as np
import pandas as pd
from pathlib import Path
from typing import Dict, Any, Optional, Union, List, Tuple
from dataclasses import dataclass
from enum import Enum

from ...core import Gender
from ...utils import ValidationError, ParameterValidationError


class LifeTableType(Enum):
    """生命表类型枚举"""
    CHINA_2020 = "china_2020"
    WHO_GLOBAL = "who_global"
    CUSTOM = "custom"


@dataclass
class LifeTableData:
    """生命表数据结构"""
    name: str
    description: str
    source: str
    year: int
    country: str
    data: pd.DataFrame  # 包含age, gender, mortality_rate列
    
    def __post_init__(self):
        """初始化后验证数据"""
        self._validate_data()
    
    def _validate_data(self):
        """验证生命表数据格式"""
        required_columns = ['age', 'gender', 'mortality_rate']
        
        if not isinstance(self.data, pd.DataFrame):
            raise ValidationError("生命表数据必须是pandas DataFrame")
        
        missing_columns = set(required_columns) - set(self.data.columns)
        if missing_columns:
            raise ValidationError(f"生命表数据缺少必需列: {missing_columns}")
        
        # 验证数据类型和范围
        if not pd.api.types.is_numeric_dtype(self.data['age']):
            raise ValidationError("年龄列必须是数值类型")
        
        if not pd.api.types.is_numeric_dtype(self.data['mortality_rate']):
            raise ValidationError("死亡率列必须是数值类型")
        
        # 验证年龄范围
        if self.data['age'].min() < 0 or self.data['age'].max() > 120:
            raise ValidationError("年龄范围必须在0-120之间")
        
        # 验证死亡率范围
        if self.data['mortality_rate'].min() < 0 or self.data['mortality_rate'].max() > 1:
            raise ValidationError("死亡率必须在0-1之间")
        
        # 验证性别值
        valid_genders = {'male', 'female', 'M', 'F', 'm', 'f'}
        invalid_genders = set(self.data['gender'].unique()) - valid_genders
        if invalid_genders:
            raise ValidationError(f"无效的性别值: {invalid_genders}")


class LifeTable:
    """
    生命表管理类
    
    负责加载、验证和管理生命表数据，
    提供年龄和性别特异性死亡率查询功能。
    """
    
    def __init__(self, data_dir: Optional[Union[str, Path]] = None):
        """
        初始化生命表管理器
        
        Args:
            data_dir: 生命表数据目录，默认为data/life_tables/
        """
        if data_dir is None:
            data_dir = Path("data/life_tables")
        
        self.data_dir = Path(data_dir)
        self._loaded_tables: Dict[str, LifeTableData] = {}
        self._current_table: Optional[LifeTableData] = None
        
        # 创建数据目录（如果不存在）
        self.data_dir.mkdir(parents=True, exist_ok=True)
    
    def load_life_table(
        self,
        file_path: Union[str, Path],
        table_name: Optional[str] = None,
        set_as_current: bool = True
    ) -> LifeTableData:
        """
        加载生命表数据
        
        Args:
            file_path: 生命表文件路径
            table_name: 生命表名称，默认使用文件名
            set_as_current: 是否设置为当前使用的生命表
            
        Returns:
            加载的生命表数据
            
        Raises:
            ValidationError: 数据验证失败时
            FileNotFoundError: 文件不存在时
        """
        file_path = Path(file_path)
        
        if not file_path.exists():
            raise FileNotFoundError(f"生命表文件不存在: {file_path}")
        
        if table_name is None:
            table_name = file_path.stem
        
        # 根据文件扩展名选择加载方法
        if file_path.suffix.lower() == '.csv':
            data = self._load_csv_data(file_path)
        elif file_path.suffix.lower() in ['.xlsx', '.xls']:
            data = self._load_excel_data(file_path)
        else:
            raise ValidationError(f"不支持的文件格式: {file_path.suffix}")
        
        # 标准化性别值
        data = self._standardize_gender_values(data)
        
        # 创建生命表数据对象
        life_table_data = LifeTableData(
            name=table_name,
            description=f"从{file_path.name}加载的生命表",
            source=str(file_path),
            year=2020,  # 默认年份，可以从文件名或元数据中提取
            country="Unknown",  # 默认国家，可以从文件名或元数据中提取
            data=data
        )
        
        # 缓存生命表数据
        self._loaded_tables[table_name] = life_table_data
        
        if set_as_current:
            self._current_table = life_table_data
        
        return life_table_data
    
    def _load_csv_data(self, file_path: Path) -> pd.DataFrame:
        """加载CSV格式的生命表数据"""
        try:
            data = pd.read_csv(file_path)
            return data
        except Exception as e:
            raise ValidationError(f"加载CSV文件失败: {e}")
    
    def _load_excel_data(self, file_path: Path) -> pd.DataFrame:
        """加载Excel格式的生命表数据"""
        try:
            data = pd.read_excel(file_path)
            return data
        except Exception as e:
            raise ValidationError(f"加载Excel文件失败: {e}")
    
    def _standardize_gender_values(self, data: pd.DataFrame) -> pd.DataFrame:
        """标准化性别值"""
        data = data.copy()
        
        # 性别值映射
        gender_mapping = {
            'M': 'male', 'm': 'male', 'Male': 'male', 'MALE': 'male',
            'F': 'female', 'f': 'female', 'Female': 'female', 'FEMALE': 'female'
        }
        
        data['gender'] = data['gender'].replace(gender_mapping)
        return data
    
    def get_mortality_rate(
        self,
        age: float,
        gender: Union[Gender, str],
        table_name: Optional[str] = None
    ) -> float:
        """
        获取特定年龄和性别的死亡率
        
        Args:
            age: 年龄
            gender: 性别
            table_name: 生命表名称，默认使用当前生命表
            
        Returns:
            死亡率
            
        Raises:
            ValidationError: 参数无效时
            ValueError: 没有可用的生命表时
        """
        # 获取生命表
        life_table = self._get_table(table_name)
        
        # 标准化性别值
        gender_str = self._standardize_gender(gender)
        
        # 验证年龄范围
        if age < 0 or age > 120:
            raise ValidationError(f"年龄必须在0-120之间，当前值: {age}")
        
        # 查询死亡率
        return self._query_mortality_rate(life_table, age, gender_str)
    
    def _get_table(self, table_name: Optional[str] = None) -> LifeTableData:
        """获取指定的生命表"""
        if table_name is not None:
            if table_name not in self._loaded_tables:
                raise ValueError(f"生命表'{table_name}'未加载")
            return self._loaded_tables[table_name]
        
        if self._current_table is None:
            raise ValueError("没有可用的生命表，请先加载生命表数据")
        
        return self._current_table
    
    def _standardize_gender(self, gender: Union[Gender, str]) -> str:
        """标准化性别值"""
        if isinstance(gender, Gender):
            return gender.value
        
        gender_str = str(gender).lower()
        if gender_str in ['male', 'm']:
            return 'male'
        elif gender_str in ['female', 'f']:
            return 'female'
        else:
            raise ValidationError(f"无效的性别值: {gender}")
    
    def _query_mortality_rate(
        self,
        life_table: LifeTableData,
        age: float,
        gender: str
    ) -> float:
        """查询死亡率，支持插值"""
        data = life_table.data
        
        # 筛选性别数据
        gender_data = data[data['gender'] == gender].copy()
        
        if gender_data.empty:
            raise ValidationError(f"生命表中没有性别'{gender}'的数据")
        
        # 按年龄排序
        gender_data = gender_data.sort_values('age')
        
        # 精确匹配
        exact_match = gender_data[gender_data['age'] == age]
        if not exact_match.empty:
            return float(exact_match['mortality_rate'].iloc[0])
        
        # 插值计算
        return self._interpolate_mortality_rate(gender_data, age)
    
    def _interpolate_mortality_rate(
        self,
        gender_data: pd.DataFrame,
        age: float
    ) -> float:
        """插值计算死亡率"""
        ages = gender_data['age'].values
        rates = gender_data['mortality_rate'].values
        
        # 如果年龄超出范围，使用边界值
        if age <= ages.min():
            return float(rates[0])
        if age >= ages.max():
            return float(rates[-1])
        
        # 线性插值
        interpolated_rate = np.interp(age, ages, rates)
        return float(interpolated_rate)

    def get_survival_probability(
        self,
        age: float,
        gender: Union[Gender, str],
        table_name: Optional[str] = None
    ) -> float:
        """
        获取特定年龄和性别的生存概率

        Args:
            age: 年龄
            gender: 性别
            table_name: 生命表名称

        Returns:
            生存概率 (1 - 死亡率)
        """
        mortality_rate = self.get_mortality_rate(age, gender, table_name)
        return 1.0 - mortality_rate

    def get_life_expectancy(
        self,
        age: float,
        gender: Union[Gender, str],
        table_name: Optional[str] = None
    ) -> float:
        """
        计算特定年龄和性别的预期寿命

        Args:
            age: 当前年龄
            gender: 性别
            table_name: 生命表名称

        Returns:
            预期寿命（年）
        """
        life_table = self._get_table(table_name)
        gender_str = self._standardize_gender(gender)

        # 获取该性别的所有年龄数据
        gender_data = life_table.data[life_table.data['gender'] == gender_str].copy()
        gender_data = gender_data.sort_values('age')

        # 计算从当前年龄开始的预期寿命
        total_years = 0.0
        survival_prob = 1.0

        current_age = int(age)
        max_age = int(gender_data['age'].max())

        for year in range(current_age, max_age + 1):
            mortality_rate = self.get_mortality_rate(year, gender_str, table_name)
            survival_prob *= (1.0 - mortality_rate)
            total_years += survival_prob

        return total_years

    def get_available_tables(self) -> List[str]:
        """获取已加载的生命表列表"""
        return list(self._loaded_tables.keys())

    def set_current_table(self, table_name: str):
        """设置当前使用的生命表"""
        if table_name not in self._loaded_tables:
            raise ValueError(f"生命表'{table_name}'未加载")

        self._current_table = self._loaded_tables[table_name]

    def get_current_table_name(self) -> Optional[str]:
        """获取当前生命表名称"""
        return self._current_table.name if self._current_table else None

    def validate_table_data(self, table_name: Optional[str] = None) -> Dict[str, Any]:
        """
        验证生命表数据完整性

        Args:
            table_name: 生命表名称

        Returns:
            验证结果字典
        """
        life_table = self._get_table(table_name)
        data = life_table.data

        validation_result = {
            "table_name": life_table.name,
            "total_records": len(data),
            "age_range": {
                "min": float(data['age'].min()),
                "max": float(data['age'].max())
            },
            "genders": sorted(data['gender'].unique().tolist()),
            "mortality_rate_range": {
                "min": float(data['mortality_rate'].min()),
                "max": float(data['mortality_rate'].max())
            },
            "missing_values": {
                "age": int(data['age'].isnull().sum()),
                "gender": int(data['gender'].isnull().sum()),
                "mortality_rate": int(data['mortality_rate'].isnull().sum())
            },
            "data_completeness": {},
            "issues": []
        }

        # 检查数据完整性
        for gender in validation_result["genders"]:
            gender_data = data[data['gender'] == gender]
            age_coverage = len(gender_data)
            expected_ages = int(validation_result["age_range"]["max"] -
                              validation_result["age_range"]["min"] + 1)

            validation_result["data_completeness"][gender] = {
                "records": age_coverage,
                "expected_ages": expected_ages,
                "coverage_ratio": age_coverage / expected_ages if expected_ages > 0 else 0
            }

            # 检查年龄连续性
            ages = sorted(gender_data['age'].unique())
            if len(ages) > 1:
                age_gaps = []
                for i in range(1, len(ages)):
                    if ages[i] - ages[i-1] > 1:
                        age_gaps.append((ages[i-1], ages[i]))

                if age_gaps:
                    validation_result["issues"].append(
                        f"性别'{gender}'存在年龄间隙: {age_gaps}"
                    )

        # 检查异常值
        high_mortality = data[data['mortality_rate'] > 0.5]
        if not high_mortality.empty:
            validation_result["issues"].append(
                f"发现{len(high_mortality)}条异常高死亡率记录 (>0.5)"
            )

        return validation_result

    def smooth_mortality_rates(
        self,
        table_name: Optional[str] = None,
        window_size: int = 3,
        method: str = "moving_average"
    ) -> LifeTableData:
        """
        平滑死亡率数据

        Args:
            table_name: 生命表名称
            window_size: 平滑窗口大小
            method: 平滑方法 ("moving_average", "exponential")

        Returns:
            平滑后的生命表数据
        """
        life_table = self._get_table(table_name)
        data = life_table.data.copy()

        for gender in data['gender'].unique():
            gender_mask = data['gender'] == gender
            gender_data = data[gender_mask].copy().sort_values('age')

            if method == "moving_average":
                smoothed_rates = gender_data['mortality_rate'].rolling(
                    window=window_size, center=True, min_periods=1
                ).mean()
            elif method == "exponential":
                smoothed_rates = gender_data['mortality_rate'].ewm(
                    span=window_size
                ).mean()
            else:
                raise ValueError(f"不支持的平滑方法: {method}")

            data.loc[gender_mask, 'mortality_rate'] = smoothed_rates.values

        # 创建新的生命表数据
        smoothed_table = LifeTableData(
            name=f"{life_table.name}_smoothed",
            description=f"{life_table.description} (已平滑)",
            source=life_table.source,
            year=life_table.year,
            country=life_table.country,
            data=data
        )

        return smoothed_table

    def export_table_data(
        self,
        output_path: Union[str, Path],
        table_name: Optional[str] = None,
        format: str = "csv"
    ):
        """
        导出生命表数据

        Args:
            output_path: 输出文件路径
            table_name: 生命表名称
            format: 输出格式 ("csv", "excel", "json")
        """
        life_table = self._get_table(table_name)
        output_path = Path(output_path)

        if format.lower() == "csv":
            life_table.data.to_csv(output_path, index=False)
        elif format.lower() in ["excel", "xlsx"]:
            life_table.data.to_excel(output_path, index=False)
        elif format.lower() == "json":
            export_data = {
                "metadata": {
                    "name": life_table.name,
                    "description": life_table.description,
                    "source": life_table.source,
                    "year": life_table.year,
                    "country": life_table.country
                },
                "data": life_table.data.to_dict(orient="records")
            }
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, ensure_ascii=False, indent=2)
        else:
            raise ValueError(f"不支持的导出格式: {format}")

    def load_predefined_table(self, table_type: LifeTableType) -> LifeTableData:
        """
        加载预定义的生命表

        Args:
            table_type: 生命表类型

        Returns:
            加载的生命表数据
        """
        table_files = {
            LifeTableType.CHINA_2020: "china_2020.csv",
            LifeTableType.WHO_GLOBAL: "who_global.csv"
        }

        if table_type not in table_files:
            raise ValueError(f"不支持的生命表类型: {table_type}")

        file_path = self.data_dir / table_files[table_type]

        if not file_path.exists():
            raise FileNotFoundError(
                f"预定义生命表文件不存在: {file_path}\n"
                f"请确保数据文件已正确放置在 {self.data_dir} 目录中"
            )

        return self.load_life_table(file_path, table_type.value)
