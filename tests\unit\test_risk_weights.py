"""
风险因素权重配置系统测试

测试权重配置的加载、验证和计算功能。
"""

import pytest
import yaml
from pathlib import Path
from tempfile import TemporaryDirectory

from src.modules.disease.risk_weights import RiskFactorWeights, WeightConfig
from src.modules.disease.risk_factors import RiskFactorType


class TestWeightConfig:
    """测试权重配置数据类"""
    
    def test_create_weight_config(self):
        """测试创建权重配置"""
        config = WeightConfig(
            value=2.5,
            confidence_interval=(2.1, 3.0),
            reference="Test et al. 2023",
            description="Test weight",
            category="genetic",
            evidence_level="high"
        )
        
        assert config.value == 2.5
        assert config.confidence_interval == (2.1, 3.0)
        assert config.reference == "Test et al. 2023"
        assert config.description == "Test weight"
        assert config.category == "genetic"
        assert config.evidence_level == "high"
    
    def test_weight_config_validation(self):
        """测试权重配置验证"""
        # 正常配置
        config = WeightConfig(value=2.5)
        assert config.value == 2.5
        
        # 无效权重值
        with pytest.raises(ValueError, match="权重值必须大于0"):
            WeightConfig(value=0)
        
        with pytest.raises(ValueError, match="权重值必须大于0"):
            WeightConfig(value=-1.0)
    
    def test_confidence_interval_validation(self):
        """测试置信区间验证"""
        # 正常置信区间
        config = WeightConfig(value=2.5, confidence_interval=(2.1, 3.0))
        assert config.confidence_interval == (2.1, 3.0)
        
        # 无效置信区间长度
        with pytest.raises(ValueError, match="置信区间必须包含两个值"):
            WeightConfig(value=2.5, confidence_interval=(2.1,))
        
        # 无效置信区间顺序
        with pytest.raises(ValueError, match="置信区间下限必须小于上限"):
            WeightConfig(value=2.5, confidence_interval=(3.0, 2.1))


class TestRiskFactorWeights:
    """测试风险因素权重管理类"""
    
    def test_load_default_config(self):
        """测试加载默认配置"""
        weights = RiskFactorWeights()
        
        # 验证基本配置加载
        assert len(weights.weights) > 0
        assert weights.metadata.get("version") is not None
        
        # 验证必需的风险因素权重
        assert weights.has_weight(RiskFactorType.FAMILY_HISTORY)
        assert weights.has_weight(RiskFactorType.IBD)
        assert weights.has_weight(RiskFactorType.BMI)
        assert weights.has_weight(RiskFactorType.DIABETES)
        assert weights.has_weight(RiskFactorType.SMOKING)
    
    def test_get_weight(self):
        """测试获取权重配置"""
        weights = RiskFactorWeights()
        
        # 获取存在的权重
        family_history_weight = weights.get_weight(RiskFactorType.FAMILY_HISTORY)
        assert family_history_weight is not None
        assert isinstance(family_history_weight, WeightConfig)
        assert family_history_weight.value > 0
        
        # 获取不存在的权重
        # 注意：所有定义的风险因素都应该有权重，所以这里测试一个假设不存在的情况
        # 实际上默认配置应该包含所有风险因素
    
    def test_get_weight_value(self):
        """测试获取权重值"""
        weights = RiskFactorWeights()
        
        # 获取存在的权重值
        family_history_value = weights.get_weight_value(RiskFactorType.FAMILY_HISTORY)
        assert isinstance(family_history_value, float)
        assert family_history_value > 0
        
        # 对于不存在的权重，应该返回默认值1.0
        # 由于默认配置包含所有风险因素，这里创建一个临时的空配置来测试
        empty_weights = RiskFactorWeights.__new__(RiskFactorWeights)
        empty_weights.weights = {}
        default_value = empty_weights.get_weight_value(RiskFactorType.FAMILY_HISTORY)
        assert default_value == 1.0
    
    def test_has_weight(self):
        """测试检查权重存在"""
        weights = RiskFactorWeights()
        
        assert weights.has_weight(RiskFactorType.FAMILY_HISTORY) is True
        assert weights.has_weight(RiskFactorType.BMI) is True
    
    def test_get_all_weights(self):
        """测试获取所有权重"""
        weights = RiskFactorWeights()
        
        all_weights = weights.get_all_weights()
        
        assert isinstance(all_weights, dict)
        assert len(all_weights) > 0
        
        # 验证返回的是副本
        original_count = len(weights.weights)
        all_weights.clear()
        assert len(weights.weights) == original_count
    
    def test_calculate_bmi_weight(self):
        """测试BMI权重计算"""
        weights = RiskFactorWeights()
        
        # 测试不同BMI值的权重计算
        bmi_20 = weights.calculate_continuous_weight(RiskFactorType.BMI, 20.0)
        bmi_25 = weights.calculate_continuous_weight(RiskFactorType.BMI, 25.0)  # 基线
        bmi_30 = weights.calculate_continuous_weight(RiskFactorType.BMI, 30.0)
        
        # BMI 25是基线，应该是最低权重
        assert bmi_20 <= bmi_25  # 低于基线的BMI权重应该不高于基线
        assert bmi_30 > bmi_25   # 高于基线的BMI权重应该更高
        
        # 所有权重都应该是正数
        assert bmi_20 > 0
        assert bmi_25 > 0
        assert bmi_30 > 0
    
    def test_calculate_sedentary_weight(self):
        """测试久坐时间权重计算"""
        weights = RiskFactorWeights()
        
        # 测试不同久坐时间的权重计算
        sedentary_2 = weights.calculate_continuous_weight(RiskFactorType.SEDENTARY_LIFESTYLE, 2.0)
        sedentary_4 = weights.calculate_continuous_weight(RiskFactorType.SEDENTARY_LIFESTYLE, 4.0)  # 基线
        sedentary_8 = weights.calculate_continuous_weight(RiskFactorType.SEDENTARY_LIFESTYLE, 8.0)
        
        # 久坐时间越长，权重应该越高
        assert sedentary_2 <= sedentary_4
        assert sedentary_8 > sedentary_4
        
        # 所有权重都应该是正数
        assert sedentary_2 > 0
        assert sedentary_4 > 0
        assert sedentary_8 > 0
    
    def test_calculate_alcohol_weight(self):
        """测试酒精消费权重计算"""
        weights = RiskFactorWeights()
        
        # 测试不同酒精消费量的权重计算
        alcohol_0 = weights.calculate_continuous_weight(RiskFactorType.ALCOHOL_CONSUMPTION, 0.0)
        alcohol_5 = weights.calculate_continuous_weight(RiskFactorType.ALCOHOL_CONSUMPTION, 5.0)
        alcohol_10 = weights.calculate_continuous_weight(RiskFactorType.ALCOHOL_CONSUMPTION, 10.0)
        
        # 酒精消费越多，权重应该越高
        assert alcohol_0 <= alcohol_5
        assert alcohol_5 <= alcohol_10
        
        # 所有权重都应该是正数
        assert alcohol_0 > 0
        assert alcohol_5 > 0
        assert alcohol_10 > 0
    
    def test_calculate_diet_weight(self):
        """测试饮食质量权重计算"""
        weights = RiskFactorWeights()
        
        # 测试不同饮食质量评分的权重计算
        diet_30 = weights.calculate_continuous_weight(RiskFactorType.DIET_QUALITY, 30.0)  # 低质量
        diet_50 = weights.calculate_continuous_weight(RiskFactorType.DIET_QUALITY, 50.0)  # 基线
        diet_80 = weights.calculate_continuous_weight(RiskFactorType.DIET_QUALITY, 80.0)  # 高质量
        
        # 饮食质量越低，权重应该越高
        assert diet_30 > diet_50
        assert diet_80 <= diet_50
        
        # 所有权重都应该是正数
        assert diet_30 > 0
        assert diet_50 > 0
        assert diet_80 > 0
    
    def test_get_risk_stratification_thresholds(self):
        """测试获取风险分层阈值"""
        weights = RiskFactorWeights()
        
        thresholds = weights.get_risk_stratification_thresholds()
        
        assert "low_risk" in thresholds
        assert "moderate_risk_min" in thresholds
        assert "moderate_risk_max" in thresholds
        assert "high_risk" in thresholds
        
        # 验证阈值的逻辑关系
        assert thresholds["low_risk"] <= thresholds["moderate_risk_min"]
        assert thresholds["moderate_risk_min"] < thresholds["moderate_risk_max"]
        assert thresholds["moderate_risk_max"] <= thresholds["high_risk"]
    
    def test_get_age_adjustment_params(self):
        """测试获取年龄调整参数"""
        weights = RiskFactorWeights()
        
        age_params = weights.get_age_adjustment_params()
        
        assert isinstance(age_params, dict)
        # 验证返回的是副本
        original_enabled = age_params.get("enabled")
        age_params["enabled"] = not original_enabled
        
        new_age_params = weights.get_age_adjustment_params()
        assert new_age_params.get("enabled") == original_enabled
    
    def test_get_gender_adjustment_params(self):
        """测试获取性别调整参数"""
        weights = RiskFactorWeights()
        
        gender_params = weights.get_gender_adjustment_params()
        
        assert isinstance(gender_params, dict)
        # 验证返回的是副本
        original_male = gender_params.get("male_multiplier")
        gender_params["male_multiplier"] = 999.0
        
        new_gender_params = weights.get_gender_adjustment_params()
        assert new_gender_params.get("male_multiplier") == original_male
    
    def test_get_metadata(self):
        """测试获取元数据"""
        weights = RiskFactorWeights()
        
        metadata = weights.get_metadata()
        
        assert isinstance(metadata, dict)
        assert "version" in metadata
        
        # 验证返回的是副本
        original_version = metadata.get("version")
        metadata["version"] = "modified"
        
        new_metadata = weights.get_metadata()
        assert new_metadata.get("version") == original_version
    
    def test_custom_config_file(self):
        """测试使用自定义配置文件"""
        with TemporaryDirectory() as temp_dir:
            config_file = Path(temp_dir) / "custom_weights.yaml"
            
            # 创建自定义配置
            custom_config = {
                "risk_factor_weights": {
                    "version": "test-1.0",
                    "source": "Test Configuration",
                    "weights": {
                        "family_history": {
                            "value": 3.0,
                            "confidence_interval": [2.5, 3.5],
                            "reference": "Test Reference",
                            "category": "genetic",
                            "evidence_level": "high"
                        },
                        "body_mass_index": {
                            "value": 1.5,
                            "baseline": 25.0,
                            "per_unit_increase": 0.1,
                            "max_effect": 2.0,
                            "calculation_method": "continuous",
                            "category": "lifestyle"
                        }
                    }
                },
                "risk_stratification": {
                    "low_risk": {"threshold": 1.0},
                    "moderate_risk": {"threshold_min": 1.0, "threshold_max": 2.0},
                    "high_risk": {"threshold": 2.0}
                }
            }
            
            # 保存配置文件
            with open(config_file, 'w', encoding='utf-8') as f:
                yaml.dump(custom_config, f)
            
            # 加载自定义配置
            weights = RiskFactorWeights(config_file)
            
            # 验证配置加载
            assert weights.metadata["version"] == "test-1.0"
            assert weights.metadata["source"] == "Test Configuration"
            
            # 验证权重配置
            family_weight = weights.get_weight(RiskFactorType.FAMILY_HISTORY)
            assert family_weight.value == 3.0
            assert family_weight.confidence_interval == (2.5, 3.5)
            
            bmi_weight = weights.get_weight(RiskFactorType.BMI)
            assert bmi_weight.value == 1.5
            assert bmi_weight.calculation_method == "continuous"
            
            # 验证风险分层阈值
            thresholds = weights.get_risk_stratification_thresholds()
            assert thresholds["low_risk"] == 1.0
            assert thresholds["high_risk"] == 2.0
    
    def test_save_config(self):
        """测试保存配置"""
        with TemporaryDirectory() as temp_dir:
            # 加载默认配置
            weights = RiskFactorWeights()
            
            # 保存到新文件
            save_path = Path(temp_dir) / "saved_config.yaml"
            weights.save_config(save_path)
            
            assert save_path.exists()
            
            # 验证保存的配置可以重新加载
            new_weights = RiskFactorWeights(save_path)
            
            # 比较关键配置
            assert new_weights.metadata["version"] == weights.metadata["version"]
            assert len(new_weights.weights) == len(weights.weights)
            
            # 比较具体权重值
            for factor_type in weights.weights:
                original_value = weights.get_weight_value(factor_type)
                saved_value = new_weights.get_weight_value(factor_type)
                assert abs(original_value - saved_value) < 0.001  # 浮点数比较
    
    def test_reload_config(self):
        """测试重新加载配置"""
        weights = RiskFactorWeights()
        
        original_count = len(weights.weights)
        original_version = weights.metadata.get("version")
        
        # 清空权重（模拟配置损坏）
        weights.weights.clear()
        assert len(weights.weights) == 0
        
        # 重新加载
        weights.reload_config()
        
        # 验证配置恢复
        assert len(weights.weights) == original_count
        assert weights.metadata.get("version") == original_version
    
    def test_repr(self):
        """测试字符串表示"""
        weights = RiskFactorWeights()
        
        repr_str = repr(weights)
        
        assert "RiskFactorWeights" in repr_str
        assert "factors=" in repr_str
        assert "version=" in repr_str
