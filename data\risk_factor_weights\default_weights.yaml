# 风险因素权重配置文件
# 基于文献的默认权重值

risk_factor_weights:
  version: "1.0"
  source: "Meta-analysis and Literature Review 2023"
  description: "结直肠癌风险因素权重，基于中国人群和国际研究数据"
  last_updated: "2025-01-31"
  
  # 权重配置
  weights:
    # 遗传因素
    family_history:
      value: 2.5
      confidence_interval: [2.1, 3.0]
      reference: "Butterworth AS, et al. Relative and absolute risk of colorectal cancer for individuals with a family history: a meta-analysis. Eur J Cancer. 2006;42(2):216-27."
      description: "结直肠癌家族史相对风险"
      category: "genetic"
      evidence_level: "high"
    
    # 疾病相关因素
    inflammatory_bowel_disease:
      value: 4.2
      confidence_interval: [3.5, 5.1]
      reference: "<PERSON><PERSON><PERSON>, et al. The risk of colorectal cancer in ulcerative colitis: a meta-analysis. Gut. 2001;48(4):526-35."
      description: "炎症性肠病（IBD）相对风险"
      category: "disease_related"
      evidence_level: "high"
    
    diabetes_mellitus:
      value: 1.8
      confidence_interval: [1.5, 2.2]
      reference: "<PERSON><PERSON>, et al. Diabetes mellitus and risk of colorectal cancer: a meta-analysis. J Natl Cancer Inst. 2005;97(22):1679-87."
      description: "糖尿病相对风险"
      category: "disease_related"
      evidence_level: "moderate"
    
    # 生活方式因素
    body_mass_index:
      baseline: 25.0  # BMI基线值
      per_unit_increase: 0.05  # 每单位BMI增加的风险增幅
      max_effect: 1.8  # 最大风险倍数
      reference: "Moghaddam AA, et al. Obesity and risk of colorectal cancer: a meta-analysis of 31 studies with 70,000 events. Cancer Epidemiol Biomarkers Prev. 2007;16(12):2533-47."
      description: "体重指数（BMI）风险效应"
      category: "lifestyle"
      evidence_level: "high"
      calculation_method: "continuous"
      formula: "1 + (BMI - baseline) * per_unit_increase, capped at max_effect"
    
    smoking_status:
      value: 2.0
      confidence_interval: [1.7, 2.4]
      reference: "Botteri E, et al. Smoking and colorectal cancer: a meta-analysis. JAMA. 2008;300(23):2765-78."
      description: "吸烟状态相对风险"
      category: "lifestyle"
      evidence_level: "high"
    
    sedentary_lifestyle:
      baseline: 4.0  # 基线久坐时间（小时/天）
      per_hour_increase: 0.08  # 每小时增加的风险增幅
      max_effect: 1.6  # 最大风险倍数
      reference: "Boyle T, et al. Physical activity and risks of proximal and distal colon cancers: a systematic review and meta-analysis. J Natl Cancer Inst. 2012;104(20):1548-61."
      description: "久坐生活方式风险效应"
      category: "lifestyle"
      evidence_level: "moderate"
      calculation_method: "continuous"
      formula: "1 + max(0, sedentary_hours - baseline) * per_hour_increase, capped at max_effect"
    
    alcohol_consumption:
      baseline: 0.0  # 基线酒精消费（单位/周）
      per_unit_increase: 0.07  # 每单位增加的风险增幅
      max_effect: 1.5  # 最大风险倍数
      reference: "Fedirko V, et al. Alcohol drinking and colorectal cancer risk: an overall and dose-response meta-analysis of published studies. Ann Oncol. 2011;22(9):1958-72."
      description: "酒精消费风险效应"
      category: "lifestyle"
      evidence_level: "moderate"
      calculation_method: "continuous"
      formula: "1 + alcohol_units * per_unit_increase, capped at max_effect"
    
    diet_quality:
      baseline: 50.0  # 基线饮食质量评分
      per_point_decrease: 0.02  # 每分下降的风险增幅
      max_effect: 1.4  # 最大风险倍数
      reference: "Schwingshackl L, et al. Diet quality as assessed by the Healthy Eating Index, Alternate Healthy Eating Index, Dietary Approaches to Stop Hypertension score, and health outcomes: an updated systematic review and meta-analysis of cohort studies. J Acad Nutr Diet. 2018;118(1):74-100."
      description: "饮食质量风险效应（评分越低风险越高）"
      category: "lifestyle"
      evidence_level: "moderate"
      calculation_method: "continuous"
      formula: "1 + max(0, baseline - diet_score) * per_point_decrease, capped at max_effect"

# 风险分层阈值
risk_stratification:
  low_risk:
    threshold: 1.5
    description: "低风险：综合风险评分 < 1.5"
  
  moderate_risk:
    threshold_min: 1.5
    threshold_max: 3.0
    description: "中等风险：1.5 ≤ 综合风险评分 < 3.0"
  
  high_risk:
    threshold: 3.0
    description: "高风险：综合风险评分 ≥ 3.0"

# 年龄调整参数
age_adjustment:
  enabled: true
  baseline_age: 50  # 基线年龄
  per_year_multiplier: 0.03  # 每年增加的风险倍数
  max_age_effect: 2.0  # 年龄的最大风险倍数
  formula: "1 + max(0, age - baseline_age) * per_year_multiplier, capped at max_age_effect"

# 性别调整参数
gender_adjustment:
  enabled: true
  male_multiplier: 1.2  # 男性风险倍数
  female_multiplier: 1.0  # 女性风险倍数（基线）
  reference: "Center MM, et al. Worldwide variations in colorectal cancer. CA Cancer J Clin. 2009;59(6):366-78."

# 配置验证规则
validation_rules:
  weight_range:
    min: 0.1
    max: 10.0
    description: "权重值必须在0.1-10.0范围内"
  
  confidence_interval:
    required: true
    description: "高证据级别的权重必须提供置信区间"
  
  reference:
    required: true
    description: "所有权重必须提供参考文献"
