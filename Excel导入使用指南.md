# Excel文件导入功能使用指南

## 功能概述
PopulationGenerator类现在支持直接从Excel文件导入人群结构数据，支持以下格式：

### 支持的文件格式
- CSV (.csv): 支持UTF-8、GBK、GB2312等编码
- Excel (.xlsx, .xls): 读取第一个工作表

### 支持的数据格式

#### 1. 聚合格式
包含年龄、性别、人数三列：
```
年龄  性别  人数
50   男   1200
50   女   1150
55   男   1100
```

#### 2. 个体格式  
包含年龄、性别两列：
```
age  gender
50   M
55   F
60   M
```

### 支持的列名
- **年龄列**: age, 年龄, Age, AGE
- **性别列**: gender, 性别, Gender, GENDER, sex  
- **人数列**: count, number, 人数, 数量, Count, Number

### 支持的性别值
- **英文**: M, F, MALE, FEMALE, Male, Female, male, female
- **中文**: 男, 女, 男性, 女性

## 使用方法

```python
from src.modules.population import PopulationGenerator

# 创建生成器
generator = PopulationGenerator(random_seed=42)

# 从Excel文件生成人群
population = generator.generate_population_from_file(
    "sample_aggregated.xlsx",
    pathway_distribution={
        "adenoma_carcinoma_ratio": 0.85,
        "serrated_adenoma_ratio": 0.15
    },
    birth_year_base=2025,
    show_progress=True
)

# 查看结果
print(f"生成人群规模: {len(population.individuals)}")
stats = population.statistics
print(f"性别分布: {stats.get_gender_distribution()}")

# 获取生成摘要
summary = generator.get_last_generation_summary()
print(f"生成时间: {summary.generation_time:.3f}秒")
```

## 示例文件
- `sample_aggregated.xlsx`: 聚合格式示例
- `sample_individual.xlsx`: 个体格式示例  
- `sample_mixed.xlsx`: 混合格式示例
