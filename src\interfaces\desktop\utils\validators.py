"""
表单验证器模块

提供各种输入验证功能，包括数字范围验证、必填字段验证、
自定义业务逻辑验证等。
"""

import logging
from abc import ABC, abstractmethod
from typing import Any, Optional, List, Dict, Union
from dataclasses import dataclass
from enum import Enum


class ValidationLevel(Enum):
    """验证级别"""
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"


@dataclass
class ValidationResult:
    """验证结果"""
    is_valid: bool
    level: ValidationLevel
    message: str
    field_name: Optional[str] = None
    suggested_value: Optional[Any] = None
    
    def __post_init__(self):
        """后处理"""
        if not self.is_valid and self.level == ValidationLevel.INFO:
            # 如果验证失败，级别不能是INFO
            self.level = ValidationLevel.ERROR


class ValidationError(Exception):
    """验证错误异常"""
    
    def __init__(self, message: str, field_name: Optional[str] = None):
        super().__init__(message)
        self.field_name = field_name
        self.message = message


class InputValidator(ABC):
    """
    输入验证器基类
    
    所有具体验证器都应该继承此类并实现validate方法。
    """
    
    def __init__(self, field_name: str = ""):
        """初始化验证器"""
        self.field_name = field_name
    
    @abstractmethod
    def validate(self, value: Any) -> ValidationResult:
        """
        验证输入值
        
        Args:
            value: 要验证的值
            
        Returns:
            ValidationResult: 验证结果
        """
        pass
    
    def _create_result(
        self, 
        is_valid: bool, 
        message: str, 
        level: ValidationLevel = ValidationLevel.ERROR,
        suggested_value: Optional[Any] = None
    ) -> ValidationResult:
        """创建验证结果"""
        return ValidationResult(
            is_valid=is_valid,
            level=level,
            message=message,
            field_name=self.field_name,
            suggested_value=suggested_value
        )


class RequiredFieldValidator(InputValidator):
    """必填字段验证器"""
    
    def validate(self, value: Any) -> ValidationResult:
        """验证必填字段"""
        if value is None:
            return self._create_result(False, f"{self.field_name}不能为空")
        
        if isinstance(value, str) and not value.strip():
            return self._create_result(False, f"{self.field_name}不能为空字符串")
        
        if isinstance(value, (list, dict)) and len(value) == 0:
            return self._create_result(False, f"{self.field_name}不能为空")
        
        return self._create_result(True, "验证通过", ValidationLevel.INFO)


class NumericValidator(InputValidator):
    """数字验证器"""
    
    def __init__(self, field_name: str = "", allow_negative: bool = False):
        super().__init__(field_name)
        self.allow_negative = allow_negative
    
    def validate(self, value: Any) -> ValidationResult:
        """验证数字"""
        try:
            num_value = float(value)
            
            if not self.allow_negative and num_value < 0:
                return self._create_result(
                    False, 
                    f"{self.field_name}不能为负数",
                    suggested_value=abs(num_value)
                )
            
            return self._create_result(True, "验证通过", ValidationLevel.INFO)
            
        except (ValueError, TypeError):
            return self._create_result(False, f"{self.field_name}必须是有效数字")


class RangeValidator(InputValidator):
    """范围验证器"""
    
    def __init__(
        self, 
        field_name: str = "", 
        min_value: Optional[float] = None, 
        max_value: Optional[float] = None,
        inclusive: bool = True
    ):
        super().__init__(field_name)
        self.min_value = min_value
        self.max_value = max_value
        self.inclusive = inclusive
    
    def validate(self, value: Any) -> ValidationResult:
        """验证范围"""
        try:
            num_value = float(value)
            
            # 检查最小值
            if self.min_value is not None:
                if self.inclusive and num_value < self.min_value:
                    return self._create_result(
                        False,
                        f"{self.field_name}不能小于{self.min_value}",
                        suggested_value=self.min_value
                    )
                elif not self.inclusive and num_value <= self.min_value:
                    return self._create_result(
                        False,
                        f"{self.field_name}必须大于{self.min_value}",
                        suggested_value=self.min_value + 0.1
                    )
            
            # 检查最大值
            if self.max_value is not None:
                if self.inclusive and num_value > self.max_value:
                    return self._create_result(
                        False,
                        f"{self.field_name}不能大于{self.max_value}",
                        suggested_value=self.max_value
                    )
                elif not self.inclusive and num_value >= self.max_value:
                    return self._create_result(
                        False,
                        f"{self.field_name}必须小于{self.max_value}",
                        suggested_value=self.max_value - 0.1
                    )
            
            return self._create_result(True, "验证通过", ValidationLevel.INFO)
            
        except (ValueError, TypeError):
            return self._create_result(False, f"{self.field_name}必须是有效数字")


class AgeValidator(RangeValidator):
    """年龄验证器"""
    
    def __init__(self, field_name: str = "年龄"):
        super().__init__(field_name, min_value=0, max_value=150, inclusive=True)
    
    def validate(self, value: Any) -> ValidationResult:
        """验证年龄"""
        # 先进行基本范围验证
        result = super().validate(value)
        if not result.is_valid:
            return result
        
        try:
            age = float(value)
            
            # 年龄特定的验证
            if age < 18:
                return self._create_result(
                    False,
                    "模拟对象年龄不能小于18岁",
                    ValidationLevel.WARNING,
                    suggested_value=18
                )
            
            if age > 100:
                return self._create_result(
                    False,
                    "年龄超过100岁，请确认输入正确",
                    ValidationLevel.WARNING
                )
            
            return self._create_result(True, "年龄验证通过", ValidationLevel.INFO)
            
        except (ValueError, TypeError):
            return self._create_result(False, "年龄必须是有效数字")


class GenderRatioValidator(InputValidator):
    """性别比例验证器"""
    
    def validate(self, value: Any) -> ValidationResult:
        """验证性别比例"""
        try:
            ratio = float(value)
            
            if ratio < 0 or ratio > 1:
                return self._create_result(
                    False,
                    "性别比例必须在0到1之间",
                    suggested_value=max(0, min(1, ratio))
                )
            
            # 检查是否为极端值
            if ratio < 0.1 or ratio > 0.9:
                return self._create_result(
                    True,
                    "性别比例偏向极端，请确认是否正确",
                    ValidationLevel.WARNING
                )
            
            return self._create_result(True, "性别比例验证通过", ValidationLevel.INFO)
            
        except (ValueError, TypeError):
            return self._create_result(False, "性别比例必须是有效数字")


class PopulationSizeValidator(RangeValidator):
    """人群规模验证器"""
    
    def __init__(self, field_name: str = "人群规模"):
        super().__init__(field_name, min_value=1, max_value=1_000_000, inclusive=True)
    
    def validate(self, value: Any) -> ValidationResult:
        """验证人群规模"""
        # 先进行基本范围验证
        result = super().validate(value)
        if not result.is_valid:
            return result
        
        try:
            size = int(value)
            
            # 人群规模特定的验证
            if size < 100:
                return self._create_result(
                    True,
                    "人群规模较小，可能影响统计结果的可靠性",
                    ValidationLevel.WARNING
                )
            
            if size > 100_000:
                return self._create_result(
                    True,
                    "人群规模较大，模拟可能需要较长时间",
                    ValidationLevel.WARNING
                )
            
            return self._create_result(True, "人群规模验证通过", ValidationLevel.INFO)
            
        except (ValueError, TypeError):
            return self._create_result(False, "人群规模必须是有效整数")


class CompositeValidator:
    """
    复合验证器
    
    可以组合多个验证器进行验证。
    """
    
    def __init__(self, validators: List[InputValidator]):
        """初始化复合验证器"""
        self.validators = validators
    
    def validate(self, value: Any) -> List[ValidationResult]:
        """执行所有验证器"""
        results = []
        
        for validator in self.validators:
            try:
                result = validator.validate(value)
                results.append(result)
                
                # 如果遇到错误级别的验证失败，可以选择是否继续
                if not result.is_valid and result.level == ValidationLevel.ERROR:
                    # 这里可以选择是否继续验证其他规则
                    pass
                    
            except Exception as e:
                logging.error(f"验证器执行错误: {e}")
                results.append(ValidationResult(
                    is_valid=False,
                    level=ValidationLevel.ERROR,
                    message=f"验证器执行错误: {e}",
                    field_name=getattr(validator, 'field_name', 'unknown')
                ))
        
        return results
    
    def is_valid(self, value: Any) -> bool:
        """检查值是否通过所有验证"""
        results = self.validate(value)
        return all(result.is_valid or result.level != ValidationLevel.ERROR for result in results)
    
    def get_error_messages(self, value: Any) -> List[str]:
        """获取所有错误消息"""
        results = self.validate(value)
        return [
            result.message 
            for result in results 
            if not result.is_valid and result.level == ValidationLevel.ERROR
        ]
    
    def get_warning_messages(self, value: Any) -> List[str]:
        """获取所有警告消息"""
        results = self.validate(value)
        return [
            result.message 
            for result in results 
            if result.level == ValidationLevel.WARNING
        ]


# 预定义的验证器组合
def create_age_validator(field_name: str = "年龄") -> CompositeValidator:
    """创建年龄验证器组合"""
    return CompositeValidator([
        RequiredFieldValidator(field_name),
        NumericValidator(field_name, allow_negative=False),
        AgeValidator(field_name)
    ])


def create_population_size_validator(field_name: str = "人群规模") -> CompositeValidator:
    """创建人群规模验证器组合"""
    return CompositeValidator([
        RequiredFieldValidator(field_name),
        NumericValidator(field_name, allow_negative=False),
        PopulationSizeValidator(field_name)
    ])


def create_gender_ratio_validator(field_name: str = "性别比例") -> CompositeValidator:
    """创建性别比例验证器组合"""
    return CompositeValidator([
        RequiredFieldValidator(field_name),
        NumericValidator(field_name, allow_negative=False),
        GenderRatioValidator(field_name)
    ])
