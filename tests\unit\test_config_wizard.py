"""
人群配置向导单元测试

测试PopulationConfigWidget的功能
"""

import pytest
import sys
from unittest.mock import Mock, patch
from PyQt6.QtWidgets import QApplication
from PyQt6.QtTest import QTest
from PyQt6.QtCore import Qt

# 添加src路径
sys.path.insert(0, "src")

from interfaces.desktop.windows.config_wizard import PopulationConfigWidget, PopulationConfig


@pytest.fixture(scope="session")
def qapp():
    """创建QApplication实例"""
    if not QApplication.instance():
        app = QApplication([])
        yield app
        app.quit()
    else:
        yield QApplication.instance()


@pytest.fixture
def config_widget(qapp):
    """创建PopulationConfigWidget实例"""
    return PopulationConfigWidget()


@pytest.fixture
def sample_config():
    """创建示例配置"""
    return PopulationConfig(
        size=50000,
        age_mean=65.0,
        age_std=12.0,
        age_min=45,
        age_max=85,
        male_ratio=0.52,
        distribution_type="normal"
    )


class TestPopulationConfig:
    """测试PopulationConfig数据类"""
    
    def test_default_config(self):
        """测试默认配置"""
        config = PopulationConfig()
        assert config.size == 10000
        assert config.age_mean == 60.0
        assert config.age_std == 10.0
        assert config.age_min == 40
        assert config.age_max == 80
        assert config.male_ratio == 0.5
        assert config.distribution_type == "normal"
    
    def test_custom_config(self, sample_config):
        """测试自定义配置"""
        assert sample_config.size == 50000
        assert sample_config.age_mean == 65.0
        assert sample_config.male_ratio == 0.52


class TestPopulationConfigWidget:
    """测试PopulationConfigWidget类"""
    
    def test_widget_creation(self, config_widget):
        """测试组件创建"""
        assert config_widget is not None
        assert isinstance(config_widget.config, PopulationConfig)
    
    def test_ui_components_exist(self, config_widget):
        """测试UI组件存在"""
        # 检查主要输入控件
        assert hasattr(config_widget, 'size_spinbox')
        assert hasattr(config_widget, 'age_min_spinbox')
        assert hasattr(config_widget, 'age_max_spinbox')
        assert hasattr(config_widget, 'age_mean_spinbox')
        assert hasattr(config_widget, 'age_std_spinbox')
        assert hasattr(config_widget, 'gender_slider')
        assert hasattr(config_widget, 'distribution_combo')
        
        # 检查按钮
        assert hasattr(config_widget, 'preview_button')
        assert hasattr(config_widget, 'reset_button')
        assert hasattr(config_widget, 'apply_button')
        
        # 检查显示组件
        assert hasattr(config_widget, 'preview_text')
        assert hasattr(config_widget, 'male_ratio_label')
        assert hasattr(config_widget, 'female_ratio_label')
    
    def test_initial_values(self, config_widget):
        """测试初始值"""
        assert config_widget.size_spinbox.value() == 10000
        assert config_widget.age_mean_spinbox.value() == 60.0
        assert config_widget.age_std_spinbox.value() == 10.0
        assert config_widget.age_min_spinbox.value() == 40
        assert config_widget.age_max_spinbox.value() == 80
        assert config_widget.gender_slider.value() == 50
    
    def test_spinbox_ranges(self, config_widget):
        """测试数值输入框范围"""
        # 人群规模范围
        assert config_widget.size_spinbox.minimum() == 100
        assert config_widget.size_spinbox.maximum() == 1000000
        
        # 年龄范围
        assert config_widget.age_min_spinbox.minimum() == 18
        assert config_widget.age_min_spinbox.maximum() == 100
        assert config_widget.age_max_spinbox.minimum() == 18
        assert config_widget.age_max_spinbox.maximum() == 100
        
        # 年龄均值和标准差范围
        assert config_widget.age_mean_spinbox.minimum() == 18.0
        assert config_widget.age_mean_spinbox.maximum() == 100.0
        assert config_widget.age_std_spinbox.minimum() == 1.0
        assert config_widget.age_std_spinbox.maximum() == 30.0
    
    def test_gender_slider(self, config_widget):
        """测试性别比例滑块"""
        slider = config_widget.gender_slider
        
        # 测试范围
        assert slider.minimum() == 0
        assert slider.maximum() == 100
        
        # 测试初始值
        assert slider.value() == 50
        
        # 测试滑块变化
        slider.setValue(60)
        assert "男性: 60.0%" in config_widget.male_ratio_label.text()
        assert "女性: 40.0%" in config_widget.female_ratio_label.text()
    
    def test_distribution_combo(self, config_widget):
        """测试分布类型选择"""
        combo = config_widget.distribution_combo
        
        # 检查选项
        items = [combo.itemText(i) for i in range(combo.count())]
        assert "正态分布" in items
        assert "均匀分布" in items
        assert "自定义分布" in items
    
    def test_config_update_from_ui(self, config_widget):
        """测试从UI更新配置"""
        # 修改UI值
        config_widget.size_spinbox.setValue(20000)
        config_widget.age_mean_spinbox.setValue(55.0)
        config_widget.gender_slider.setValue(45)
        
        # 更新配置
        config_widget._update_config_from_ui()
        
        # 检查配置是否更新
        assert config_widget.config.size == 20000
        assert config_widget.config.age_mean == 55.0
        assert config_widget.config.male_ratio == 0.45
    
    def test_ui_update_from_config(self, config_widget, sample_config):
        """测试从配置更新UI"""
        # 设置配置
        config_widget.config = sample_config
        config_widget._update_ui_from_config()
        
        # 检查UI是否更新
        assert config_widget.size_spinbox.value() == 50000
        assert config_widget.age_mean_spinbox.value() == 65.0
        assert config_widget.age_min_spinbox.value() == 45
        assert config_widget.age_max_spinbox.value() == 85
        assert config_widget.gender_slider.value() == 52
    
    def test_preview_generation(self, config_widget):
        """测试预览生成"""
        # 生成预览
        config_widget._generate_preview()
        
        # 检查预览文本不为空
        preview_text = config_widget.preview_text.toPlainText()
        assert len(preview_text) > 0
        assert "配置摘要" in preview_text
        assert "人群规模" in preview_text
        assert "年龄分布" in preview_text
    
    def test_reset_config(self, config_widget):
        """测试重置配置"""
        # 修改配置
        config_widget.size_spinbox.setValue(20000)
        config_widget.age_mean_spinbox.setValue(55.0)
        
        # 重置配置
        config_widget._reset_config()
        
        # 检查是否恢复默认值
        assert config_widget.size_spinbox.value() == 10000
        assert config_widget.age_mean_spinbox.value() == 60.0
        assert config_widget.config.size == 10000
        assert config_widget.config.age_mean == 60.0
    
    def test_apply_config(self, config_widget):
        """测试应用配置"""
        # 修改UI值
        config_widget.size_spinbox.setValue(15000)
        
        # 模拟信号
        with patch.object(config_widget, 'config_changed') as mock_signal:
            config_widget._apply_config()
            
            # 检查信号是否发出
            mock_signal.emit.assert_called_once()
            
            # 检查配置是否更新
            assert config_widget.config.size == 15000
    
    def test_get_set_config(self, config_widget, sample_config):
        """测试获取和设置配置"""
        # 设置配置
        config_widget.set_config(sample_config)
        
        # 获取配置
        retrieved_config = config_widget.get_config()
        
        # 检查配置是否一致
        assert retrieved_config.size == sample_config.size
        assert retrieved_config.age_mean == sample_config.age_mean
        assert retrieved_config.male_ratio == sample_config.male_ratio
    
    def test_signals(self, config_widget):
        """测试信号"""
        # 检查信号是否定义
        assert hasattr(config_widget, 'config_changed')
        assert hasattr(config_widget, 'preview_requested')
        
        # 测试信号连接
        with patch.object(config_widget, 'config_changed') as mock_signal:
            # 触发配置变化
            config_widget.size_spinbox.setValue(15000)
            config_widget._on_config_changed()
            
            # 检查信号是否发出
            mock_signal.emit.assert_called()
    
    def test_validation_ranges(self, config_widget):
        """测试验证范围"""
        # 测试人群规模边界值
        config_widget.size_spinbox.setValue(100)  # 最小值
        assert config_widget.size_spinbox.value() == 100
        
        config_widget.size_spinbox.setValue(1000000)  # 最大值
        assert config_widget.size_spinbox.value() == 1000000
        
        # 测试年龄边界值
        config_widget.age_min_spinbox.setValue(18)  # 最小值
        assert config_widget.age_min_spinbox.value() == 18
        
        config_widget.age_max_spinbox.setValue(100)  # 最大值
        assert config_widget.age_max_spinbox.value() == 100


class TestConfigWidgetIntegration:
    """测试配置组件集成"""
    
    def test_config_change_workflow(self, config_widget):
        """测试配置变化工作流"""
        # 模拟用户操作序列
        original_size = config_widget.config.size
        
        # 1. 用户修改人群规模
        config_widget.size_spinbox.setValue(25000)
        
        # 2. 触发配置变化
        config_widget._on_config_changed()
        
        # 3. 检查配置是否更新
        assert config_widget.config.size == 25000
        assert config_widget.config.size != original_size
        
        # 4. 检查预览是否更新
        preview_text = config_widget.preview_text.toPlainText()
        assert "25,000" in preview_text
    
    def test_gender_ratio_consistency(self, config_widget):
        """测试性别比例一致性"""
        # 设置男性比例为60%
        config_widget.gender_slider.setValue(60)
        config_widget._on_gender_ratio_changed(60)
        
        # 检查标签显示
        assert "男性: 60.0%" in config_widget.male_ratio_label.text()
        assert "女性: 40.0%" in config_widget.female_ratio_label.text()
        
        # 检查配置更新
        config_widget._update_config_from_ui()
        assert config_widget.config.male_ratio == 0.6


if __name__ == "__main__":
    pytest.main([__file__])
