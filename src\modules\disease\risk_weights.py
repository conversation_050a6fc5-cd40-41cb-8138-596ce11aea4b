"""
风险因素权重配置管理系统

实现风险因素权重的加载、验证和管理功能。
"""

import os
import yaml
import logging
from pathlib import Path
from typing import Dict, Any, Optional, Union, Tuple
from dataclasses import dataclass, field
from datetime import datetime

from .risk_factors import RiskFactorType


logger = logging.getLogger(__name__)


def get_default_config_path() -> Path:
    """
    获取默认配置文件路径，支持环境变量配置

    查找顺序：
    1. 环境变量 RISK_FACTOR_WEIGHTS_CONFIG
    2. 环境变量 RISK_FACTOR_WEIGHTS_DIR + default_weights.yaml
    3. 项目根目录/data/risk_factor_weights/default_weights.yaml
    4. 当前模块目录的相对路径

    Returns:
        Path: 配置文件路径

    Raises:
        FileNotFoundError: 如果找不到配置文件
    """
    # 1. 直接指定配置文件路径
    config_file = os.getenv("RISK_FACTOR_WEIGHTS_CONFIG")
    if config_file:
        config_path = Path(config_file)
        if config_path.exists():
            logger.info(f"使用环境变量指定的配置文件: {config_path}")
            return config_path
        else:
            logger.warning(f"环境变量指定的配置文件不存在: {config_path}")

    # 2. 指定配置目录
    config_dir = os.getenv("RISK_FACTOR_WEIGHTS_DIR")
    if config_dir:
        config_path = Path(config_dir) / "default_weights.yaml"
        if config_path.exists():
            logger.info(f"使用环境变量指定的配置目录: {config_path}")
            return config_path
        else:
            logger.warning(f"环境变量指定的配置目录中找不到文件: {config_path}")

    # 3. 项目根目录查找
    # 从当前文件向上查找项目根目录
    current_path = Path(__file__).parent
    for _ in range(5):  # 最多向上查找5级目录
        potential_path = current_path / "data" / "risk_factor_weights" / "default_weights.yaml"
        if potential_path.exists():
            logger.info(f"使用项目根目录配置文件: {potential_path}")
            return potential_path
        current_path = current_path.parent

    # 4. 使用相对于当前模块的默认路径
    default_path = Path(__file__).parent.parent.parent.parent / "data" / "risk_factor_weights" / "default_weights.yaml"
    if default_path.exists():
        logger.info(f"使用默认相对路径配置文件: {default_path}")
        return default_path

    # 如果都找不到，抛出异常
    raise FileNotFoundError(
        f"无法找到风险因素权重配置文件。请检查以下位置之一：\n"
        f"1. 环境变量 RISK_FACTOR_WEIGHTS_CONFIG 指定的文件\n"
        f"2. 环境变量 RISK_FACTOR_WEIGHTS_DIR 目录下的 default_weights.yaml\n"
        f"3. 项目根目录下的 data/risk_factor_weights/default_weights.yaml\n"
        f"4. 默认路径: {default_path}"
    )


def set_config_path(config_path: Union[str, Path]) -> None:
    """
    设置风险因素权重配置文件路径

    Args:
        config_path: 配置文件路径

    Raises:
        FileNotFoundError: 如果配置文件不存在
    """
    config_path = Path(config_path)
    if not config_path.exists():
        raise FileNotFoundError(f"配置文件不存在: {config_path}")

    os.environ["RISK_FACTOR_WEIGHTS_CONFIG"] = str(config_path.absolute())
    logger.info(f"设置风险因素权重配置文件路径: {config_path}")


def set_config_dir(config_dir: Union[str, Path]) -> None:
    """
    设置风险因素权重配置目录

    Args:
        config_dir: 配置目录路径

    Raises:
        FileNotFoundError: 如果配置目录不存在
    """
    config_dir = Path(config_dir)
    if not config_dir.exists():
        raise FileNotFoundError(f"配置目录不存在: {config_dir}")

    config_file = config_dir / "default_weights.yaml"
    if not config_file.exists():
        raise FileNotFoundError(f"配置目录中找不到default_weights.yaml: {config_dir}")

    os.environ["RISK_FACTOR_WEIGHTS_DIR"] = str(config_dir.absolute())
    logger.info(f"设置风险因素权重配置目录: {config_dir}")


def clear_config_env() -> None:
    """清除配置相关的环境变量"""
    os.environ.pop("RISK_FACTOR_WEIGHTS_CONFIG", None)
    os.environ.pop("RISK_FACTOR_WEIGHTS_DIR", None)
    logger.info("已清除风险因素权重配置环境变量")


def get_current_config_info() -> Dict[str, Any]:
    """
    获取当前配置信息

    Returns:
        Dict: 包含当前配置信息的字典
    """
    safe_path = _safe_get_default_path()
    return {
        "config_file_env": os.getenv("RISK_FACTOR_WEIGHTS_CONFIG"),
        "config_dir_env": os.getenv("RISK_FACTOR_WEIGHTS_DIR"),
        "default_path_exists": safe_path.exists() if safe_path else False,
        "default_path": str(safe_path) if safe_path else None,
        "environment_variables": {
            "RISK_FACTOR_WEIGHTS_CONFIG": os.getenv("RISK_FACTOR_WEIGHTS_CONFIG"),
            "RISK_FACTOR_WEIGHTS_DIR": os.getenv("RISK_FACTOR_WEIGHTS_DIR")
        }
    }


def _safe_get_default_path() -> Optional[Path]:
    """安全地获取默认路径，不抛出异常"""
    try:
        return get_default_config_path()
    except FileNotFoundError:
        return None


@dataclass
class WeightConfig:
    """权重配置数据类"""
    value: float
    confidence_interval: Optional[Tuple[float, float]] = None
    reference: Optional[str] = None
    description: Optional[str] = None
    category: Optional[str] = None
    evidence_level: Optional[str] = None
    calculation_method: str = "boolean"
    
    # 连续型风险因素的额外参数
    baseline: Optional[float] = None
    per_unit_increase: Optional[float] = None
    per_hour_increase: Optional[float] = None
    per_point_decrease: Optional[float] = None
    max_effect: Optional[float] = None
    formula: Optional[str] = None
    
    def __post_init__(self):
        """验证权重配置"""
        if self.value <= 0:
            raise ValueError("权重值必须大于0")
        
        if self.confidence_interval:
            if len(self.confidence_interval) != 2:
                raise ValueError("置信区间必须包含两个值")
            if self.confidence_interval[0] >= self.confidence_interval[1]:
                raise ValueError("置信区间下限必须小于上限")


class RiskFactorWeights:
    """风险因素权重管理类"""
    
    def __init__(self, config_file: Optional[Path] = None):
        """
        初始化权重管理器
        
        Args:
            config_file: 配置文件路径，如果为None则使用默认配置
        """
        self.config_file = config_file
        self.weights: Dict[RiskFactorType, WeightConfig] = {}
        self.risk_stratification: Dict[str, Any] = {}
        self.age_adjustment: Dict[str, Any] = {}
        self.gender_adjustment: Dict[str, Any] = {}
        self.validation_rules: Dict[str, Any] = {}
        self.metadata: Dict[str, Any] = {}
        
        # 加载配置
        self._load_config()
    
    def _load_config(self):
        """加载权重配置"""
        if self.config_file is None:
            # 使用智能路径解析获取默认配置文件
            try:
                self.config_file = get_default_config_path()
            except FileNotFoundError as e:
                logger.error(f"无法找到默认配置文件: {e}")
                raise

        if not self.config_file.exists():
            raise FileNotFoundError(f"权重配置文件不存在: {self.config_file}")

        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                config_data = yaml.safe_load(f)

            self._parse_config(config_data)
            self._validate_config()

            logger.info(f"成功加载权重配置: {self.config_file}")

        except Exception as e:
            logger.error(f"加载权重配置失败: {e}")
            raise
    
    def _parse_config(self, config_data: Dict[str, Any]):
        """解析配置数据"""
        # 解析元数据
        risk_weights = config_data.get("risk_factor_weights", {})
        self.metadata = {
            "version": risk_weights.get("version"),
            "source": risk_weights.get("source"),
            "description": risk_weights.get("description"),
            "last_updated": risk_weights.get("last_updated")
        }
        
        # 解析权重配置
        weights_data = risk_weights.get("weights", {})
        for factor_name, weight_data in weights_data.items():
            try:
                factor_type = RiskFactorType(factor_name)
                
                # 处理置信区间
                ci = weight_data.get("confidence_interval")
                if ci and isinstance(ci, list) and len(ci) == 2:
                    ci = tuple(ci)
                
                # 对于连续型风险因素，如果没有直接的value，使用默认值1.0
                value = weight_data.get("value", 1.0)

                weight_config = WeightConfig(
                    value=value,
                    confidence_interval=ci,
                    reference=weight_data.get("reference"),
                    description=weight_data.get("description"),
                    category=weight_data.get("category"),
                    evidence_level=weight_data.get("evidence_level"),
                    calculation_method=weight_data.get("calculation_method", "boolean"),
                    baseline=weight_data.get("baseline"),
                    per_unit_increase=weight_data.get("per_unit_increase"),
                    per_hour_increase=weight_data.get("per_hour_increase"),
                    per_point_decrease=weight_data.get("per_point_decrease"),
                    max_effect=weight_data.get("max_effect"),
                    formula=weight_data.get("formula")
                )
                
                self.weights[factor_type] = weight_config
                
            except ValueError as e:
                logger.warning(f"跳过未知风险因素类型: {factor_name}")
                continue
        
        # 解析其他配置
        self.risk_stratification = config_data.get("risk_stratification", {})
        self.age_adjustment = config_data.get("age_adjustment", {})
        self.gender_adjustment = config_data.get("gender_adjustment", {})
        self.validation_rules = config_data.get("validation_rules", {})
    
    def _validate_config(self):
        """验证配置完整性"""
        # 检查必需的风险因素是否都有权重
        required_factors = {
            RiskFactorType.FAMILY_HISTORY,
            RiskFactorType.IBD,
            RiskFactorType.BMI,
            RiskFactorType.DIABETES,
            RiskFactorType.SMOKING
        }
        
        missing_factors = required_factors - set(self.weights.keys())
        if missing_factors:
            logger.warning(f"缺少必需风险因素的权重配置: {[f.value for f in missing_factors]}")
        
        # 验证权重值范围
        weight_range = self.validation_rules.get("weight_range", {})
        min_weight = weight_range.get("min", 0.1)
        max_weight = weight_range.get("max", 10.0)
        
        for factor_type, weight_config in self.weights.items():
            if not (min_weight <= weight_config.value <= max_weight):
                logger.warning(
                    f"权重值超出范围 {factor_type.value}: {weight_config.value} "
                    f"(范围: {min_weight}-{max_weight})"
                )
    
    def get_weight(self, factor_type: RiskFactorType) -> Optional[WeightConfig]:
        """获取指定风险因素的权重配置"""
        return self.weights.get(factor_type)
    
    def get_weight_value(self, factor_type: RiskFactorType) -> float:
        """获取指定风险因素的权重值"""
        weight_config = self.get_weight(factor_type)
        return weight_config.value if weight_config else 1.0
    
    def has_weight(self, factor_type: RiskFactorType) -> bool:
        """检查是否存在指定风险因素的权重"""
        return factor_type in self.weights
    
    def get_all_weights(self) -> Dict[RiskFactorType, WeightConfig]:
        """获取所有权重配置"""
        return self.weights.copy()
    
    def calculate_continuous_weight(
        self, 
        factor_type: RiskFactorType, 
        value: float
    ) -> float:
        """
        计算连续型风险因素的权重
        
        Args:
            factor_type: 风险因素类型
            value: 风险因素值
            
        Returns:
            float: 计算得出的权重
        """
        weight_config = self.get_weight(factor_type)
        if not weight_config or weight_config.calculation_method != "continuous":
            return weight_config.value if weight_config else 1.0
        
        if factor_type == RiskFactorType.BMI:
            return self._calculate_bmi_weight(value, weight_config)
        elif factor_type == RiskFactorType.SEDENTARY_LIFESTYLE:
            return self._calculate_sedentary_weight(value, weight_config)
        elif factor_type == RiskFactorType.ALCOHOL_CONSUMPTION:
            return self._calculate_alcohol_weight(value, weight_config)
        elif factor_type == RiskFactorType.DIET_QUALITY:
            return self._calculate_diet_weight(value, weight_config)
        else:
            return weight_config.value
    
    def _calculate_bmi_weight(self, bmi: float, config: WeightConfig) -> float:
        """计算BMI权重"""
        if config.baseline is None or config.per_unit_increase is None:
            return config.value
        
        weight = 1.0 + max(0, bmi - config.baseline) * config.per_unit_increase
        
        if config.max_effect:
            weight = min(weight, config.max_effect)
        
        return weight
    
    def _calculate_sedentary_weight(self, hours: float, config: WeightConfig) -> float:
        """计算久坐时间权重"""
        if config.baseline is None or config.per_hour_increase is None:
            return config.value
        
        weight = 1.0 + max(0, hours - config.baseline) * config.per_hour_increase
        
        if config.max_effect:
            weight = min(weight, config.max_effect)
        
        return weight
    
    def _calculate_alcohol_weight(self, units: float, config: WeightConfig) -> float:
        """计算酒精消费权重"""
        if config.per_unit_increase is None:
            return config.value
        
        weight = 1.0 + units * config.per_unit_increase
        
        if config.max_effect:
            weight = min(weight, config.max_effect)
        
        return weight
    
    def _calculate_diet_weight(self, score: float, config: WeightConfig) -> float:
        """计算饮食质量权重"""
        if config.baseline is None or config.per_point_decrease is None:
            return config.value
        
        weight = 1.0 + max(0, config.baseline - score) * config.per_point_decrease
        
        if config.max_effect:
            weight = min(weight, config.max_effect)
        
        return weight
    
    def get_risk_stratification_thresholds(self) -> Dict[str, float]:
        """获取风险分层阈值"""
        return {
            "low_risk": self.risk_stratification.get("low_risk", {}).get("threshold", 1.5),
            "moderate_risk_min": self.risk_stratification.get("moderate_risk", {}).get("threshold_min", 1.5),
            "moderate_risk_max": self.risk_stratification.get("moderate_risk", {}).get("threshold_max", 3.0),
            "high_risk": self.risk_stratification.get("high_risk", {}).get("threshold", 3.0)
        }
    
    def get_age_adjustment_params(self) -> Dict[str, Any]:
        """获取年龄调整参数"""
        return self.age_adjustment.copy()
    
    def get_gender_adjustment_params(self) -> Dict[str, Any]:
        """获取性别调整参数"""
        return self.gender_adjustment.copy()
    
    def get_metadata(self) -> Dict[str, Any]:
        """获取配置元数据"""
        return self.metadata.copy()
    
    def reload_config(self):
        """重新加载配置"""
        self.weights.clear()
        self._load_config()
    
    def save_config(self, file_path: Path):
        """保存配置到文件"""
        config_data = {
            "risk_factor_weights": {
                **self.metadata,
                "weights": {}
            },
            "risk_stratification": self.risk_stratification,
            "age_adjustment": self.age_adjustment,
            "gender_adjustment": self.gender_adjustment,
            "validation_rules": self.validation_rules
        }
        
        # 转换权重配置
        for factor_type, weight_config in self.weights.items():
            weight_data = {
                "value": weight_config.value,
                "calculation_method": weight_config.calculation_method
            }
            
            if weight_config.confidence_interval:
                weight_data["confidence_interval"] = list(weight_config.confidence_interval)
            
            for attr in ["reference", "description", "category", "evidence_level", 
                        "baseline", "per_unit_increase", "per_hour_increase", 
                        "per_point_decrease", "max_effect", "formula"]:
                value = getattr(weight_config, attr)
                if value is not None:
                    weight_data[attr] = value
            
            config_data["risk_factor_weights"]["weights"][factor_type.value] = weight_data
        
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                yaml.dump(config_data, f, default_flow_style=False, allow_unicode=True)
            
            logger.info(f"配置已保存到: {file_path}")
            
        except Exception as e:
            logger.error(f"保存配置失败: {e}")
            raise
    
    def __repr__(self) -> str:
        """字符串表示"""
        return (
            f"RiskFactorWeights(factors={len(self.weights)}, "
            f"version={self.metadata.get('version', 'unknown')})"
        )
