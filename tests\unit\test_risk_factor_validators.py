"""
风险因素验证功能测试

测试风险因素值的验证、约束检查和一致性验证。
"""

import pytest

from src.utils.validators import (
    RiskFactorValidationError,
    validate_bmi,
    validate_sedentary_hours,
    validate_alcohol_consumption,
    validate_diet_quality_score,
    validate_boolean_risk_factor,
    validate_risk_factor_value,
    validate_risk_factor_weight,
    validate_risk_factor_profile_completeness,
    validate_age_risk_factor_consistency
)
from src.modules.disease.risk_factors import RiskFactorType, RiskFactor, RiskFactorProfile


class TestBMIValidation:
    """测试BMI验证"""
    
    def test_valid_bmi(self):
        """测试有效BMI值"""
        assert validate_bmi(18.5) == 18.5
        assert validate_bmi(25.0) == 25.0
        assert validate_bmi(30.5) == 30.5
        assert validate_bmi(40) == 40.0
    
    def test_bmi_boundary_values(self):
        """测试BMI边界值"""
        assert validate_bmi(10.0) == 10.0  # 最小值
        assert validate_bmi(60.0) == 60.0  # 最大值
    
    def test_invalid_bmi_range(self):
        """测试无效BMI范围"""
        with pytest.raises(RiskFactorValidationError, match="BMI值必须在10.0-60.0范围内"):
            validate_bmi(9.9)
        
        with pytest.raises(RiskFactorValidationError, match="BMI值必须在10.0-60.0范围内"):
            validate_bmi(60.1)
    
    def test_invalid_bmi_type(self):
        """测试无效BMI类型"""
        with pytest.raises(RiskFactorValidationError, match="BMI必须是数字类型"):
            validate_bmi("invalid")

        with pytest.raises(RiskFactorValidationError, match="BMI必须是数字类型"):
            validate_bmi(None)


class TestSedentaryHoursValidation:
    """测试久坐时间验证"""
    
    def test_valid_sedentary_hours(self):
        """测试有效久坐时间"""
        assert validate_sedentary_hours(0.0) == 0.0
        assert validate_sedentary_hours(8.5) == 8.5
        assert validate_sedentary_hours(24.0) == 24.0
    
    def test_invalid_sedentary_hours_range(self):
        """测试无效久坐时间范围"""
        with pytest.raises(RiskFactorValidationError, match="久坐时间必须在0-24小时范围内"):
            validate_sedentary_hours(-1.0)
        
        with pytest.raises(RiskFactorValidationError, match="久坐时间必须在0-24小时范围内"):
            validate_sedentary_hours(25.0)
    
    def test_invalid_sedentary_hours_type(self):
        """测试无效久坐时间类型"""
        with pytest.raises(RiskFactorValidationError, match="久坐时间必须是数字类型"):
            validate_sedentary_hours("invalid")


class TestAlcoholConsumptionValidation:
    """测试酒精消费量验证"""
    
    def test_valid_alcohol_consumption(self):
        """测试有效酒精消费量"""
        assert validate_alcohol_consumption(0.0) == 0.0
        assert validate_alcohol_consumption(10.5) == 10.5
        assert validate_alcohol_consumption(100.0) == 100.0
    
    def test_invalid_alcohol_consumption_range(self):
        """测试无效酒精消费量范围"""
        with pytest.raises(RiskFactorValidationError, match="酒精消费量必须在0-100单位/周范围内"):
            validate_alcohol_consumption(-1.0)
        
        with pytest.raises(RiskFactorValidationError, match="酒精消费量必须在0-100单位/周范围内"):
            validate_alcohol_consumption(101.0)


class TestDietQualityScoreValidation:
    """测试饮食质量评分验证"""
    
    def test_valid_diet_quality_score(self):
        """测试有效饮食质量评分"""
        assert validate_diet_quality_score(0.0) == 0.0
        assert validate_diet_quality_score(50.0) == 50.0
        assert validate_diet_quality_score(100.0) == 100.0
    
    def test_invalid_diet_quality_score_range(self):
        """测试无效饮食质量评分范围"""
        with pytest.raises(RiskFactorValidationError, match="饮食质量评分必须在0-100范围内"):
            validate_diet_quality_score(-1.0)
        
        with pytest.raises(RiskFactorValidationError, match="饮食质量评分必须在0-100范围内"):
            validate_diet_quality_score(101.0)


class TestBooleanRiskFactorValidation:
    """测试布尔型风险因素验证"""
    
    def test_valid_boolean_values(self):
        """测试有效布尔值"""
        # 标准布尔值
        assert validate_boolean_risk_factor(True) is True
        assert validate_boolean_risk_factor(False) is False
        
        # 字符串布尔值
        assert validate_boolean_risk_factor("true") is True
        assert validate_boolean_risk_factor("false") is False
        assert validate_boolean_risk_factor("yes") is True
        assert validate_boolean_risk_factor("no") is False
        assert validate_boolean_risk_factor("1") is True
        assert validate_boolean_risk_factor("0") is False
        
        # 数字布尔值
        assert validate_boolean_risk_factor(1) is True
        assert validate_boolean_risk_factor(0) is False
        assert validate_boolean_risk_factor(1.0) is True
        assert validate_boolean_risk_factor(0.0) is False
    
    def test_case_insensitive_strings(self):
        """测试大小写不敏感的字符串"""
        assert validate_boolean_risk_factor("TRUE") is True
        assert validate_boolean_risk_factor("False") is False
        assert validate_boolean_risk_factor("YES") is True
        assert validate_boolean_risk_factor("No") is False
    
    def test_chinese_boolean_values(self):
        """测试中文布尔值"""
        assert validate_boolean_risk_factor("是") is True
        assert validate_boolean_risk_factor("否") is False
        assert validate_boolean_risk_factor("有") is True
        assert validate_boolean_risk_factor("无") is False
    
    def test_invalid_boolean_values(self):
        """测试无效布尔值"""
        with pytest.raises(RiskFactorValidationError, match="无效的布尔值"):
            validate_boolean_risk_factor("maybe")
        
        with pytest.raises(RiskFactorValidationError, match="数字类型的布尔值只能是0或1"):
            validate_boolean_risk_factor(2)
        
        with pytest.raises(RiskFactorValidationError, match="布尔型风险因素必须是布尔、字符串或数字类型"):
            validate_boolean_risk_factor([])


class TestRiskFactorValueValidation:
    """测试风险因素值验证"""
    
    def test_bmi_factor_validation(self):
        """测试BMI风险因素验证"""
        result = validate_risk_factor_value(RiskFactorType.BMI, 25.0)
        assert result == 25.0
        
        with pytest.raises(RiskFactorValidationError):
            validate_risk_factor_value(RiskFactorType.BMI, 70.0)
    
    def test_boolean_factor_validation(self):
        """测试布尔型风险因素验证"""
        result = validate_risk_factor_value(RiskFactorType.FAMILY_HISTORY, True)
        assert result is True
        
        result = validate_risk_factor_value(RiskFactorType.FAMILY_HISTORY, "yes")
        assert result is True
    
    def test_sedentary_factor_validation(self):
        """测试久坐时间风险因素验证"""
        result = validate_risk_factor_value(RiskFactorType.SEDENTARY_LIFESTYLE, 8.0)
        assert result == 8.0
        
        with pytest.raises(RiskFactorValidationError):
            validate_risk_factor_value(RiskFactorType.SEDENTARY_LIFESTYLE, 25.0)
    
    def test_invalid_factor_type(self):
        """测试无效风险因素类型"""
        with pytest.raises(RiskFactorValidationError, match="factor_type必须是RiskFactorType枚举"):
            validate_risk_factor_value("invalid_type", 25.0)


class TestRiskFactorWeightValidation:
    """测试风险因素权重验证"""
    
    def test_valid_weights(self):
        """测试有效权重"""
        assert validate_risk_factor_weight(1.0) == 1.0
        assert validate_risk_factor_weight(2.5) == 2.5
        assert validate_risk_factor_weight(10.0) == 10.0
        assert validate_risk_factor_weight(0) == 0.0
    
    def test_invalid_weight_range(self):
        """测试无效权重范围"""
        with pytest.raises(RiskFactorValidationError, match="权重不能为负数"):
            validate_risk_factor_weight(-1.0)
        
        with pytest.raises(RiskFactorValidationError, match="权重不能超过10.0"):
            validate_risk_factor_weight(11.0)
    
    def test_invalid_weight_type(self):
        """测试无效权重类型"""
        with pytest.raises(RiskFactorValidationError, match="权重必须是数字类型"):
            validate_risk_factor_weight("invalid")


class TestRiskFactorProfileCompleteness:
    """测试风险因素档案完整性验证"""
    
    def test_complete_profile(self):
        """测试完整档案"""
        profile = RiskFactorProfile("test-complete")
        profile.add_risk_factor(RiskFactor(
            factor_type=RiskFactorType.FAMILY_HISTORY,
            value=True
        ))
        profile.add_risk_factor(RiskFactor(
            factor_type=RiskFactorType.BMI,
            value=25.0
        ))
        
        result = validate_risk_factor_profile_completeness(profile)
        
        assert result["is_complete"] is True
        assert len(result["missing_factors"]) == 0
        assert len(result["invalid_factors"]) == 0
    
    def test_incomplete_profile(self):
        """测试不完整档案"""
        profile = RiskFactorProfile("test-incomplete")
        profile.add_risk_factor(RiskFactor(
            factor_type=RiskFactorType.FAMILY_HISTORY,
            value=True
        ))
        # 缺少BMI
        
        with pytest.raises(RiskFactorValidationError, match="缺少必需的风险因素"):
            validate_risk_factor_profile_completeness(profile)
    
    def test_profile_with_invalid_values(self):
        """测试包含无效值的档案"""
        profile = RiskFactorProfile("test-invalid")
        profile.add_risk_factor(RiskFactor(
            factor_type=RiskFactorType.FAMILY_HISTORY,
            value=True
        ))
        # 添加无效BMI值（绕过RiskFactor的验证）
        invalid_bmi_factor = RiskFactor.__new__(RiskFactor)
        invalid_bmi_factor.factor_type = RiskFactorType.BMI
        invalid_bmi_factor.value = 70.0  # 超出范围
        invalid_bmi_factor.weight = 1.0
        invalid_bmi_factor.last_updated = profile.created_at
        invalid_bmi_factor.source = "test"
        invalid_bmi_factor.confidence_interval = None
        invalid_bmi_factor.reference = None
        
        profile.risk_factors[RiskFactorType.BMI] = invalid_bmi_factor
        
        with pytest.raises(RiskFactorValidationError, match="风险因素值无效"):
            validate_risk_factor_profile_completeness(profile)
    
    def test_custom_required_factors(self):
        """测试自定义必需因素"""
        profile = RiskFactorProfile("test-custom")
        profile.add_risk_factor(RiskFactor(
            factor_type=RiskFactorType.DIABETES,
            value=True
        ))
        
        # 使用自定义必需因素列表
        required_factors = [RiskFactorType.DIABETES]
        
        result = validate_risk_factor_profile_completeness(profile, required_factors)
        assert result["is_complete"] is True
    
    def test_invalid_profile_type(self):
        """测试无效档案类型"""
        with pytest.raises(RiskFactorValidationError, match="risk_profile必须是RiskFactorProfile类型"):
            validate_risk_factor_profile_completeness("invalid_profile")


class TestAgeRiskFactorConsistency:
    """测试年龄与风险因素一致性验证"""
    
    def test_adult_risk_factors(self):
        """测试成年人风险因素"""
        risk_factors = {
            "smoking_status": True,
            "alcohol_consumption": 5.0,
            "body_mass_index": 25.0
        }
        
        # 成年人应该通过验证
        assert validate_age_risk_factor_consistency(30.0, risk_factors) is True
    
    def test_minor_with_adult_factors(self):
        """测试未成年人有成人风险因素"""
        risk_factors = {
            "smoking_status": True,  # 未成年人不应该有吸烟
            "body_mass_index": 20.0
        }
        
        with pytest.raises(RiskFactorValidationError, match="未成年人.*不应该有.*风险因素"):
            validate_age_risk_factor_consistency(16.0, risk_factors)
    
    def test_elderly_low_bmi(self):
        """测试高龄人群低BMI"""
        risk_factors = {
            "body_mass_index": 17.0  # 过低BMI
        }
        
        with pytest.raises(RiskFactorValidationError, match="高龄人群.*BMI过低"):
            validate_age_risk_factor_consistency(85.0, risk_factors)
    
    def test_consistent_age_factors(self):
        """测试一致的年龄和风险因素"""
        # 年轻人的正常风险因素
        young_factors = {
            "body_mass_index": 22.0,
            "diet_quality": 80.0
        }
        assert validate_age_risk_factor_consistency(25.0, young_factors) is True
        
        # 中年人的正常风险因素
        middle_age_factors = {
            "body_mass_index": 26.0,
            "smoking_status": False,
            "alcohol_consumption": 3.0
        }
        assert validate_age_risk_factor_consistency(45.0, middle_age_factors) is True
        
        # 老年人的正常风险因素
        elderly_factors = {
            "body_mass_index": 24.0,
            "sedentary_lifestyle": 6.0
        }
        assert validate_age_risk_factor_consistency(70.0, elderly_factors) is True
