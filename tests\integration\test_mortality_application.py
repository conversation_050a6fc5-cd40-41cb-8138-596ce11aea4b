"""
死亡率应用集成测试

测试生命表和死亡率引擎的集成功能。
"""

import pytest
import numpy as np
from pathlib import Path

from src.modules.population import LifeTable, LifeTableType, PopulationGenerator
from src.core import MortalityEngine, Individual, Population, Gender, DiseaseState
from src.utils import ValidationError


class TestMortalityApplicationIntegration:
    """死亡率应用集成测试"""
    
    @pytest.fixture
    def life_table_with_china_data(self):
        """使用中国生命表数据"""
        life_table = LifeTable()
        
        # 检查中国生命表文件是否存在
        china_file = Path("data/life_tables/china_2020.csv")
        if china_file.exists():
            life_table.load_predefined_table(LifeTableType.CHINA_2020)
        else:
            # 如果文件不存在，跳过测试
            pytest.skip("中国生命表数据文件不存在")
        
        return life_table
    
    @pytest.fixture
    def mortality_engine_china(self, life_table_with_china_data):
        """使用中国生命表的死亡率引擎"""
        return MortalityEngine(
            life_table=life_table_with_china_data,
            random_seed=42,
            time_precision="monthly"
        )
    
    @pytest.fixture
    def test_population(self):
        """创建测试人群"""
        generator = PopulationGenerator(random_seed=42)
        
        population = generator.generate_population(
            size=1000,
            age_distribution={
                "type": "uniform",
                "min_age": 50,
                "max_age": 80
            },
            gender_distribution={
                "male_ratio": 0.5,
                "female_ratio": 0.5
            },
            show_progress=False
        )
        
        return population
    
    def test_life_table_loading_and_validation(self, life_table_with_china_data):
        """测试生命表加载和验证"""
        # 验证生命表已正确加载
        assert life_table_with_china_data.get_current_table_name() == "china_2020"
        
        # 验证数据质量
        validation_result = life_table_with_china_data.validate_table_data()
        
        assert validation_result["total_records"] > 0
        assert "male" in validation_result["genders"]
        assert "female" in validation_result["genders"]
        assert validation_result["age_range"]["min"] >= 0
        assert validation_result["age_range"]["max"] <= 120
        
        # 检查数据完整性
        for gender in validation_result["genders"]:
            completeness = validation_result["data_completeness"][gender]
            assert completeness["coverage_ratio"] > 0.8  # 至少80%覆盖率
    
    def test_mortality_rate_queries(self, life_table_with_china_data):
        """测试死亡率查询功能"""
        # 测试不同年龄的死亡率
        ages = [50, 60, 70, 80]
        
        for age in ages:
            male_rate = life_table_with_china_data.get_mortality_rate(age, "male")
            female_rate = life_table_with_china_data.get_mortality_rate(age, "female")
            
            # 验证死亡率在合理范围内
            assert 0 <= male_rate <= 1
            assert 0 <= female_rate <= 1
            
            # 验证死亡率随年龄增长
            if age > 50:
                prev_male_rate = life_table_with_china_data.get_mortality_rate(age - 10, "male")
                prev_female_rate = life_table_with_china_data.get_mortality_rate(age - 10, "female")
                
                assert male_rate >= prev_male_rate
                assert female_rate >= prev_female_rate
    
    def test_mortality_engine_individual_application(self, mortality_engine_china):
        """测试对个体应用死亡率"""
        # 创建不同年龄的个体
        individuals = [
            Individual(1945, Gender.MALE, "elderly_male"),    # 80岁
            Individual(1955, Gender.FEMALE, "senior_female"), # 70岁
            Individual(1965, Gender.MALE, "middle_male"),     # 60岁
            Individual(1975, Gender.FEMALE, "adult_female")   # 50岁
        ]
        
        death_counts = 0
        total_applications = 0
        
        # 模拟多年的死亡率应用
        for year in range(2025, 2035):  # 10年模拟
            for individual in individuals:
                if individual.is_alive():
                    death_occurred = mortality_engine_china.apply_mortality_to_individual(
                        individual=individual,
                        current_time=float(year),
                        time_step=1.0
                    )
                    
                    if death_occurred:
                        death_counts += 1
                    
                    total_applications += 1
        
        # 验证有一定比例的死亡发生
        if total_applications > 0:
            death_rate = death_counts / total_applications
            assert 0 <= death_rate <= 1
            
            # 对于老年人群，应该有一定的死亡率
            assert death_rate > 0  # 至少有一些死亡
    
    def test_mortality_engine_population_application(self, mortality_engine_china, test_population):
        """测试对人群应用死亡率"""
        initial_size = test_population.get_size()
        initial_alive = sum(1 for ind in test_population if ind.is_alive())
        
        # 应用一年的死亡率
        result = mortality_engine_china.apply_mortality_to_population(
            population=test_population,
            current_time=2025.0,
            time_step=1.0,
            show_progress=False
        )
        
        # 验证结果
        assert result.total_individuals == initial_size
        assert result.deaths_applied >= 0
        assert result.deaths_applied <= initial_alive
        assert result.natural_deaths + result.cancer_deaths == result.deaths_applied
        assert 0 <= result.death_rate <= 1
        assert result.application_time > 0
        
        # 验证人群状态
        final_alive = sum(1 for ind in test_population if ind.is_alive())
        assert final_alive == initial_alive - result.deaths_applied
    
    def test_population_survival_statistics(self, mortality_engine_china, test_population):
        """测试人群生存统计"""
        # 应用几年的死亡率
        for year in range(2025, 2030):
            mortality_engine_china.apply_mortality_to_population(
                population=test_population,
                current_time=float(year),
                show_progress=False
            )
        
        # 获取生存统计
        survival_stats = test_population.statistics.get_survival_statistics()
        
        assert "total_individuals" in survival_stats
        assert "alive_count" in survival_stats
        assert "dead_count" in survival_stats
        assert "survival_rate" in survival_stats
        assert "mortality_rate" in survival_stats
        
        # 验证统计一致性
        assert survival_stats["alive_count"] + survival_stats["dead_count"] == survival_stats["total_individuals"]
        assert abs(survival_stats["survival_rate"] + survival_stats["mortality_rate"] - 1.0) < 0.001
        
        # 验证年龄组生存率
        if "age_group_survival" in survival_stats:
            age_group_survival = survival_stats["age_group_survival"]
            for group_name, group_stats in age_group_survival.items():
                assert 0 <= group_stats["survival_rate"] <= 1
                assert group_stats["alive"] + (group_stats["total"] - group_stats["alive"]) == group_stats["total"]
    
    def test_survival_curve_calculation(self, test_population):
        """测试生存曲线计算"""
        # 计算生存曲线
        survival_curve = test_population.calculate_survival_curve(
            time_points=[0, 5, 10, 15, 20],
            reference_year=2025
        )
        
        assert "time_points" in survival_curve
        assert "survival_probabilities" in survival_curve
        assert "at_risk" in survival_curve
        assert "total_individuals" in survival_curve
        assert "total_events" in survival_curve
        
        # 验证生存概率递减
        probs = survival_curve["survival_probabilities"]
        for i in range(1, len(probs)):
            assert probs[i] <= probs[i-1]  # 生存概率应该递减或保持不变
        
        # 验证风险人数递减
        at_risk = survival_curve["at_risk"]
        for i in range(1, len(at_risk)):
            assert at_risk[i] <= at_risk[i-1]  # 风险人数应该递减或保持不变
    
    def test_cohort_survival_analysis(self, test_population):
        """测试队列生存分析"""
        # 按年龄组进行队列分析
        cohort_analysis = test_population.get_cohort_survival_analysis(
            cohort_definition="age_group",
            reference_year=2025
        )
        
        assert "cohort_definition" in cohort_analysis
        assert "reference_year" in cohort_analysis
        assert "cohorts" in cohort_analysis
        assert "total_cohorts" in cohort_analysis
        
        cohorts = cohort_analysis["cohorts"]
        assert len(cohorts) > 0
        
        # 验证每个队列的统计
        for cohort_name, cohort_stats in cohorts.items():
            assert "total_individuals" in cohort_stats
            assert "alive_count" in cohort_stats
            assert "dead_count" in cohort_stats
            assert "survival_rate" in cohort_stats
            assert "mean_age" in cohort_stats
            
            # 验证统计一致性
            total = cohort_stats["total_individuals"]
            alive = cohort_stats["alive_count"]
            dead = cohort_stats["dead_count"]
            
            assert alive + dead == total
            assert 0 <= cohort_stats["survival_rate"] <= 1
            assert cohort_stats["mean_age"] > 0
    
    def test_mortality_statistics_accuracy(self, mortality_engine_china, test_population):
        """测试死亡率统计准确性"""
        # 获取初始统计
        initial_stats = mortality_engine_china.get_population_mortality_statistics(
            population=test_population,
            current_time=2025.0
        )
        
        # 应用死亡率
        result = mortality_engine_china.apply_mortality_to_population(
            population=test_population,
            current_time=2025.0,
            show_progress=False
        )
        
        # 获取应用后统计
        final_stats = mortality_engine_china.get_population_mortality_statistics(
            population=test_population,
            current_time=2025.0
        )
        
        # 验证存活人数减少
        assert final_stats["total_alive"] == initial_stats["total_alive"] - result.deaths_applied
        
        # 验证平均死亡率在合理范围内
        assert 0 <= initial_stats["average_mortality_rate"] <= 1
        assert 0 <= final_stats["average_mortality_rate"] <= 1
    
    def test_application_history_tracking(self, mortality_engine_china, test_population):
        """测试应用历史跟踪"""
        # 清空历史
        mortality_engine_china.reset_history()
        
        # 应用多次死亡率
        years = [2025, 2026, 2027]
        for year in years:
            mortality_engine_china.apply_mortality_to_population(
                population=test_population,
                current_time=float(year),
                show_progress=False
            )
        
        # 验证历史记录
        history = mortality_engine_china.get_application_history()
        assert len(history) == len(years)
        
        # 验证历史记录的完整性
        for i, result in enumerate(history):
            assert isinstance(result, type(mortality_engine_china.get_application_history()[0]))
            assert result.total_individuals > 0
            assert result.deaths_applied >= 0
            assert result.application_time > 0
            
            # 验证死亡数量递减（因为人群在减少）
            if i > 0:
                prev_result = history[i-1]
                assert result.total_individuals <= prev_result.total_individuals
    
    def test_random_seed_reproducibility_integration(self, life_table_with_china_data):
        """测试随机种子可重现性的集成测试"""
        # 创建两个相同配置的引擎
        engine1 = MortalityEngine(life_table_with_china_data, random_seed=123)
        engine2 = MortalityEngine(life_table_with_china_data, random_seed=123)
        
        # 创建相同的人群
        generator = PopulationGenerator(random_seed=456)
        
        pop1 = generator.generate_population(
            size=100,
            age_distribution={"type": "uniform", "min_age": 60, "max_age": 70},
            gender_distribution={"male_ratio": 0.5, "female_ratio": 0.5},
            show_progress=False
        )
        
        generator = PopulationGenerator(random_seed=456)  # 重置生成器
        pop2 = generator.generate_population(
            size=100,
            age_distribution={"type": "uniform", "min_age": 60, "max_age": 70},
            gender_distribution={"male_ratio": 0.5, "female_ratio": 0.5},
            show_progress=False
        )
        
        # 应用死亡率
        result1 = engine1.apply_mortality_to_population(pop1, 2025.0, show_progress=False)
        result2 = engine2.apply_mortality_to_population(pop2, 2025.0, show_progress=False)
        
        # 验证结果一致性
        assert result1.deaths_applied == result2.deaths_applied
        assert result1.natural_deaths == result2.natural_deaths
        assert result1.cancer_deaths == result2.cancer_deaths
        
        # 验证个体状态一致性
        alive1 = sum(1 for ind in pop1 if ind.is_alive())
        alive2 = sum(1 for ind in pop2 if ind.is_alive())
        assert alive1 == alive2
