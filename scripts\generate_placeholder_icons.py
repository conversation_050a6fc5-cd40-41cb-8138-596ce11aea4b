#!/usr/bin/env python3
"""
生成占位符图标脚本

为结直肠癌筛查模拟器生成简单的占位符图标，
用于开发和测试阶段。正式发布前应替换为专业设计的图标。
"""

import os
from pathlib import Path

try:
    from PIL import Image, ImageDraw, ImageFont
    PIL_AVAILABLE = True
except ImportError:
    PIL_AVAILABLE = False
    print("警告: PIL/Pillow未安装，无法生成图标")


def create_placeholder_icon(size: int, output_path: Path) -> None:
    """创建占位符图标"""
    if not PIL_AVAILABLE:
        print(f"跳过创建 {output_path}，PIL不可用")
        return
    
    # 创建图像
    img = Image.new('RGBA', (size, size), (0, 0, 0, 0))
    draw = ImageDraw.Draw(img)
    
    # 背景圆形
    margin = size // 8
    draw.ellipse(
        [margin, margin, size - margin, size - margin],
        fill=(33, 150, 243, 255),  # 医疗蓝色
        outline=(25, 118, 210, 255),
        width=max(1, size // 32)
    )
    
    # 添加文字 "CRC"（Colorectal Cancer）
    try:
        # 尝试使用系统字体
        font_size = size // 4
        font = ImageFont.truetype("arial.ttf", font_size)
    except (OSError, IOError):
        # 如果系统字体不可用，使用默认字体
        font = ImageFont.load_default()
    
    text = "CRC"
    
    # 计算文字位置（居中）
    bbox = draw.textbbox((0, 0), text, font=font)
    text_width = bbox[2] - bbox[0]
    text_height = bbox[3] - bbox[1]
    
    x = (size - text_width) // 2
    y = (size - text_height) // 2
    
    # 绘制文字
    draw.text((x, y), text, fill=(255, 255, 255, 255), font=font)
    
    # 保存图像
    img.save(output_path, format='PNG')
    print(f"已创建占位符图标: {output_path}")


def generate_all_icons():
    """生成所有尺寸的占位符图标"""
    # 确保图标目录存在
    icons_dir = Path(__file__).parent.parent / "resources" / "icons"
    icons_dir.mkdir(parents=True, exist_ok=True)
    
    # 定义图标尺寸
    sizes = [16, 32, 48, 64, 128, 256]
    
    # 生成PNG图标
    for size in sizes:
        icon_path = icons_dir / f"app_{size}.png"
        create_placeholder_icon(size, icon_path)
    
    # 生成主图标（256x256）
    main_icon_path = icons_dir / "app.png"
    create_placeholder_icon(256, main_icon_path)
    
    # 创建ICO文件（Windows）
    if PIL_AVAILABLE:
        try:
            # 收集所有PNG图标
            png_images = []
            for size in [16, 32, 48, 64, 128, 256]:
                png_path = icons_dir / f"app_{size}.png"
                if png_path.exists():
                    png_images.append(Image.open(png_path))
            
            if png_images:
                # 保存为ICO文件
                ico_path = icons_dir / "app.ico"
                png_images[0].save(
                    ico_path,
                    format='ICO',
                    sizes=[(img.width, img.height) for img in png_images]
                )
                print(f"已创建ICO图标: {ico_path}")
                
                # 关闭图像
                for img in png_images:
                    img.close()
        
        except Exception as e:
            print(f"创建ICO文件失败: {e}")
    
    print("\n占位符图标生成完成！")
    print("注意: 这些是临时占位符图标，正式发布前请替换为专业设计的图标。")


def create_icon_info_file():
    """创建图标信息文件"""
    icons_dir = Path(__file__).parent.parent / "resources" / "icons"
    info_file = icons_dir / "icon_info.txt"
    
    with open(info_file, 'w', encoding='utf-8') as f:
        f.write("结直肠癌筛查模拟器 - 图标信息\n")
        f.write("=" * 40 + "\n\n")
        f.write("当前图标状态: 占位符图标\n")
        f.write("创建时间: 开发阶段\n")
        f.write("设计风格: 简单医疗主题\n")
        f.write("主要颜色: 医疗蓝色 (#2196F3)\n\n")
        f.write("包含的图标文件:\n")
        
        # 列出所有图标文件
        for icon_file in sorted(icons_dir.glob("app*")):
            if icon_file.suffix in ['.png', '.ico', '.icns']:
                f.write(f"- {icon_file.name}\n")
        
        f.write("\n注意事项:\n")
        f.write("- 这些是临时占位符图标\n")
        f.write("- 正式发布前需要专业设计的图标\n")
        f.write("- 建议联系UI设计师创建正式图标\n")
    
    print(f"已创建图标信息文件: {info_file}")


if __name__ == "__main__":
    print("正在生成占位符图标...")
    
    if not PIL_AVAILABLE:
        print("\n要生成图标，请安装Pillow库:")
        print("pip install Pillow")
        print("\n或者手动创建图标文件并放置在 resources/icons/ 目录中")
    else:
        generate_all_icons()
        create_icon_info_file()
