"""
人群生成器文件导入功能测试

测试PopulationGenerator类的文件导入功能
"""

import pytest
import tempfile
import pandas as pd
from pathlib import Path
import sys

# 添加src路径
sys.path.insert(0, "src")

from modules.population.population_generator import PopulationGenerator
from core import Gender, PathwayType
from utils import ValidationError


@pytest.fixture
def generator():
    """创建PopulationGenerator实例"""
    return PopulationGenerator()


@pytest.fixture
def sample_individual_csv():
    """创建个体格式CSV文件"""
    with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False, encoding='utf-8') as f:
        f.write("age,gender\n")
        f.write("45,M\n")
        f.write("50,F\n")
        f.write("55,M\n")
        f.write("60,F\n")
        f.write("65,M\n")
        f.write("70,F\n")
        
        temp_path = f.name
    
    yield temp_path
    
    # 清理
    Path(temp_path).unlink(missing_ok=True)


@pytest.fixture
def sample_aggregated_csv():
    """创建聚合格式CSV文件"""
    with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False, encoding='utf-8') as f:
        f.write("age,gender,number\n")
        f.write("45,M,100\n")
        f.write("45,F,95\n")
        f.write("50,M,120\n")
        f.write("50,F,115\n")
        f.write("55,M,140\n")
        f.write("55,F,135\n")
        
        temp_path = f.name
    
    yield temp_path
    
    # 清理
    Path(temp_path).unlink(missing_ok=True)


@pytest.fixture
def chinese_aggregated_csv():
    """创建中文聚合格式CSV文件"""
    with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False, encoding='utf-8') as f:
        f.write("年龄,性别,人数\n")
        f.write("45,男,100\n")
        f.write("45,女,95\n")
        f.write("50,男,120\n")
        f.write("50,女,115\n")
        
        temp_path = f.name
    
    yield temp_path
    
    # 清理
    Path(temp_path).unlink(missing_ok=True)


class TestFileValidation:
    """测试文件验证功能"""
    
    def test_validate_individual_format(self, generator, sample_individual_csv):
        """测试个体格式文件验证"""
        df = pd.read_csv(sample_individual_csv)
        column_mapping = generator._validate_population_file_columns(df)
        
        assert 'age' in column_mapping
        assert 'gender' in column_mapping
        assert 'count' not in column_mapping
    
    def test_validate_aggregated_format(self, generator, sample_aggregated_csv):
        """测试聚合格式文件验证"""
        df = pd.read_csv(sample_aggregated_csv)
        column_mapping = generator._validate_population_file_columns(df)
        
        assert 'age' in column_mapping
        assert 'gender' in column_mapping
        assert 'count' in column_mapping
    
    def test_validate_chinese_columns(self, generator, chinese_aggregated_csv):
        """测试中文列名验证"""
        df = pd.read_csv(chinese_aggregated_csv)
        column_mapping = generator._validate_population_file_columns(df)
        
        assert column_mapping['age'] == '年龄'
        assert column_mapping['gender'] == '性别'
        assert column_mapping['count'] == '人数'
    
    def test_missing_age_column_error(self, generator):
        """测试缺少年龄列的错误"""
        df = pd.DataFrame({'gender': ['M', 'F'], 'other': [1, 2]})
        
        with pytest.raises(ValidationError, match="未找到年龄列"):
            generator._validate_population_file_columns(df)
    
    def test_missing_gender_column_error(self, generator):
        """测试缺少性别列的错误"""
        df = pd.DataFrame({'age': [45, 50], 'other': [1, 2]})
        
        with pytest.raises(ValidationError, match="未找到性别列"):
            generator._validate_population_file_columns(df)


class TestDataProcessing:
    """测试数据处理功能"""
    
    def test_process_individual_data(self, generator, sample_individual_csv):
        """测试个体数据处理"""
        df = pd.read_csv(sample_individual_csv)
        column_mapping = {'age': 'age', 'gender': 'gender'}
        
        age_dist, gender_dist, total_size = generator._process_individual_population_data(df, column_mapping)
        
        # 检查结果
        assert isinstance(age_dist, dict)
        assert isinstance(gender_dist, dict)
        assert total_size == len(df)
        assert sum(age_dist.values()) == total_size
        assert sum(gender_dist.values()) == total_size
    
    def test_process_aggregated_data(self, generator, sample_aggregated_csv):
        """测试聚合数据处理"""
        df = pd.read_csv(sample_aggregated_csv)
        column_mapping = {'age': 'age', 'gender': 'gender', 'count': 'number'}
        
        age_dist, gender_dist, total_size = generator._process_aggregated_population_data(df, column_mapping)
        
        # 检查结果
        expected_total = df['number'].sum()
        assert total_size == expected_total
        assert sum(age_dist.values()) == expected_total
        assert sum(gender_dist.values()) == expected_total
    
    def test_gender_standardization(self, generator):
        """测试性别标准化"""
        gender_series = pd.Series(['M', 'F', '男', '女', 'MALE', 'FEMALE'])
        
        standardized = generator._standardize_gender_values(gender_series)
        
        # 检查所有值都被正确转换
        assert all(g in [Gender.MALE, Gender.FEMALE] for g in standardized)
        assert standardized.tolist().count(Gender.MALE) == 3
        assert standardized.tolist().count(Gender.FEMALE) == 3
    
    def test_invalid_gender_error(self, generator):
        """测试无效性别值错误"""
        gender_series = pd.Series(['X', 'Y'])
        
        with pytest.raises(ValidationError, match="无效的性别值"):
            generator._standardize_gender_values(gender_series)


class TestFileGeneration:
    """测试从文件生成人群"""
    
    def test_generate_from_individual_file(self, generator, sample_individual_csv):
        """测试从个体文件生成人群"""
        population = generator.generate_population_from_file(
            sample_individual_csv,
            show_progress=False
        )
        
        # 检查生成的人群
        assert len(population.individuals) == 6  # CSV文件中有6行数据
        
        # 检查年龄分布
        ages = [ind.get_age(2025) for ind in population.individuals]
        assert min(ages) >= 45
        assert max(ages) <= 70
    
    def test_generate_from_aggregated_file(self, generator, sample_aggregated_csv):
        """测试从聚合文件生成人群"""
        population = generator.generate_population_from_file(
            sample_aggregated_csv,
            show_progress=False
        )
        
        # 检查生成的人群规模
        expected_size = pd.read_csv(sample_aggregated_csv)['number'].sum()
        assert len(population.individuals) == expected_size
    
    def test_generate_with_pathway_distribution(self, generator, sample_aggregated_csv):
        """测试带疾病通路分布的生成"""
        pathway_dist = {
            'normal': 0.8,
            'adenoma': 0.15,
            'serrated': 0.05
        }
        
        population = generator.generate_population_from_file(
            sample_aggregated_csv,
            pathway_distribution=pathway_dist,
            show_progress=False
        )
        
        # 检查疾病通路分布
        pathway_counts = {}
        for ind in population.individuals:
            pathway = ind.pathway.value
            pathway_counts[pathway] = pathway_counts.get(pathway, 0) + 1
        
        total = len(population.individuals)
        normal_ratio = pathway_counts.get('normal', 0) / total
        
        # 允许一定的随机误差
        assert 0.7 <= normal_ratio <= 0.9
    
    def test_generation_summary(self, generator, sample_aggregated_csv):
        """测试生成摘要"""
        population = generator.generate_population_from_file(
            sample_aggregated_csv,
            show_progress=False
        )
        
        summary = generator.get_last_generation_summary()
        
        assert summary is not None
        assert summary.total_individuals == len(population.individuals)
        assert summary.generation_time > 0
        assert summary.age_stats is not None
        assert summary.gender_stats is not None


class TestErrorHandling:
    """测试错误处理"""
    
    def test_file_not_found_error(self, generator):
        """测试文件不存在错误"""
        with pytest.raises(FileNotFoundError):
            generator.generate_population_from_file("nonexistent_file.csv")
    
    def test_unsupported_file_format_error(self, generator):
        """测试不支持的文件格式错误"""
        with tempfile.NamedTemporaryFile(suffix='.txt', delete=False) as f:
            temp_path = f.name
        
        try:
            with pytest.raises(ValidationError, match="不支持的文件格式"):
                generator.generate_population_from_file(temp_path)
        finally:
            Path(temp_path).unlink(missing_ok=True)
    
    def test_invalid_age_data_error(self, generator):
        """测试无效年龄数据错误"""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False, encoding='utf-8') as f:
            f.write("age,gender\n")
            f.write("abc,M\n")
            f.write("def,F\n")
            temp_path = f.name
        
        try:
            with pytest.raises(ValidationError, match="年龄列包含无效数据"):
                generator.generate_population_from_file(temp_path, show_progress=False)
        finally:
            Path(temp_path).unlink(missing_ok=True)
    
    def test_age_range_validation_error(self, generator):
        """测试年龄范围验证错误"""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False, encoding='utf-8') as f:
            f.write("age,gender\n")
            f.write("150,M\n")
            f.write("200,F\n")
            temp_path = f.name
        
        try:
            with pytest.raises(ValidationError, match="年龄数据超出合理范围"):
                generator.generate_population_from_file(temp_path, show_progress=False)
        finally:
            Path(temp_path).unlink(missing_ok=True)


if __name__ == "__main__":
    pytest.main([__file__])
