"""
配置向导窗口

提供人群配置的图形界面，包括人群规模、年龄分布、性别比例等参数设置。
"""

import logging
from pathlib import Path
from typing import Dict, Any, Optional

from PyQt6.QtWidgets import (
    QDialog, QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
    QGroupBox, QLabel, QSpinBox, QDoubleSpinBox, QSlider,
    QComboBox, QPushButton, QFileDialog, QTextEdit,
    QFormLayout, QMessageBox, QProgressBar, QCheckBox
)
from PyQt6.QtCore import Qt, pyqtSignal, QThread, pyqtSlot
from PyQt6.QtGui import QFont, QPixmap

from src.core import Gender
from src.modules.population import PopulationGenerator


class PopulationConfigWidget(QWidget):
    """
    人群配置面板
    
    提供人群参数配置的用户界面，包括规模、年龄分布、性别比例等设置。
    """
    
    # 信号定义
    config_changed = pyqtSignal(dict)  # 配置变更信号
    preview_requested = pyqtSignal()   # 预览请求信号
    
    def __init__(self, parent=None):
        """初始化人群配置面板"""
        super().__init__(parent)
        
        self.config_data = {}
        self._setup_ui()
        self._connect_signals()
        self._set_default_values()
        
        logging.info("人群配置面板初始化完成")
    
    def _setup_ui(self) -> None:
        """设置用户界面"""
        layout = QVBoxLayout(self)
        
        # 基本配置组
        basic_group = self._create_basic_config_group()
        layout.addWidget(basic_group)
        
        # 年龄分布配置组
        age_group = self._create_age_distribution_group()
        layout.addWidget(age_group)
        
        # 性别分布配置组
        gender_group = self._create_gender_distribution_group()
        layout.addWidget(gender_group)
        
        # 文件导入配置组
        file_group = self._create_file_import_group()
        layout.addWidget(file_group)
        
        # 预览和操作按钮
        button_layout = self._create_button_layout()
        layout.addLayout(button_layout)
        
        # 配置预览区域
        preview_group = self._create_preview_group()
        layout.addWidget(preview_group)
    
    def _create_basic_config_group(self) -> QGroupBox:
        """创建基本配置组"""
        group = QGroupBox("基本配置")
        layout = QFormLayout(group)
        
        # 人群规模输入
        self.population_size_spin = QSpinBox()
        self.population_size_spin.setRange(1, 1_000_000)
        self.population_size_spin.setValue(10_000)
        self.population_size_spin.setSuffix(" 人")
        layout.addRow("人群规模:", self.population_size_spin)
        
        # 随机种子
        self.random_seed_spin = QSpinBox()
        self.random_seed_spin.setRange(1, 999999)
        self.random_seed_spin.setValue(42)
        layout.addRow("随机种子:", self.random_seed_spin)
        
        # 基准年份
        self.base_year_spin = QSpinBox()
        self.base_year_spin.setRange(1900, 2100)
        self.base_year_spin.setValue(2025)
        layout.addRow("基准年份:", self.base_year_spin)
        
        return group
    
    def _create_age_distribution_group(self) -> QGroupBox:
        """创建年龄分布配置组"""
        group = QGroupBox("年龄分布配置")
        layout = QGridLayout(group)
        
        # 分布类型选择
        layout.addWidget(QLabel("分布类型:"), 0, 0)
        self.age_distribution_combo = QComboBox()
        self.age_distribution_combo.addItems(["正态分布", "均匀分布", "自定义分布"])
        layout.addWidget(self.age_distribution_combo, 0, 1)
        
        # 年龄范围
        layout.addWidget(QLabel("最小年龄:"), 1, 0)
        self.min_age_spin = QSpinBox()
        self.min_age_spin.setRange(18, 100)
        self.min_age_spin.setValue(40)
        self.min_age_spin.setSuffix(" 岁")
        layout.addWidget(self.min_age_spin, 1, 1)
        
        layout.addWidget(QLabel("最大年龄:"), 1, 2)
        self.max_age_spin = QSpinBox()
        self.max_age_spin.setRange(18, 100)
        self.max_age_spin.setValue(80)
        self.max_age_spin.setSuffix(" 岁")
        layout.addWidget(self.max_age_spin, 1, 3)
        
        # 正态分布参数
        layout.addWidget(QLabel("平均年龄:"), 2, 0)
        self.mean_age_spin = QDoubleSpinBox()
        self.mean_age_spin.setRange(18.0, 100.0)
        self.mean_age_spin.setValue(60.0)
        self.mean_age_spin.setSuffix(" 岁")
        layout.addWidget(self.mean_age_spin, 2, 1)
        
        layout.addWidget(QLabel("标准差:"), 2, 2)
        self.std_age_spin = QDoubleSpinBox()
        self.std_age_spin.setRange(1.0, 20.0)
        self.std_age_spin.setValue(10.0)
        layout.addWidget(self.std_age_spin, 2, 3)
        
        return group
    
    def _create_gender_distribution_group(self) -> QGroupBox:
        """创建性别分布配置组"""
        group = QGroupBox("性别分布配置")
        layout = QGridLayout(group)
        
        # 性别比例滑块
        layout.addWidget(QLabel("男性比例:"), 0, 0)
        self.male_ratio_slider = QSlider(Qt.Orientation.Horizontal)
        self.male_ratio_slider.setRange(0, 100)
        self.male_ratio_slider.setValue(50)
        layout.addWidget(self.male_ratio_slider, 0, 1)
        
        self.male_ratio_label = QLabel("50%")
        layout.addWidget(self.male_ratio_label, 0, 2)
        
        layout.addWidget(QLabel("女性比例:"), 1, 0)
        self.female_ratio_label = QLabel("50%")
        layout.addWidget(self.female_ratio_label, 1, 1)
        
        return group
    
    def _create_file_import_group(self) -> QGroupBox:
        """创建文件导入配置组"""
        group = QGroupBox("文件导入配置")
        layout = QVBoxLayout(group)
        
        # 文件选择
        file_layout = QHBoxLayout()
        self.file_path_label = QLabel("未选择文件")
        file_layout.addWidget(self.file_path_label)
        
        self.browse_button = QPushButton("浏览...")
        file_layout.addWidget(self.browse_button)
        layout.addLayout(file_layout)
        
        # 文件格式选项
        format_layout = QHBoxLayout()
        self.excel_checkbox = QCheckBox("Excel (.xlsx)")
        self.csv_checkbox = QCheckBox("CSV (.csv)")
        format_layout.addWidget(self.excel_checkbox)
        format_layout.addWidget(self.csv_checkbox)
        layout.addLayout(format_layout)
        
        return group
    
    def _create_button_layout(self) -> QHBoxLayout:
        """创建按钮布局"""
        layout = QHBoxLayout()
        
        # 生成预览按钮
        self.preview_button = QPushButton("生成预览")
        self.preview_button.setStyleSheet("""
            QPushButton {
                background-color: #2196F3;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
        """)
        layout.addWidget(self.preview_button)
        
        # 重置按钮
        self.reset_button = QPushButton("重置")
        layout.addWidget(self.reset_button)
        
        # 应用按钮
        self.apply_button = QPushButton("应用配置")
        self.apply_button.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """)
        layout.addWidget(self.apply_button)
        
        layout.addStretch()
        return layout
    
    def _create_preview_group(self) -> QGroupBox:
        """创建预览组"""
        group = QGroupBox("配置预览")
        layout = QVBoxLayout(group)
        
        # 预览文本区域
        self.preview_text = QTextEdit()
        self.preview_text.setMaximumHeight(150)
        self.preview_text.setReadOnly(True)
        layout.addWidget(self.preview_text)
        
        return group

    def _connect_signals(self) -> None:
        """连接信号和槽"""
        # 人群规模变化
        self.population_size_spin.valueChanged.connect(self._update_config)
        self.random_seed_spin.valueChanged.connect(self._update_config)
        self.base_year_spin.valueChanged.connect(self._update_config)

        # 年龄分布变化
        self.age_distribution_combo.currentTextChanged.connect(self._update_config)
        self.min_age_spin.valueChanged.connect(self._update_config)
        self.max_age_spin.valueChanged.connect(self._update_config)
        self.mean_age_spin.valueChanged.connect(self._update_config)
        self.std_age_spin.valueChanged.connect(self._update_config)

        # 性别比例变化
        self.male_ratio_slider.valueChanged.connect(self._update_gender_ratio)

        # 文件选择
        self.browse_button.clicked.connect(self._browse_file)

        # 按钮事件
        self.preview_button.clicked.connect(self._generate_preview)
        self.reset_button.clicked.connect(self._reset_config)
        self.apply_button.clicked.connect(self._apply_config)

    def _set_default_values(self) -> None:
        """设置默认值"""
        self._update_config()
        self._update_gender_ratio()

    def _update_config(self) -> None:
        """更新配置数据"""
        self.config_data = {
            'population_size': self.population_size_spin.value(),
            'random_seed': self.random_seed_spin.value(),
            'base_year': self.base_year_spin.value(),
            'age_distribution': {
                'type': self.age_distribution_combo.currentText(),
                'min_age': self.min_age_spin.value(),
                'max_age': self.max_age_spin.value(),
                'mean': self.mean_age_spin.value(),
                'std': self.std_age_spin.value()
            },
            'gender_distribution': {
                'male_ratio': self.male_ratio_slider.value() / 100.0,
                'female_ratio': (100 - self.male_ratio_slider.value()) / 100.0
            }
        }

        # 发送配置变更信号
        self.config_changed.emit(self.config_data)

        # 更新预览
        self._update_preview_text()

    def _update_gender_ratio(self) -> None:
        """更新性别比例显示"""
        male_ratio = self.male_ratio_slider.value()
        female_ratio = 100 - male_ratio

        self.male_ratio_label.setText(f"{male_ratio}%")
        self.female_ratio_label.setText(f"{female_ratio}%")

        self._update_config()

    def _browse_file(self) -> None:
        """浏览文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "选择人群配置文件",
            "",
            "Excel文件 (*.xlsx);;CSV文件 (*.csv);;所有文件 (*)"
        )

        if file_path:
            self.file_path_label.setText(Path(file_path).name)
            logging.info(f"选择文件: {file_path}")

    def _generate_preview(self) -> None:
        """生成预览"""
        logging.info("生成人群配置预览")
        self.preview_requested.emit()

        # 这里可以添加实际的预览生成逻辑
        preview_text = f"""
人群配置预览：
- 人群规模: {self.config_data['population_size']:,} 人
- 随机种子: {self.config_data['random_seed']}
- 基准年份: {self.config_data['base_year']}
- 年龄分布: {self.config_data['age_distribution']['type']}
  * 年龄范围: {self.config_data['age_distribution']['min_age']}-{self.config_data['age_distribution']['max_age']} 岁
  * 平均年龄: {self.config_data['age_distribution']['mean']:.1f} 岁
  * 标准差: {self.config_data['age_distribution']['std']:.1f}
- 性别分布:
  * 男性: {self.config_data['gender_distribution']['male_ratio']:.1%}
  * 女性: {self.config_data['gender_distribution']['female_ratio']:.1%}
        """.strip()

        self.preview_text.setText(preview_text)

    def _reset_config(self) -> None:
        """重置配置"""
        logging.info("重置人群配置")

        # 重置所有控件到默认值
        self.population_size_spin.setValue(10_000)
        self.random_seed_spin.setValue(42)
        self.base_year_spin.setValue(2025)
        self.age_distribution_combo.setCurrentIndex(0)
        self.min_age_spin.setValue(40)
        self.max_age_spin.setValue(80)
        self.mean_age_spin.setValue(60.0)
        self.std_age_spin.setValue(10.0)
        self.male_ratio_slider.setValue(50)

        # 清空文件选择
        self.file_path_label.setText("未选择文件")
        self.excel_checkbox.setChecked(False)
        self.csv_checkbox.setChecked(False)

        # 清空预览
        self.preview_text.clear()

    def _apply_config(self) -> None:
        """应用配置"""
        logging.info("应用人群配置")

        # 验证配置
        if not self._validate_config():
            return

        # 这里可以添加实际的配置应用逻辑
        QMessageBox.information(self, "成功", "人群配置已应用")

    def _validate_config(self) -> bool:
        """验证配置"""
        # 检查年龄范围
        if self.min_age_spin.value() >= self.max_age_spin.value():
            QMessageBox.warning(self, "配置错误", "最小年龄必须小于最大年龄")
            return False

        # 检查平均年龄是否在合理范围内
        mean_age = self.mean_age_spin.value()
        min_age = self.min_age_spin.value()
        max_age = self.max_age_spin.value()

        if not (min_age <= mean_age <= max_age):
            QMessageBox.warning(self, "配置错误", "平均年龄必须在年龄范围内")
            return False

        return True

    def _update_preview_text(self) -> None:
        """更新预览文本"""
        if not self.config_data:
            return

        preview_text = f"""配置摘要：
人群规模: {self.config_data['population_size']:,} 人
年龄分布: {self.config_data['age_distribution']['type']} ({self.config_data['age_distribution']['min_age']}-{self.config_data['age_distribution']['max_age']} 岁)
性别比例: 男性 {self.config_data['gender_distribution']['male_ratio']:.1%}, 女性 {self.config_data['gender_distribution']['female_ratio']:.1%}"""

        self.preview_text.setText(preview_text)

    def get_config(self) -> Dict[str, Any]:
        """获取当前配置"""
        return self.config_data.copy()


class ConfigWizard(QDialog):
    """
    配置向导对话框

    提供完整的人群配置向导界面。
    """

    def __init__(self, parent=None):
        """初始化配置向导"""
        super().__init__(parent)

        self.setWindowTitle("人群配置向导")
        self.setModal(True)
        self.resize(800, 600)

        self._setup_ui()

        logging.info("配置向导初始化完成")

    def _setup_ui(self) -> None:
        """设置用户界面"""
        layout = QVBoxLayout(self)

        # 添加配置面板
        self.config_widget = PopulationConfigWidget()
        layout.addWidget(self.config_widget)

        # 添加对话框按钮
        button_layout = QHBoxLayout()

        self.ok_button = QPushButton("确定")
        self.ok_button.clicked.connect(self.accept)
        button_layout.addWidget(self.ok_button)

        self.cancel_button = QPushButton("取消")
        self.cancel_button.clicked.connect(self.reject)
        button_layout.addWidget(self.cancel_button)

        layout.addLayout(button_layout)

    def get_config(self) -> Dict[str, Any]:
        """获取配置数据"""
        return self.config_widget.get_config()
