#!/bin/bash
# macOS DMG创建脚本
# 用于创建结直肠癌筛查模拟器的macOS安装包

set -e

# 配置变量
APP_NAME="ColorectalCancerSimulator"
APP_VERSION="1.0.0"
DMG_NAME="${APP_NAME}-${APP_VERSION}"
SOURCE_DIR="../../dist/${APP_NAME}.app"
DMG_DIR="../../dist/macos"
TEMP_DMG_DIR="${DMG_DIR}/temp"

# 检查应用是否存在
if [ ! -d "$SOURCE_DIR" ]; then
    echo "错误: 找不到应用程序 $SOURCE_DIR"
    echo "请先运行 PyInstaller 构建应用程序"
    exit 1
fi

# 创建输出目录
mkdir -p "$DMG_DIR"
mkdir -p "$TEMP_DMG_DIR"

echo "正在创建 DMG 安装包..."

# 复制应用到临时目录
cp -R "$SOURCE_DIR" "$TEMP_DMG_DIR/"

# 创建应用程序文件夹的符号链接
ln -sf /Applications "$TEMP_DMG_DIR/Applications"

# 复制额外文件
if [ -f "../../README.md" ]; then
    cp "../../README.md" "$TEMP_DMG_DIR/使用说明.txt"
fi

if [ -f "../../LICENSE" ]; then
    cp "../../LICENSE" "$TEMP_DMG_DIR/许可协议.txt"
fi

# 设置DMG背景和图标位置（如果有自定义背景）
if [ -f "dmg_background.png" ]; then
    mkdir -p "$TEMP_DMG_DIR/.background"
    cp "dmg_background.png" "$TEMP_DMG_DIR/.background/"
fi

# 创建临时DMG
TEMP_DMG="${DMG_DIR}/${DMG_NAME}-temp.dmg"
hdiutil create -srcfolder "$TEMP_DMG_DIR" -volname "$APP_NAME" -fs HFS+ \
    -fsargs "-c c=64,a=16,e=16" -format UDRW -size 200m "$TEMP_DMG"

# 挂载临时DMG
MOUNT_DIR="/Volumes/$APP_NAME"
hdiutil attach "$TEMP_DMG" -readwrite -mount required

# 等待挂载完成
sleep 2

# 设置Finder视图选项
echo '
tell application "Finder"
    tell disk "'$APP_NAME'"
        open
        set current view of container window to icon view
        set toolbar visible of container window to false
        set statusbar visible of container window to false
        set the bounds of container window to {400, 100, 920, 440}
        set viewOptions to the icon view options of container window
        set arrangement of viewOptions to not arranged
        set icon size of viewOptions to 72
        set background picture of viewOptions to file ".background:dmg_background.png"
        set position of item "'$APP_NAME'.app" of container window to {160, 205}
        set position of item "Applications" of container window to {360, 205}
        close
        open
        update without registering applications
        delay 2
    end tell
end tell
' | osascript || true

# 卸载临时DMG
hdiutil detach "$MOUNT_DIR" || true

# 创建最终的压缩DMG
FINAL_DMG="${DMG_DIR}/${DMG_NAME}.dmg"
hdiutil convert "$TEMP_DMG" -format UDZO -imagekey zlib-level=9 -o "$FINAL_DMG"

# 清理临时文件
rm -f "$TEMP_DMG"
rm -rf "$TEMP_DMG_DIR"

echo "DMG 创建完成: $FINAL_DMG"

# 验证DMG
if hdiutil verify "$FINAL_DMG"; then
    echo "DMG 验证成功"
else
    echo "警告: DMG 验证失败"
    exit 1
fi

# 显示文件信息
ls -lh "$FINAL_DMG"

echo "macOS 安装包创建完成！"
