# 风险因素管理系统使用指南

## 概述

本指南介绍如何在结直肠癌筛查微观模拟模型中使用风险因素管理系统。该系统提供了完整的风险因素定义、配置、计算和验证功能。

## 快速开始

### 基本使用流程

```python
from src.modules.disease import RiskFactorType, RiskFactor, RiskFactorProfile
from src.modules.disease import RiskCalculator, RiskFactorWeights
from src.core import Individual, Gender

# 1. 创建个体
individual = Individual(
    birth_year=1970,
    gender=Gender.MALE
)

# 2. 添加风险因素
individual.add_risk_factor(RiskFactor(
    factor_type=RiskFactorType.FAMILY_HISTORY,
    value=True,
    source="patient_report"
))

individual.add_risk_factor(RiskFactor(
    factor_type=RiskFactorType.BMI,
    value=28.5,
    source="clinical_measurement"
))

# 3. 计算风险评分
calculator = RiskCalculator()
risk_score = calculator.calculate_risk_score(
    individual_id=individual.individual_id,
    risk_profile=individual.risk_factor_profile,
    age=individual.get_current_age(),
    gender=individual.gender.value
)

print(f"风险评分: {risk_score.total_score:.2f}")
print(f"风险等级: {risk_score.risk_level.value}")
```

## 风险因素类型

### 1. 布尔型风险因素

```python
# 家族史
family_history = RiskFactor(
    factor_type=RiskFactorType.FAMILY_HISTORY,
    value=True  # True表示有家族史
)

# 炎症性肠病
ibd = RiskFactor(
    factor_type=RiskFactorType.IBD,
    value=False  # False表示无IBD
)

# 糖尿病
diabetes = RiskFactor(
    factor_type=RiskFactorType.DIABETES,
    value=True  # True表示有糖尿病
)

# 吸烟状态
smoking = RiskFactor(
    factor_type=RiskFactorType.SMOKING,
    value=True  # True表示吸烟或曾经吸烟
)
```

### 2. 连续型风险因素

```python
# 体重指数 (BMI)
bmi = RiskFactor(
    factor_type=RiskFactorType.BMI,
    value=25.8,  # kg/m²
    source="clinical_measurement"
)

# 酒精消费量
alcohol = RiskFactor(
    factor_type=RiskFactorType.ALCOHOL_CONSUMPTION,
    value=15.0,  # 克/天
    source="patient_report"
)

# 久坐时间
sedentary = RiskFactor(
    factor_type=RiskFactorType.SEDENTARY_LIFESTYLE,
    value=8.5,  # 小时/天
    source="lifestyle_survey"
)

# 体力活动
activity = RiskFactor(
    factor_type=RiskFactorType.PHYSICAL_ACTIVITY,
    value=3.5,  # 小时/周
    source="fitness_tracker"
)

# 饮食质量评分
diet = RiskFactor(
    factor_type=RiskFactorType.DIET_QUALITY,
    value=75.0,  # 0-100分
    source="nutrition_assessment"
)
```

## 风险因素配置文件管理

### 创建和管理配置文件

```python
# 创建风险因素配置文件
profile = RiskFactorProfile(individual_id="patient_001")

# 批量添加风险因素
risk_factors = [
    RiskFactor(RiskFactorType.FAMILY_HISTORY, True),
    RiskFactor(RiskFactorType.BMI, 28.5),
    RiskFactor(RiskFactorType.SMOKING, False),
    RiskFactor(RiskFactorType.ALCOHOL_CONSUMPTION, 10.0)
]

for factor in risk_factors:
    profile.add_factor(factor)

# 检查特定风险因素
if profile.has_factor(RiskFactorType.FAMILY_HISTORY):
    family_history_value = profile.get_factor_value(RiskFactorType.FAMILY_HISTORY)
    print(f"家族史: {family_history_value}")

# 更新风险因素值
profile.update_factor_value(
    RiskFactorType.BMI, 
    27.2, 
    source="follow_up_measurement"
)

# 按类别获取风险因素
genetic_factors = profile.get_factors_by_category("genetic")
lifestyle_factors = profile.get_factors_by_category("lifestyle")
disease_factors = profile.get_factors_by_category("disease")
```

## 风险评分计算

### 基本风险评分

```python
# 使用默认权重配置
calculator = RiskCalculator()

# 计算风险评分
risk_score = calculator.calculate_risk_score(
    individual_id="patient_001",
    risk_profile=profile,
    age=54,
    gender="male",
    algorithm="multiplicative"  # 可选: "additive", "log_linear"
)

# 查看结果
print(f"总风险评分: {risk_score.total_score:.2f}")
print(f"风险等级: {risk_score.risk_level.value}")
print(f"年龄调整因子: {risk_score.age_adjustment:.2f}")
print(f"性别调整因子: {risk_score.gender_adjustment:.2f}")

# 查看各风险因素贡献
for factor_type, score in risk_score.component_scores.items():
    print(f"{factor_type.value}: {score:.2f}")
```

### 自定义权重配置

```python
# 使用自定义权重文件
custom_weights = RiskFactorWeights("path/to/custom_weights.yaml")
calculator = RiskCalculator(weights=custom_weights)

# 计算风险评分
risk_score = calculator.calculate_risk_score(
    individual_id="patient_001",
    risk_profile=profile,
    age=54,
    gender="male"
)
```

## 风险趋势分析

### 跟踪风险变化

```python
# 获取个体风险趋势
trend = calculator.get_risk_trend("patient_001")

if trend:
    print(f"趋势方向: {trend.trend_direction}")
    print(f"变化幅度: {trend.trend_magnitude:.2%}")
    print(f"历史评分数量: {len(trend.scores)}")
    
    # 查看最新评分
    latest_score = trend.scores[-1]
    print(f"最新评分: {latest_score.total_score:.2f}")
```

### 风险干预模拟

```python
# 模拟减重干预效果
interventions = {
    RiskFactorType.BMI: 24.0,  # 目标BMI
    RiskFactorType.PHYSICAL_ACTIVITY: 5.0,  # 增加体力活动
    RiskFactorType.ALCOHOL_CONSUMPTION: 5.0  # 减少酒精消费
}

intervention_result = calculator.simulate_risk_reduction(
    risk_profile=profile,
    age=54,
    gender="male",
    interventions=interventions
)

print(f"当前风险: {intervention_result['current_risk']['score']:.2f}")
print(f"干预后风险: {intervention_result['intervention_risk']['score']:.2f}")
print(f"风险降低: {intervention_result['risk_reduction']['percentage']:.1f}%")
```

## 数据验证

### 风险因素值验证

```python
from src.utils.validators import validate_risk_factor_value, RiskFactorValidationError

try:
    # 验证BMI值
    validated_bmi = validate_risk_factor_value(
        RiskFactorType.BMI, 
        28.5, 
        "patient_bmi"
    )
    
    # 验证酒精消费量
    validated_alcohol = validate_risk_factor_value(
        RiskFactorType.ALCOHOL_CONSUMPTION, 
        15.0, 
        "alcohol_intake"
    )
    
except RiskFactorValidationError as e:
    print(f"验证错误: {e}")
```

### 配置文件完整性检查

```python
from src.utils.validators import (
    validate_risk_factor_profile, 
    validate_risk_factor_data_completeness
)

# 检查配置文件一致性
warnings = validate_risk_factor_profile(profile, age=54, gender="male")
for warning in warnings:
    print(f"警告: {warning}")

# 检查数据完整性
completeness = validate_risk_factor_data_completeness(profile)
print(f"数据完整性: {completeness['completeness_percentage']:.1f}%")
print(f"缺失因素: {completeness['missing_factors_list']}")
```

## 高级功能

### 识别高影响风险因素

```python
# 识别对风险评分影响最大的因素
high_impact_factors = calculator.identify_high_impact_factors(
    risk_profile=profile,
    age=54,
    gender="male"
)

print("高影响风险因素:")
for factor_type, impact in high_impact_factors[:3]:  # 前3个
    print(f"{factor_type.value}: 影响度 {impact:.2f}")
```

### 风险配置文件比较

```python
# 创建另一个配置文件进行比较
profile2 = RiskFactorProfile(individual_id="patient_002")
# ... 添加风险因素 ...

# 比较两个配置文件
comparison = calculator.compare_risk_profiles(
    profile1=profile,
    profile2=profile2,
    age=54,
    gender="male"
)

print(f"评分差异: {comparison['score_difference']:.2f}")
print(f"相对变化: {comparison['relative_change']:.2%}")
```

## 配置文件序列化

### 保存和加载配置文件

```python
import json

# 转换为字典格式
profile_dict = profile.to_dict()

# 保存到文件
with open("patient_001_risk_profile.json", "w") as f:
    json.dump(profile_dict, f, indent=2, ensure_ascii=False)

# 从文件加载
with open("patient_001_risk_profile.json", "r") as f:
    loaded_dict = json.load(f)

# 重建配置文件
loaded_profile = RiskFactorProfile.from_dict(loaded_dict)
```

## 最佳实践

### 1. 数据质量控制
- 始终验证输入数据
- 定期检查数据完整性
- 记录数据来源和更新时间

### 2. 风险评分解释
- 考虑置信区间
- 结合临床判断
- 定期更新评分

### 3. 性能优化
- 批量处理多个个体
- 缓存权重配置
- 定期清理历史数据

### 4. 错误处理
```python
try:
    risk_score = calculator.calculate_risk_score(...)
except RiskFactorValidationError as e:
    print(f"风险因素验证错误: {e}")
except Exception as e:
    print(f"计算错误: {e}")
```

## 常见问题

### Q: 如何处理缺失的风险因素？
A: 系统会使用默认权重1.0，但建议尽可能收集完整数据。

### Q: 风险评分的有效范围是什么？
A: 理论上无上限，但通常在0.5-10.0之间，>4.0为极高风险。

### Q: 如何更新权重配置？
A: 修改YAML配置文件或创建新的RiskFactorWeights实例。

### Q: 系统支持哪些算法？
A: 支持乘法模型、加法模型和对数线性模型。

## 技术支持

如有问题，请参考：
- API文档: `docs/risk_factors/api_reference.md`
- 科学依据: `docs/risk_factors/scientific_evidence.md`
- 示例代码: `examples/risk_factor_examples.py`
