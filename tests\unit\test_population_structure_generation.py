"""
人群结构文件生成功能测试

测试PopulationGenerator从Excel/CSV文件生成人群的功能。
"""

import pytest
import pandas as pd
from pathlib import Path
from unittest.mock import patch
import tempfile
import os

from src.modules.population import PopulationGenerator
from src.modules.population.population_generator import PopulationStructureRow
from src.core import Gender, PathwayType
from src.utils import ValidationError


class TestPopulationStructureRow:
    """测试PopulationStructureRow数据类"""
    
    def test_valid_structure_row(self):
        """测试有效的结构行数据"""
        row = PopulationStructureRow(age=50, gender="male", count=100)
        assert row.age == 50
        assert row.gender == "male"
        assert row.count == 100
    
    def test_gender_normalization(self):
        """测试性别值标准化"""
        test_cases = [
            ("male", "male"),
            ("Male", "male"),
            ("m", "male"),
            ("M", "male"),
            ("男", "male"),
            ("female", "female"),
            ("Female", "female"),
            ("f", "female"),
            ("F", "female"),
            ("女", "female")
        ]
        
        for input_gender, expected in test_cases:
            row = PopulationStructureRow(age=50, gender=input_gender, count=10)
            assert row.gender == expected
    
    def test_invalid_age(self):
        """测试无效年龄"""
        with pytest.raises(ValueError, match="年龄必须在0-150之间"):
            PopulationStructureRow(age=-1, gender="male", count=10)
        
        with pytest.raises(ValueError, match="年龄必须在0-150之间"):
            PopulationStructureRow(age=200, gender="male", count=10)
    
    def test_invalid_gender(self):
        """测试无效性别"""
        with pytest.raises(ValueError, match="性别值无效"):
            PopulationStructureRow(age=50, gender="invalid", count=10)
    
    def test_invalid_count(self):
        """测试无效人数"""
        with pytest.raises(ValueError, match="人数必须大于0"):
            PopulationStructureRow(age=50, gender="male", count=0)
        
        with pytest.raises(ValueError, match="人数必须大于0"):
            PopulationStructureRow(age=50, gender="male", count=-5)


class TestPopulationGeneratorStructureFile:
    """测试PopulationGenerator结构文件功能"""
    
    def setup_method(self):
        """测试设置"""
        self.generator = PopulationGenerator(random_seed=42)
        self.temp_dir = Path(tempfile.mkdtemp())
    
    def teardown_method(self):
        """测试清理"""
        # 清理临时文件
        import shutil
        if self.temp_dir.exists():
            shutil.rmtree(self.temp_dir)
    
    def create_test_csv(self, filename: str, data: list) -> Path:
        """创建测试CSV文件"""
        file_path = self.temp_dir / filename
        df = pd.DataFrame(data)
        df.to_csv(file_path, index=False)
        return file_path
    
    def create_test_excel(self, filename: str, data: list) -> Path:
        """创建测试Excel文件"""
        file_path = self.temp_dir / filename
        df = pd.DataFrame(data)
        df.to_excel(file_path, index=False)
        return file_path
    
    def test_load_valid_csv_structure(self):
        """测试加载有效的CSV结构文件"""
        data = [
            {"age": 50, "gender": "male", "count": 10},
            {"age": 50, "gender": "female", "count": 8},
            {"age": 60, "gender": "male", "count": 12},
            {"age": 60, "gender": "female", "count": 15}
        ]
        
        csv_file = self.create_test_csv("test_structure.csv", data)
        structure_data = self.generator._load_population_structure_file(csv_file)
        
        assert len(structure_data) == 4
        assert structure_data[0].age == 50
        assert structure_data[0].gender == "male"
        assert structure_data[0].count == 10
    
    def test_load_valid_excel_structure(self):
        """测试加载有效的Excel结构文件"""
        data = [
            {"age": 45, "gender": "female", "count": 20},
            {"age": 55, "gender": "male", "count": 25}
        ]
        
        excel_file = self.create_test_excel("test_structure.xlsx", data)
        structure_data = self.generator._load_population_structure_file(excel_file)
        
        assert len(structure_data) == 2
        assert structure_data[0].age == 45
        assert structure_data[0].gender == "female"
        assert structure_data[0].count == 20
    
    def test_file_not_found(self):
        """测试文件不存在"""
        non_existent_file = self.temp_dir / "non_existent.csv"
        
        with pytest.raises(FileNotFoundError, match="人群结构文件不存在"):
            self.generator._load_population_structure_file(non_existent_file)
    
    def test_unsupported_file_format(self):
        """测试不支持的文件格式"""
        txt_file = self.temp_dir / "test.txt"
        txt_file.write_text("some content")
        
        with pytest.raises(ValidationError, match="不支持的文件格式"):
            self.generator._load_population_structure_file(txt_file)
    
    def test_missing_required_columns(self):
        """测试缺少必需列"""
        data = [
            {"age": 50, "gender": "male"},  # 缺少count列
            {"age": 60, "gender": "female"}
        ]
        
        csv_file = self.create_test_csv("missing_columns.csv", data)
        
        with pytest.raises(ValidationError, match="文件缺少必需的列"):
            self.generator._load_population_structure_file(csv_file)
    
    def test_invalid_data_in_file(self):
        """测试文件中的无效数据"""
        data = [
            {"age": -5, "gender": "male", "count": 10},  # 无效年龄
        ]
        
        csv_file = self.create_test_csv("invalid_data.csv", data)
        
        with pytest.raises(ValidationError, match="数据无效"):
            self.generator._load_population_structure_file(csv_file)
    
    def test_generate_population_from_structure_file(self):
        """测试从结构文件生成人群"""
        data = [
            {"age": 50, "gender": "male", "count": 50},    # 50岁男性占50%
            {"age": 50, "gender": "female", "count": 30},  # 50岁女性占30%
            {"age": 60, "gender": "male", "count": 10},    # 60岁男性占10%
            {"age": 60, "gender": "female", "count": 10}   # 60岁女性占10%
        ]
        # 总计：100人，50岁占80%，60岁占20%，男性占60%，女性占40%

        csv_file = self.create_test_csv("population_structure.csv", data)

        target_size = 1000
        population = self.generator.generate_population_from_structure_file(
            csv_file, target_size=target_size, show_progress=False
        )

        # 验证生成的人群规模
        assert population.get_size() == target_size

        # 验证年龄分布比例（允许一定误差）
        age_50_count = sum(1 for ind in population.individuals.values() if ind.get_current_age() == 50)
        age_60_count = sum(1 for ind in population.individuals.values() if ind.get_current_age() == 60)

        age_50_ratio = age_50_count / target_size
        age_60_ratio = age_60_count / target_size

        assert 0.75 <= age_50_ratio <= 0.85  # 期望80%，允许误差
        assert 0.15 <= age_60_ratio <= 0.25  # 期望20%，允许误差

        # 验证性别分布比例
        male_count = sum(1 for ind in population.individuals.values() if ind.gender == Gender.MALE)
        female_count = sum(1 for ind in population.individuals.values() if ind.gender == Gender.FEMALE)

        male_ratio = male_count / target_size
        female_ratio = female_count / target_size

        assert 0.55 <= male_ratio <= 0.65   # 期望60%，允许误差
        assert 0.35 <= female_ratio <= 0.45 # 期望40%，允许误差
    
    def test_generate_with_pathway_distribution(self):
        """测试带疾病通路分布的生成"""
        data = [
            {"age": 55, "gender": "male", "count": 50},
            {"age": 55, "gender": "female", "count": 50}
        ]

        csv_file = self.create_test_csv("pathway_test.csv", data)

        pathway_distribution = {
            "adenoma_carcinoma_ratio": 0.8,
            "serrated_adenoma_ratio": 0.2
        }

        target_size = 200
        population = self.generator.generate_population_from_structure_file(
            csv_file,
            target_size=target_size,
            pathway_distribution=pathway_distribution,
            show_progress=False
        )

        # 验证人群规模
        assert population.get_size() == target_size

        # 验证疾病通路分布
        adenoma_count = sum(
            1 for ind in population.individuals.values()
            if ind.pathway_type == PathwayType.ADENOMA_CARCINOMA
        )
        serrated_count = sum(
            1 for ind in population.individuals.values()
            if ind.pathway_type == PathwayType.SERRATED_ADENOMA
        )

        total_with_pathway = adenoma_count + serrated_count
        assert total_with_pathway == target_size

        # 验证比例（允许一定误差）
        adenoma_ratio = adenoma_count / total_with_pathway
        assert 0.7 <= adenoma_ratio <= 0.9  # 期望0.8，允许误差
    
    def test_generation_summary(self):
        """测试生成摘要"""
        data = [
            {"age": 40, "gender": "male", "count": 60},
            {"age": 40, "gender": "female", "count": 40}
        ]

        csv_file = self.create_test_csv("summary_test.csv", data)

        target_size = 500
        population = self.generator.generate_population_from_structure_file(
            csv_file, target_size=target_size, show_progress=False
        )

        summary = self.generator.get_last_generation_summary()

        assert summary is not None
        assert summary.total_individuals == target_size
        assert summary.generation_time > 0
        assert "source_file" in summary.config_used
        assert summary.config_used["total_structure_rows"] == 2
        assert summary.config_used["target_size"] == target_size
        assert summary.config_used["actual_size"] == target_size

    def test_calculate_distribution_from_structure(self):
        """测试从结构数据计算分布比例"""
        data = [
            {"age": 50, "gender": "male", "count": 40},
            {"age": 50, "gender": "female", "count": 20},
            {"age": 60, "gender": "male", "count": 30},
            {"age": 60, "gender": "female", "count": 10}
        ]
        # 总计100人：50岁60人(40男+20女)，60岁40人(30男+10女)
        # 总体性别比：70男:30女

        csv_file = self.create_test_csv("distribution_test.csv", data)
        structure_data = self.generator._load_population_structure_file(csv_file)

        distribution = self.generator._calculate_distribution_from_structure(structure_data)

        # 验证总体统计
        assert distribution["total_count"] == 100
        assert len(distribution["unique_ages"]) == 2
        assert 50 in distribution["unique_ages"]
        assert 60 in distribution["unique_ages"]

        # 验证性别分布
        gender_dist = distribution["gender_distribution"]
        assert abs(gender_dist["male_ratio"] - 0.7) < 0.01  # 70%男性
        assert abs(gender_dist["female_ratio"] - 0.3) < 0.01  # 30%女性

        # 验证年龄分布
        age_dist = distribution["age_distribution"]

        # 50岁组：60人，占60%
        age_50 = age_dist[50]
        assert abs(age_50["total_ratio"] - 0.6) < 0.01
        assert abs(age_50["male_ratio"] - 2/3) < 0.01  # 40/60
        assert abs(age_50["female_ratio"] - 1/3) < 0.01  # 20/60

        # 60岁组：40人，占40%
        age_60 = age_dist[60]
        assert abs(age_60["total_ratio"] - 0.4) < 0.01
        assert abs(age_60["male_ratio"] - 0.75) < 0.01  # 30/40
        assert abs(age_60["female_ratio"] - 0.25) < 0.01  # 10/40
