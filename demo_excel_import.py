#!/usr/bin/env python3
"""
Excel导入功能演示

创建示例Excel文件并演示导入功能
"""

import pandas as pd
import numpy as np


def create_sample_files():
    """创建示例Excel文件"""
    
    print("创建Excel导入功能示例文件")
    print("="*50)
    
    # 1. 创建聚合格式Excel文件
    aggregated_data = {
        '年龄': [50, 50, 55, 55, 60, 60, 65, 65, 70, 70],
        '性别': ['男', '女', '男', '女', '男', '女', '男', '女', '男', '女'],
        '人数': [1200, 1150, 1100, 1050, 1000, 950, 900, 850, 800, 750]
    }
    
    df_agg = pd.DataFrame(aggregated_data)
    df_agg.to_excel("sample_aggregated.xlsx", index=False)
    
    print(f"✓ 创建聚合格式Excel文件: sample_aggregated.xlsx")
    print(f"  总人数: {df_agg['人数'].sum():,}")
    print(f"  年龄范围: {df_agg['年龄'].min()}-{df_agg['年龄'].max()}岁")
    
    # 2. 创建个体格式Excel文件
    np.random.seed(42)
    individual_data = {
        'age': [50, 52, 54, 56, 58, 60, 62, 64, 66, 68],
        'gender': ['M', 'F', 'M', 'F', 'M', 'F', 'M', 'F', 'M', 'F']
    }
    
    df_ind = pd.DataFrame(individual_data)
    df_ind.to_excel("sample_individual.xlsx", index=False)
    
    print(f"✓ 创建个体格式Excel文件: sample_individual.xlsx")
    print(f"  总人数: {len(df_ind)}")
    
    # 3. 创建混合格式Excel文件
    mixed_data = {
        'Age': [45, 50, 55, 60],
        '性别': ['Male', '女', 'M', 'Female'],
        'Count': [100, 120, 140, 110]
    }
    
    df_mixed = pd.DataFrame(mixed_data)
    df_mixed.to_excel("sample_mixed.xlsx", index=False)
    
    print(f"✓ 创建混合格式Excel文件: sample_mixed.xlsx")
    print("  展示中英文混合列名和性别值")
    
    return ["sample_aggregated.xlsx", "sample_individual.xlsx", "sample_mixed.xlsx"]


def test_file_reading():
    """测试文件读取功能"""
    
    print("\n测试Excel文件读取")
    print("="*50)
    
    files = ["sample_aggregated.xlsx", "sample_individual.xlsx", "sample_mixed.xlsx"]
    
    for file in files:
        try:
            print(f"\n--- 读取 {file} ---")
            df = pd.read_excel(file)
            print(f"✓ 成功读取，形状: {df.shape}")
            print("列名:", list(df.columns))
            print("前3行数据:")
            print(df.head(3))
            
            # 测试列名映射
            age_columns = ['age', '年龄', 'Age', 'AGE']
            gender_columns = ['gender', '性别', 'Gender', 'GENDER', 'sex']
            count_columns = ['count', 'number', '人数', '数量', 'Count', 'Number']
            
            column_mapping = {}
            
            for col in age_columns:
                if col in df.columns:
                    column_mapping['age'] = col
                    break
            
            for col in gender_columns:
                if col in df.columns:
                    column_mapping['gender'] = col
                    break
                    
            for col in count_columns:
                if col in df.columns:
                    column_mapping['count'] = col
                    break
            
            print(f"列映射: {column_mapping}")
            
        except Exception as e:
            print(f"✗ 读取失败: {e}")


def create_usage_guide():
    """创建使用指南"""
    
    guide = """# Excel文件导入功能使用指南

## 功能概述
PopulationGenerator类现在支持直接从Excel文件导入人群结构数据，支持以下格式：

### 支持的文件格式
- CSV (.csv): 支持UTF-8、GBK、GB2312等编码
- Excel (.xlsx, .xls): 读取第一个工作表

### 支持的数据格式

#### 1. 聚合格式
包含年龄、性别、人数三列：
```
年龄  性别  人数
50   男   1200
50   女   1150
55   男   1100
```

#### 2. 个体格式  
包含年龄、性别两列：
```
age  gender
50   M
55   F
60   M
```

### 支持的列名
- **年龄列**: age, 年龄, Age, AGE
- **性别列**: gender, 性别, Gender, GENDER, sex  
- **人数列**: count, number, 人数, 数量, Count, Number

### 支持的性别值
- **英文**: M, F, MALE, FEMALE, Male, Female, male, female
- **中文**: 男, 女, 男性, 女性

## 使用方法

```python
from src.modules.population import PopulationGenerator

# 创建生成器
generator = PopulationGenerator(random_seed=42)

# 从Excel文件生成人群
population = generator.generate_population_from_file(
    "sample_aggregated.xlsx",
    pathway_distribution={
        "adenoma_carcinoma_ratio": 0.85,
        "serrated_adenoma_ratio": 0.15
    },
    birth_year_base=2025,
    show_progress=True
)

# 查看结果
print(f"生成人群规模: {len(population.individuals)}")
stats = population.statistics
print(f"性别分布: {stats.get_gender_distribution()}")

# 获取生成摘要
summary = generator.get_last_generation_summary()
print(f"生成时间: {summary.generation_time:.3f}秒")
```

## 示例文件
- `sample_aggregated.xlsx`: 聚合格式示例
- `sample_individual.xlsx`: 个体格式示例  
- `sample_mixed.xlsx`: 混合格式示例
"""
    
    with open("Excel导入使用指南.md", "w", encoding="utf-8") as f:
        f.write(guide)
    
    print("✓ 创建使用指南: Excel导入使用指南.md")


if __name__ == "__main__":
    # 创建示例文件
    files = create_sample_files()
    
    # 测试文件读取
    test_file_reading()
    
    # 创建使用指南
    print()
    create_usage_guide()
    
    print(f"\n🎉 Excel导入功能演示完成!")
    print(f"\n📁 创建的文件:")
    for file in files:
        print(f"  - {file}")
    print(f"  - Excel导入使用指南.md")
    
    print(f"\n💡 提示:")
    print(f"  1. 这些示例文件可以直接用于测试Excel导入功能")
    print(f"  2. 支持中英文混合的列名和性别值")
    print(f"  3. 自动识别聚合格式和个体格式")
    print(f"  4. 详细使用方法请参考 Excel导入使用指南.md")
