"""
死亡率引擎单元测试

测试MortalityEngine类的死亡率应用和统计功能。
"""

import pytest
import pandas as pd
import numpy as np
from unittest.mock import Mock, patch
import tempfile
import os

from src.core.mortality_engine import MortalityEngine, MortalityApplicationResult
from src.core import Individual, Population, Gender, DiseaseState, CancerStage
from src.modules.population.life_table import LifeTable
from src.utils import ValidationError


class TestMortalityApplicationResult:
    """测试MortalityApplicationResult数据结构"""
    
    def test_mortality_application_result(self):
        """测试死亡率应用结果"""
        result = MortalityApplicationResult(
            total_individuals=1000,
            deaths_applied=50,
            natural_deaths=30,
            cancer_deaths=20,
            mortality_rate_applied=0.05,
            application_time=1.5
        )
        
        assert result.total_individuals == 1000
        assert result.deaths_applied == 50
        assert result.natural_deaths == 30
        assert result.cancer_deaths == 20
        assert result.death_rate == 0.05  # 50/1000
        assert result.application_time == 1.5
    
    def test_death_rate_calculation(self):
        """测试死亡率计算"""
        # 正常情况
        result = MortalityApplicationResult(
            total_individuals=100,
            deaths_applied=10,
            natural_deaths=6,
            cancer_deaths=4,
            mortality_rate_applied=0.1,
            application_time=1.0
        )
        assert result.death_rate == 0.1
        
        # 零个体情况
        result_zero = MortalityApplicationResult(
            total_individuals=0,
            deaths_applied=0,
            natural_deaths=0,
            cancer_deaths=0,
            mortality_rate_applied=0.0,
            application_time=0.0
        )
        assert result_zero.death_rate == 0.0


class TestMortalityEngine:
    """测试MortalityEngine类"""
    
    @pytest.fixture
    def sample_life_table_data(self):
        """创建示例生命表数据"""
        return """age,gender,mortality_rate
50,male,0.010
50,female,0.008
60,male,0.020
60,female,0.015
70,male,0.040
70,female,0.030
80,male,0.080
80,female,0.060"""
    
    @pytest.fixture
    def life_table(self, sample_life_table_data):
        """创建生命表实例"""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False) as f:
            f.write(sample_life_table_data)
            temp_path = f.name
        
        life_table = LifeTable()
        life_table.load_life_table(temp_path)
        
        # 清理临时文件
        os.unlink(temp_path)
        
        return life_table
    
    @pytest.fixture
    def mortality_engine(self, life_table):
        """创建死亡率引擎实例"""
        return MortalityEngine(
            life_table=life_table,
            random_seed=42,
            time_precision="monthly"
        )
    
    @pytest.fixture
    def sample_individual(self):
        """创建示例个体"""
        return Individual(
            birth_year=1960,  # 65岁（假设当前年份2025）
            gender=Gender.MALE,
            individual_id="test_individual"
        )
    
    @pytest.fixture
    def sample_population(self):
        """创建示例人群"""
        individuals = []
        for i in range(100):
            individual = Individual(
                birth_year=1960 + (i % 20),  # 年龄范围45-65岁
                gender=Gender.MALE if i % 2 == 0 else Gender.FEMALE,
                individual_id=f"person_{i}"
            )
            individuals.append(individual)
        
        return Population(individuals)
    
    def test_initialization(self, life_table):
        """测试初始化"""
        engine = MortalityEngine(life_table, random_seed=123, time_precision="quarterly")
        
        assert engine.life_table == life_table
        assert engine.time_precision == "quarterly"
        assert len(engine._application_history) == 0
        assert engine._time_divisions["quarterly"] == 4
    
    def test_invalid_time_precision(self, life_table):
        """测试无效的时间精度"""
        with pytest.raises(ValidationError, match="不支持的时间精度"):
            MortalityEngine(life_table, time_precision="invalid")
    
    def test_apply_mortality_to_individual_alive(self, mortality_engine, sample_individual):
        """测试对存活个体应用死亡率"""
        # 设置固定的随机数以确保可预测的结果
        with patch.object(mortality_engine._random_generator, 'random', return_value=0.5):
            death_occurred = mortality_engine.apply_mortality_to_individual(
                individual=sample_individual,
                current_time=2025.0,
                time_step=1.0
            )
            
            # 由于死亡率较低，0.5的随机数不应导致死亡
            assert not death_occurred
            assert sample_individual.is_alive()
    
    def test_apply_mortality_to_individual_death(self, mortality_engine, sample_individual):
        """测试个体死亡情况"""
        # 设置很小的随机数以确保死亡
        with patch.object(mortality_engine._random_generator, 'random', return_value=0.001):
            death_occurred = mortality_engine.apply_mortality_to_individual(
                individual=sample_individual,
                current_time=2025.0,
                time_step=1.0
            )
            
            assert death_occurred
            assert not sample_individual.is_alive()
            assert sample_individual.current_disease_state.is_death()
    
    def test_apply_mortality_to_dead_individual(self, mortality_engine, sample_individual):
        """测试对已死亡个体应用死亡率"""
        # 先让个体死亡
        sample_individual.transition_to_state(DiseaseState.DEATH_OTHER)
        
        death_occurred = mortality_engine.apply_mortality_to_individual(
            individual=sample_individual,
            current_time=2025.0,
            time_step=1.0
        )
        
        # 已死亡的个体不应再次死亡
        assert not death_occurred
    
    def test_convert_annual_to_step_rate(self, mortality_engine):
        """测试年度死亡率转换"""
        annual_rate = 0.12  # 12%年度死亡率
        
        # 月度转换
        monthly_rate = mortality_engine._convert_annual_to_step_rate(
            annual_rate, time_step=1.0, time_divisions=12
        )
        
        # 验证月度死亡率小于年度死亡率
        assert monthly_rate < annual_rate
        assert monthly_rate > 0
        
        # 验证12个月的累积概率接近年度死亡率
        cumulative_survival = (1 - monthly_rate) ** 12
        cumulative_death_rate = 1 - cumulative_survival
        assert abs(cumulative_death_rate - annual_rate) < 0.01
    
    def test_determine_death_cause_normal_individual(self, mortality_engine, sample_individual):
        """测试正常个体的死亡原因判定"""
        death_state = mortality_engine._determine_death_cause(sample_individual)
        assert death_state == DiseaseState.DEATH_OTHER
    
    def test_determine_death_cause_cancer_individual(self, mortality_engine):
        """测试癌症个体的死亡原因判定"""
        cancer_individual = Individual(
            birth_year=1960,
            gender=Gender.MALE,
            individual_id="cancer_patient"
        )
        
        # 设置为癌症状态
        cancer_individual.transition_to_state(
            DiseaseState.CLINICAL_CANCER,
            cancer_stage=CancerStage.STAGE_IV
        )
        
        # 测试多次判定以验证概率性
        death_causes = []
        for _ in range(100):
            death_state = mortality_engine._determine_death_cause(cancer_individual)
            death_causes.append(death_state)
        
        # 应该有癌症死亡和自然死亡两种情况
        unique_causes = set(death_causes)
        assert DiseaseState.DEATH_OTHER in unique_causes
        # 由于晚期癌症，应该有较高概率的癌症死亡
        cancer_deaths = sum(1 for cause in death_causes if cause == DiseaseState.DEATH_CANCER)
        assert cancer_deaths > 50  # 应该有超过50%的癌症死亡
    
    def test_get_cancer_death_probability(self, mortality_engine):
        """测试癌症死亡概率计算"""
        # 创建不同分期的癌症个体
        stages = [CancerStage.STAGE_I, CancerStage.STAGE_II, CancerStage.STAGE_III, CancerStage.STAGE_IV]
        probabilities = []
        
        for stage in stages:
            individual = Individual(
                birth_year=1960,
                gender=Gender.MALE,
                individual_id=f"cancer_{stage.value}"
            )
            individual.transition_to_state(DiseaseState.CLINICAL_CANCER, cancer_stage=stage)
            
            prob = mortality_engine._get_cancer_death_probability(individual)
            probabilities.append(prob)
        
        # 验证晚期癌症的死亡概率更高
        assert probabilities[0] < probabilities[1] < probabilities[2] < probabilities[3]
        assert all(0 <= prob <= 1 for prob in probabilities)
    
    def test_apply_mortality_to_population(self, mortality_engine, sample_population):
        """测试对人群应用死亡率"""
        initial_size = sample_population.get_size()
        
        result = mortality_engine.apply_mortality_to_population(
            population=sample_population,
            current_time=2025.0,
            time_step=1.0,
            show_progress=False
        )
        
        assert isinstance(result, MortalityApplicationResult)
        assert result.total_individuals == initial_size
        assert result.deaths_applied >= 0
        assert result.natural_deaths >= 0
        assert result.cancer_deaths >= 0
        assert result.deaths_applied == result.natural_deaths + result.cancer_deaths
        assert result.application_time > 0
    
    def test_get_population_mortality_statistics(self, mortality_engine, sample_population):
        """测试人群死亡率统计"""
        stats = mortality_engine.get_population_mortality_statistics(
            population=sample_population,
            current_time=2025.0
        )
        
        assert "total_alive" in stats
        assert "average_mortality_rate" in stats
        assert "mortality_by_age_group" in stats
        assert "mortality_by_gender" in stats
        
        assert stats["total_alive"] == sample_population.get_size()
        assert stats["average_mortality_rate"] >= 0
        
        # 验证年龄组统计
        age_groups = stats["mortality_by_age_group"]
        assert isinstance(age_groups, dict)
        
        # 验证性别统计
        gender_stats = stats["mortality_by_gender"]
        assert isinstance(gender_stats, dict)
        assert "male" in gender_stats or "female" in gender_stats
    
    def test_application_history(self, mortality_engine, sample_population):
        """测试应用历史记录"""
        # 初始历史应该为空
        history = mortality_engine.get_application_history()
        assert len(history) == 0
        
        # 应用死亡率
        mortality_engine.apply_mortality_to_population(
            population=sample_population,
            current_time=2025.0,
            show_progress=False
        )
        
        # 验证历史记录
        history = mortality_engine.get_application_history()
        assert len(history) == 1
        assert isinstance(history[0], MortalityApplicationResult)
        
        # 重置历史
        mortality_engine.reset_history()
        history = mortality_engine.get_application_history()
        assert len(history) == 0
    
    def test_random_seed_reproducibility(self, life_table):
        """测试随机种子的可重现性"""
        # 创建两个相同种子的引擎
        engine1 = MortalityEngine(life_table, random_seed=123)
        engine2 = MortalityEngine(life_table, random_seed=123)
        
        # 创建相同的个体
        individual1 = Individual(1960, Gender.MALE, "test1")
        individual2 = Individual(1960, Gender.MALE, "test2")
        
        # 应用死亡率应该产生相同结果
        result1 = engine1.apply_mortality_to_individual(individual1, 2025.0)
        result2 = engine2.apply_mortality_to_individual(individual2, 2025.0)
        
        assert result1 == result2
        assert individual1.is_alive() == individual2.is_alive()
    
    def test_set_random_seed(self, mortality_engine):
        """测试设置随机种子"""
        # 设置新的随机种子
        mortality_engine.set_random_seed(999)
        
        # 验证随机生成器已更新
        assert mortality_engine._random_generator.getstate()[1][0] != 42  # 不同于初始种子
    
    def test_time_precision_effects(self, life_table):
        """测试不同时间精度的影响"""
        engines = {
            "monthly": MortalityEngine(life_table, random_seed=42, time_precision="monthly"),
            "quarterly": MortalityEngine(life_table, random_seed=42, time_precision="quarterly"),
            "yearly": MortalityEngine(life_table, random_seed=42, time_precision="yearly")
        }
        
        # 测试时间分割数
        assert engines["monthly"]._time_divisions["monthly"] == 12
        assert engines["quarterly"]._time_divisions["quarterly"] == 4
        assert engines["yearly"]._time_divisions["yearly"] == 1
        
        # 测试死亡率转换
        annual_rate = 0.12
        monthly_rate = engines["monthly"]._convert_annual_to_step_rate(annual_rate, 1.0, 12)
        quarterly_rate = engines["quarterly"]._convert_annual_to_step_rate(annual_rate, 1.0, 4)
        yearly_rate = engines["yearly"]._convert_annual_to_step_rate(annual_rate, 1.0, 1)
        
        # 月度死亡率应该最小，年度死亡率应该等于原始死亡率
        assert monthly_rate < quarterly_rate < yearly_rate
        assert abs(yearly_rate - annual_rate) < 0.001
