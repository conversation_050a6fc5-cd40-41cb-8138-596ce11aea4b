#!/usr/bin/env python3
"""QA验证测试脚本"""

import sys
from pathlib import Path

def test_imports():
    """测试模块导入"""
    try:
        from src.interfaces.desktop.main import Application, MainWindow
        from src.interfaces.desktop.windows.config_wizard import PopulationConfigWidget
        from src.interfaces.desktop.widgets.simulation_control import SimulationControlWidget
        from src.interfaces.desktop.windows.results_viewer import ResultsWindow
        print("✅ 所有核心模块导入成功")
        return True
    except Exception as e:
        print(f"❌ 导入失败: {e}")
        return False

def test_organization_info():
    """测试组织信息"""
    try:
        import PyQt6.QtWidgets
        app = PyQt6.QtWidgets.QApplication([])
        from src.interfaces.desktop.main import Application
        test_app = Application([])
        
        org_name = test_app.organizationName()
        expected = "深圳市南山区慢性病防治院"
        
        if org_name == expected:
            print(f"✅ 组织信息正确: {org_name}")
            return True
        else:
            print(f"❌ 组织信息不匹配: 期望'{expected}', 实际'{org_name}'")
            return False
    except Exception as e:
        print(f"❌ 组织信息测试失败: {e}")
        return False

def test_icon_resources():
    """测试图标资源"""
    icons_dir = Path("resources/icons")
    required_icons = ["app.png", "app.ico", "app_16.png", "app_32.png"]
    
    missing = []
    for icon in required_icons:
        if not (icons_dir / icon).exists():
            missing.append(icon)
    
    if not missing:
        print("✅ 图标资源完整")
        return True
    else:
        print(f"❌ 缺少图标文件: {missing}")
        return False

if __name__ == "__main__":
    print("🔍 QA验证测试开始...")
    
    tests = [
        test_imports,
        test_organization_info, 
        test_icon_resources
    ]
    
    results = []
    for test in tests:
        results.append(test())
    
    if all(results):
        print("\n🎉 所有QA验证测试通过！")
        sys.exit(0)
    else:
        print(f"\n❌ {len([r for r in results if not r])} 个测试失败")
        sys.exit(1)
