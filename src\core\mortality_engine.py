"""
死亡率引擎模块

实现MortalityEngine类，管理死亡率应用和死亡判定。
支持随机抽样死亡判定、批量死亡率计算和死亡原因分类。
"""

import random
import numpy as np
from typing import Dict, Any, Optional, List, Union, Tuple
from dataclasses import dataclass
from datetime import datetime

from .enums import DiseaseState, Gender
from .individual import Individual
from .population import Population
from ..modules.population.life_table import LifeTable
from ..utils import ValidationError


@dataclass
class MortalityApplicationResult:
    """死亡率应用结果"""
    total_individuals: int
    deaths_applied: int
    natural_deaths: int
    cancer_deaths: int
    mortality_rate_applied: float
    application_time: float
    
    @property
    def death_rate(self) -> float:
        """计算实际死亡率"""
        return self.deaths_applied / self.total_individuals if self.total_individuals > 0 else 0.0


class MortalityEngine:
    """
    死亡率引擎类
    
    管理死亡率应用和死亡判定，支持：
    - 基于生命表的自然死亡率应用
    - 随机抽样死亡判定
    - 批量死亡率计算
    - 死亡原因分类和统计
    """
    
    def __init__(
        self,
        life_table: LifeTable,
        random_seed: Optional[int] = None,
        time_precision: str = "monthly"
    ):
        """
        初始化死亡率引擎
        
        Args:
            life_table: 生命表对象
            random_seed: 随机种子
            time_precision: 时间精度 ("monthly", "quarterly", "yearly")
        """
        self.life_table = life_table
        self.time_precision = time_precision
        self._random_generator = random.Random(random_seed)
        self._application_history: List[MortalityApplicationResult] = []
        
        # 时间精度配置
        self._time_divisions = {
            "monthly": 12,
            "quarterly": 4,
            "yearly": 1
        }
        
        if time_precision not in self._time_divisions:
            raise ValidationError(f"不支持的时间精度: {time_precision}")
    
    def apply_mortality_to_individual(
        self,
        individual: Individual,
        current_time: float,
        time_step: float = 1.0
    ) -> bool:
        """
        对单个个体应用死亡率
        
        Args:
            individual: 个体对象
            current_time: 当前时间（年）
            time_step: 时间步长（年）
            
        Returns:
            是否发生死亡
        """
        # 检查个体是否已死亡
        if not individual.is_alive():
            return False
        
        # 计算当前年龄
        age = individual.get_current_age(reference_year=int(current_time))
        
        # 获取年度死亡率
        annual_mortality_rate = self.life_table.get_mortality_rate(age, individual.gender)
        
        # 转换为时间步长对应的死亡概率
        time_divisions = self._time_divisions[self.time_precision]
        step_mortality_rate = self._convert_annual_to_step_rate(
            annual_mortality_rate, time_step, time_divisions
        )
        
        # 随机判定是否死亡
        if self._random_generator.random() < step_mortality_rate:
            # 判定死亡原因
            death_state = self._determine_death_cause(individual)
            
            # 执行状态转换
            success = individual.transition_to_state(
                death_state,
                additional_data={
                    "death_time": current_time,
                    "age_at_death": age,
                    "mortality_rate": annual_mortality_rate,
                    "cause": "natural" if death_state == DiseaseState.DEATH_OTHER else "cancer"
                }
            )
            
            return success
        
        return False
    
    def apply_mortality_to_population(
        self,
        population: Population,
        current_time: float,
        time_step: float = 1.0,
        show_progress: bool = False
    ) -> MortalityApplicationResult:
        """
        对整个人群应用死亡率
        
        Args:
            population: 人群对象
            current_time: 当前时间（年）
            time_step: 时间步长（年）
            show_progress: 是否显示进度
            
        Returns:
            死亡率应用结果
        """
        import time
        start_time = time.time()
        
        total_individuals = population.get_size()
        deaths_applied = 0
        natural_deaths = 0
        cancer_deaths = 0
        
        # 获取存活的个体
        alive_individuals = [ind for ind in population if ind.is_alive()]
        
        if show_progress and len(alive_individuals) > 1000:
            from tqdm import tqdm
            iterator = tqdm(alive_individuals, desc="应用死亡率", unit="个体")
        else:
            iterator = alive_individuals
        
        for individual in iterator:
            if self.apply_mortality_to_individual(individual, current_time, time_step):
                deaths_applied += 1
                
                # 统计死亡原因
                if individual.current_disease_state == DiseaseState.DEATH_OTHER:
                    natural_deaths += 1
                elif individual.current_disease_state == DiseaseState.DEATH_CANCER:
                    cancer_deaths += 1
        
        # 计算平均死亡率
        if len(alive_individuals) > 0:
            ages = [ind.get_current_age(reference_year=int(current_time)) for ind in alive_individuals]
            genders = [ind.gender for ind in alive_individuals]
            
            mortality_rates = [
                self.life_table.get_mortality_rate(age, gender)
                for age, gender in zip(ages, genders)
            ]
            avg_mortality_rate = np.mean(mortality_rates)
        else:
            avg_mortality_rate = 0.0
        
        # 创建结果对象
        result = MortalityApplicationResult(
            total_individuals=total_individuals,
            deaths_applied=deaths_applied,
            natural_deaths=natural_deaths,
            cancer_deaths=cancer_deaths,
            mortality_rate_applied=avg_mortality_rate,
            application_time=time.time() - start_time
        )
        
        # 记录应用历史
        self._application_history.append(result)
        
        return result
    
    def _convert_annual_to_step_rate(
        self,
        annual_rate: float,
        time_step: float,
        time_divisions: int
    ) -> float:
        """
        将年度死亡率转换为时间步长对应的死亡概率
        
        Args:
            annual_rate: 年度死亡率
            time_step: 时间步长（年）
            time_divisions: 年内时间分割数
            
        Returns:
            时间步长对应的死亡概率
        """
        # 计算单位时间的死亡概率
        # 使用公式: p_step = 1 - (1 - p_annual)^(time_step * time_divisions / divisions_per_year)
        step_rate = 1.0 - (1.0 - annual_rate) ** (time_step * time_divisions / time_divisions)
        return min(step_rate, 1.0)  # 确保概率不超过1
    
    def _determine_death_cause(self, individual: Individual) -> DiseaseState:
        """
        判定死亡原因
        
        Args:
            individual: 个体对象
            
        Returns:
            死亡状态
        """
        # 如果个体已患癌症，有一定概率死于癌症
        if individual.current_disease_state.is_cancer():
            # 根据癌症分期调整癌症死亡概率
            cancer_death_probability = self._get_cancer_death_probability(individual)
            
            if self._random_generator.random() < cancer_death_probability:
                return DiseaseState.DEATH_CANCER
        
        # 默认为自然死亡
        return DiseaseState.DEATH_OTHER
    
    def _get_cancer_death_probability(self, individual: Individual) -> float:
        """
        获取癌症死亡概率
        
        Args:
            individual: 个体对象
            
        Returns:
            癌症死亡概率
        """
        # 基础癌症死亡概率
        base_probability = 0.3
        
        # 根据癌症分期调整
        if individual.cancer_stage:
            stage_multipliers = {
                "stage_1": 0.2,
                "stage_2": 0.4,
                "stage_3": 0.7,
                "stage_4": 0.9
            }
            stage_key = individual.cancer_stage.value
            multiplier = stage_multipliers.get(stage_key, 0.5)
            base_probability *= multiplier
        
        return min(base_probability, 1.0)
    
    def get_population_mortality_statistics(
        self,
        population: Population,
        current_time: float
    ) -> Dict[str, Any]:
        """
        获取人群死亡率统计
        
        Args:
            population: 人群对象
            current_time: 当前时间
            
        Returns:
            死亡率统计字典
        """
        alive_individuals = [ind for ind in population if ind.is_alive()]
        
        if not alive_individuals:
            return {
                "total_alive": 0,
                "average_mortality_rate": 0.0,
                "mortality_by_age_group": {},
                "mortality_by_gender": {}
            }
        
        # 计算年龄和性别
        ages = [ind.get_current_age(reference_year=int(current_time)) for ind in alive_individuals]
        genders = [ind.gender for ind in alive_individuals]
        
        # 获取死亡率
        mortality_rates = [
            self.life_table.get_mortality_rate(age, gender)
            for age, gender in zip(ages, genders)
        ]
        
        # 按年龄组统计
        age_groups = {
            "18-30": [], "31-40": [], "41-50": [], "51-60": [],
            "61-70": [], "71-80": [], "81-90": [], "90+": []
        }
        
        for age, rate in zip(ages, mortality_rates):
            if age <= 30:
                age_groups["18-30"].append(rate)
            elif age <= 40:
                age_groups["31-40"].append(rate)
            elif age <= 50:
                age_groups["41-50"].append(rate)
            elif age <= 60:
                age_groups["51-60"].append(rate)
            elif age <= 70:
                age_groups["61-70"].append(rate)
            elif age <= 80:
                age_groups["71-80"].append(rate)
            elif age <= 90:
                age_groups["81-90"].append(rate)
            else:
                age_groups["90+"].append(rate)
        
        # 按性别统计
        gender_stats = {}
        for gender in set(genders):
            gender_rates = [
                rate for g, rate in zip(genders, mortality_rates) if g == gender
            ]
            gender_stats[gender.value if hasattr(gender, 'value') else str(gender)] = {
                "count": len(gender_rates),
                "average_mortality_rate": np.mean(gender_rates) if gender_rates else 0.0
            }
        
        return {
            "total_alive": len(alive_individuals),
            "average_mortality_rate": np.mean(mortality_rates),
            "mortality_by_age_group": {
                group: {
                    "count": len(rates),
                    "average_mortality_rate": np.mean(rates) if rates else 0.0
                }
                for group, rates in age_groups.items()
            },
            "mortality_by_gender": gender_stats
        }
    
    def get_application_history(self) -> List[MortalityApplicationResult]:
        """获取死亡率应用历史"""
        return self._application_history.copy()
    
    def reset_history(self):
        """重置应用历史"""
        self._application_history.clear()
    
    def set_random_seed(self, seed: int):
        """设置随机种子"""
        self._random_generator = random.Random(seed)
