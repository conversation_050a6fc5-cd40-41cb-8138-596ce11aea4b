#!/usr/bin/env python3
"""
桌面应用组件演示脚本

演示已实现的桌面应用组件功能，无需完整的依赖环境
"""

import sys
from pathlib import Path

# 添加src目录到Python路径
project_root = Path(__file__).parent
src_path = project_root / "src"
sys.path.insert(0, str(src_path))

def demo_config_data_structure():
    """演示人群配置数据结构"""
    print("=== 人群配置数据结构演示 ===")
    
    try:
        from interfaces.desktop.windows.config_wizard import PopulationConfig
        
        # 创建默认配置
        default_config = PopulationConfig()
        print(f"默认配置:")
        print(f"  人群规模: {default_config.size:,}")
        print(f"  年龄均值: {default_config.age_mean}岁")
        print(f"  年龄标准差: {default_config.age_std}岁")
        print(f"  年龄范围: {default_config.age_min}-{default_config.age_max}岁")
        print(f"  男性比例: {default_config.male_ratio:.1%}")
        print(f"  分布类型: {default_config.distribution_type}")
        
        # 创建自定义配置
        custom_config = PopulationConfig(
            size=50000,
            age_mean=65.0,
            age_std=12.0,
            age_min=45,
            age_max=85,
            male_ratio=0.52,
            distribution_type="normal"
        )
        
        print(f"\n自定义配置:")
        print(f"  人群规模: {custom_config.size:,}")
        print(f"  年龄均值: {custom_config.age_mean}岁")
        print(f"  男性比例: {custom_config.male_ratio:.1%}")
        
    except ImportError as e:
        print(f"无法导入配置模块: {e}")
        print("这是正常的，因为需要PyQt6依赖")

def demo_simulation_parameters():
    """演示模拟参数数据结构"""
    print("\n=== 模拟参数数据结构演示 ===")
    
    try:
        from interfaces.desktop.widgets.simulation_control import SimulationParameters, SimulationStatus
        
        # 创建默认参数
        default_params = SimulationParameters()
        print(f"默认模拟参数:")
        print(f"  开始年份: {default_params.start_year}")
        print(f"  模拟时长: {default_params.duration_years}年")
        print(f"  时间步长: {default_params.time_step}年")
        print(f"  随机种子: {default_params.random_seed}")
        print(f"  并行计算: {default_params.parallel_enabled}")
        print(f"  最大线程: {default_params.max_workers}")
        
        # 演示模拟状态
        print(f"\n模拟状态枚举:")
        for status in SimulationStatus:
            print(f"  {status.name}: {status.value}")
            
    except ImportError as e:
        print(f"无法导入模拟控制模块: {e}")
        print("这是正常的，因为需要PyQt6依赖")

def demo_validators():
    """演示验证器功能"""
    print("\n=== 表单验证器演示 ===")
    
    try:
        from interfaces.desktop.utils.validators import (
            NumericRangeValidator, RequiredFieldValidator, 
            PercentageValidator, ValidationResult
        )
        
        # 数字范围验证器
        age_validator = NumericRangeValidator(18, 100, "age", "年龄必须在18-100岁之间")
        
        test_ages = [25, 150, "abc", "", 65.5]
        print("年龄验证测试:")
        for age in test_ages:
            result = age_validator.validate(age)
            status = "✅" if result.is_valid else "❌"
            message = result.error_message if result.error_message else "有效"
            print(f"  {age} -> {status} {message}")
        
        # 百分比验证器
        percent_validator = PercentageValidator()
        
        test_percentages = [50, 120, -10, 85.5]
        print("\n百分比验证测试:")
        for percent in test_percentages:
            result = percent_validator.validate(percent)
            status = "✅" if result.is_valid else "❌"
            message = result.error_message if result.error_message else "有效"
            print(f"  {percent}% -> {status} {message}")
        
        # 必填字段验证器
        required_validator = RequiredFieldValidator("name", "姓名为必填项")
        
        test_values = ["张三", "", None, "   ", "李四"]
        print("\n必填字段验证测试:")
        for value in test_values:
            result = required_validator.validate(value)
            status = "✅" if result.is_valid else "❌"
            message = result.error_message if result.error_message else "有效"
            display_value = repr(value) if value is not None else "None"
            print(f"  {display_value} -> {status} {message}")
            
    except ImportError as e:
        print(f"无法导入验证器模块: {e}")

def demo_file_structure():
    """演示文件结构"""
    print("\n=== 项目文件结构演示 ===")
    
    # 显示已创建的主要文件
    important_files = [
        "src/interfaces/desktop/main.py",
        "src/interfaces/desktop/windows/config_wizard.py", 
        "src/interfaces/desktop/widgets/simulation_control.py",
        "src/interfaces/desktop/utils/validators.py",
        "src/interfaces/desktop/windows/results_viewer.py",
        "build.spec",
        "requirements.txt",
        "run_app.py"
    ]
    
    print("已创建的主要文件:")
    for file_path in important_files:
        full_path = project_root / file_path
        if full_path.exists():
            size = full_path.stat().st_size
            print(f"  ✅ {file_path} ({size:,} bytes)")
        else:
            print(f"  ❌ {file_path} (不存在)")
    
    # 显示测试文件
    test_files = [
        "tests/unit/test_desktop_app.py",
        "tests/unit/test_config_wizard.py",
        "tests/integration/test_desktop_integration.py"
    ]
    
    print("\n测试文件:")
    for file_path in test_files:
        full_path = project_root / file_path
        if full_path.exists():
            size = full_path.stat().st_size
            print(f"  ✅ {file_path} ({size:,} bytes)")
        else:
            print(f"  ❌ {file_path} (不存在)")

def demo_installation_info():
    """演示安装信息"""
    print("\n=== 安装和运行信息 ===")
    
    print("要运行桌面应用程序，请执行以下步骤:")
    print("\n1. 安装依赖:")
    print("   pip install -r requirements.txt")
    
    print("\n2. 运行应用程序:")
    print("   python run_app.py")
    
    print("\n3. 或者直接运行主模块:")
    print("   python -m src.interfaces.desktop.main")
    
    print("\n4. 运行测试:")
    print("   pytest tests/unit/ -v")
    print("   pytest tests/integration/ -v")
    
    print("\n5. 打包应用程序:")
    print("   pyinstaller build.spec")
    
    print("\n主要功能:")
    print("  • 人群配置界面 - 设置人群规模、年龄分布、性别比例")
    print("  • 模拟控制面板 - 开始/暂停/停止模拟，参数设置")
    print("  • 结果显示窗口 - 统计表格、图表可视化、数据导出")
    print("  • 表单验证系统 - 实时输入验证和错误提示")
    print("  • 跨平台支持 - Windows、macOS、Linux")

def main():
    """主演示函数"""
    print("结直肠癌筛查模拟器 - 桌面应用组件演示")
    print("=" * 50)
    
    demo_config_data_structure()
    demo_simulation_parameters()
    demo_validators()
    demo_file_structure()
    demo_installation_info()
    
    print("\n" + "=" * 50)
    print("演示完成！")
    print("\n注意: 完整的GUI功能需要安装PyQt6依赖包")
    print("执行 'pip install PyQt6' 来安装GUI依赖")

if __name__ == "__main__":
    main()
