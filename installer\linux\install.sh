#!/bin/bash
# Linux安装脚本
# 用于在Linux系统上安装结直肠癌筛查模拟器

set -e

# 配置变量
APP_NAME="ColorectalCancerSimulator"
APP_VERSION="1.0.0"
INSTALL_DIR="/opt/colorectal-cancer-simulator"
DESKTOP_FILE="colorectal-cancer-simulator.desktop"
ICON_NAME="colorectal-cancer-simulator"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印函数
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查是否为root用户
check_root() {
    if [ "$EUID" -ne 0 ]; then
        print_error "请使用sudo运行此脚本"
        exit 1
    fi
}

# 检查系统依赖
check_dependencies() {
    print_info "检查系统依赖..."
    
    # 检查必要的库
    local missing_deps=()
    
    # 检查Python 3
    if ! command -v python3 &> /dev/null; then
        missing_deps+=("python3")
    fi
    
    # 检查Qt库（通过pkg-config）
    if ! pkg-config --exists Qt6Core 2>/dev/null && ! pkg-config --exists Qt5Core 2>/dev/null; then
        print_warning "未检测到Qt库，应用程序可能无法正常运行"
    fi
    
    # 检查其他依赖
    local deps=("libgl1-mesa-glx" "libglib2.0-0" "libxkbcommon-x11-0")
    for dep in "${deps[@]}"; do
        if ! dpkg -l | grep -q "^ii  $dep "; then
            missing_deps+=("$dep")
        fi
    done
    
    if [ ${#missing_deps[@]} -ne 0 ]; then
        print_warning "缺少以下依赖项: ${missing_deps[*]}"
        print_info "尝试安装依赖项..."
        
        # 尝试使用apt安装（Ubuntu/Debian）
        if command -v apt &> /dev/null; then
            apt update
            apt install -y "${missing_deps[@]}"
        # 尝试使用yum安装（CentOS/RHEL）
        elif command -v yum &> /dev/null; then
            yum install -y "${missing_deps[@]}"
        # 尝试使用dnf安装（Fedora）
        elif command -v dnf &> /dev/null; then
            dnf install -y "${missing_deps[@]}"
        else
            print_error "无法自动安装依赖项，请手动安装: ${missing_deps[*]}"
            exit 1
        fi
    fi
}

# 创建安装目录
create_install_dir() {
    print_info "创建安装目录: $INSTALL_DIR"
    
    # 如果目录已存在，备份
    if [ -d "$INSTALL_DIR" ]; then
        print_warning "安装目录已存在，创建备份..."
        mv "$INSTALL_DIR" "${INSTALL_DIR}.backup.$(date +%Y%m%d_%H%M%S)"
    fi
    
    mkdir -p "$INSTALL_DIR"
    chmod 755 "$INSTALL_DIR"
}

# 复制应用程序文件
install_application() {
    print_info "安装应用程序文件..."
    
    # 检查源文件是否存在
    local source_dir="../../dist/$APP_NAME"
    if [ ! -d "$source_dir" ]; then
        print_error "找不到应用程序文件: $source_dir"
        print_info "请先运行 PyInstaller 构建应用程序"
        exit 1
    fi
    
    # 复制文件
    cp -r "$source_dir"/* "$INSTALL_DIR/"
    
    # 设置可执行权限
    chmod +x "$INSTALL_DIR/$APP_NAME"
    
    # 复制其他文件
    if [ -f "../../README.md" ]; then
        cp "../../README.md" "$INSTALL_DIR/"
    fi
    
    if [ -f "../../LICENSE" ]; then
        cp "../../LICENSE" "$INSTALL_DIR/"
    fi
}

# 安装桌面文件
install_desktop_file() {
    print_info "安装桌面文件..."
    
    # 复制桌面文件
    cp "$DESKTOP_FILE" "/usr/share/applications/"
    
    # 更新桌面文件中的路径
    sed -i "s|/opt/colorectal-cancer-simulator|$INSTALL_DIR|g" "/usr/share/applications/$DESKTOP_FILE"
    
    # 设置权限
    chmod 644 "/usr/share/applications/$DESKTOP_FILE"
    
    # 更新桌面数据库
    if command -v update-desktop-database &> /dev/null; then
        update-desktop-database /usr/share/applications/
    fi
}

# 安装图标
install_icons() {
    print_info "安装应用程序图标..."
    
    local icon_sizes=("16" "32" "48" "64" "128" "256")
    local icon_dir="/usr/share/icons/hicolor"
    
    for size in "${icon_sizes[@]}"; do
        local size_dir="$icon_dir/${size}x${size}/apps"
        mkdir -p "$size_dir"
        
        # 如果有对应尺寸的图标文件
        local icon_file="$INSTALL_DIR/resources/icons/app_${size}.png"
        if [ -f "$icon_file" ]; then
            cp "$icon_file" "$size_dir/$ICON_NAME.png"
        elif [ -f "$INSTALL_DIR/resources/icons/app.png" ]; then
            # 使用默认图标
            cp "$INSTALL_DIR/resources/icons/app.png" "$size_dir/$ICON_NAME.png"
        fi
    done
    
    # 更新图标缓存
    if command -v gtk-update-icon-cache &> /dev/null; then
        gtk-update-icon-cache -t "$icon_dir" 2>/dev/null || true
    fi
}

# 创建符号链接
create_symlink() {
    print_info "创建命令行符号链接..."
    
    local bin_link="/usr/local/bin/colorectal-simulator"
    
    # 删除旧的符号链接
    if [ -L "$bin_link" ]; then
        rm "$bin_link"
    fi
    
    # 创建新的符号链接
    ln -s "$INSTALL_DIR/$APP_NAME" "$bin_link"
    chmod +x "$bin_link"
}

# 设置文件关联
setup_file_associations() {
    print_info "设置文件关联..."
    
    # 创建MIME类型文件
    local mime_file="/usr/share/mime/packages/colorectal-simulator.xml"
    cat > "$mime_file" << EOF
<?xml version="1.0" encoding="UTF-8"?>
<mime-info xmlns="http://www.freedesktop.org/standards/shared-mime-info">
    <mime-type type="application/x-colorectal-simulation">
        <comment>Colorectal Cancer Simulation File</comment>
        <comment xml:lang="zh_CN">结直肠癌模拟文件</comment>
        <glob pattern="*.ccs"/>
        <glob pattern="*.simulation"/>
    </mime-type>
</mime-info>
EOF
    
    # 更新MIME数据库
    if command -v update-mime-database &> /dev/null; then
        update-mime-database /usr/share/mime/
    fi
}

# 主安装函数
main() {
    print_info "开始安装结直肠癌筛查模拟器 v$APP_VERSION"
    
    check_root
    check_dependencies
    create_install_dir
    install_application
    install_desktop_file
    install_icons
    create_symlink
    setup_file_associations
    
    print_success "安装完成！"
    print_info "您可以通过以下方式启动应用程序："
    print_info "1. 在应用程序菜单中查找 '结直肠癌筛查模拟器'"
    print_info "2. 在终端中运行: colorectal-simulator"
    print_info "3. 直接运行: $INSTALL_DIR/$APP_NAME"
}

# 卸载函数
uninstall() {
    print_info "卸载结直肠癌筛查模拟器..."
    
    # 删除安装目录
    if [ -d "$INSTALL_DIR" ]; then
        rm -rf "$INSTALL_DIR"
        print_success "已删除安装目录"
    fi
    
    # 删除桌面文件
    if [ -f "/usr/share/applications/$DESKTOP_FILE" ]; then
        rm "/usr/share/applications/$DESKTOP_FILE"
        print_success "已删除桌面文件"
    fi
    
    # 删除图标
    local icon_dir="/usr/share/icons/hicolor"
    find "$icon_dir" -name "$ICON_NAME.png" -delete 2>/dev/null || true
    
    # 删除符号链接
    if [ -L "/usr/local/bin/colorectal-simulator" ]; then
        rm "/usr/local/bin/colorectal-simulator"
        print_success "已删除命令行链接"
    fi
    
    # 删除MIME类型
    if [ -f "/usr/share/mime/packages/colorectal-simulator.xml" ]; then
        rm "/usr/share/mime/packages/colorectal-simulator.xml"
        update-mime-database /usr/share/mime/ 2>/dev/null || true
    fi
    
    print_success "卸载完成！"
}

# 检查命令行参数
case "${1:-install}" in
    install)
        main
        ;;
    uninstall)
        check_root
        uninstall
        ;;
    *)
        echo "用法: $0 [install|uninstall]"
        exit 1
        ;;
esac
