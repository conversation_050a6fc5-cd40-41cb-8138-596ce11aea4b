"""
综合风险评分计算引擎测试

测试风险评分计算、年龄调整、性别调整和风险分层功能。
"""

import pytest
from datetime import datetime

from src.modules.disease.risk_calculator import RiskCalculator, RiskScore, RiskLevel
from src.modules.disease.risk_factors import RiskFactorType, RiskFactor, RiskFactorProfile
from src.modules.disease.risk_weights import RiskFactorWeights
from src.core.enums import Gender


class TestRiskScore:
    """测试风险评分结果类"""
    
    def test_create_risk_score(self):
        """测试创建风险评分"""
        contributing_factors = {
            RiskFactorType.FAMILY_HISTORY: 2.5,
            RiskFactorType.BMI: 1.3
        }
        
        score = RiskScore(
            individual_id="test-123",
            base_score=3.25,
            age_adjusted_score=3.9,
            gender_adjusted_score=4.68,
            final_score=4.68,
            risk_level=RiskLevel.HIGH,
            contributing_factors=contributing_factors
        )
        
        assert score.individual_id == "test-123"
        assert score.base_score == 3.25
        assert score.age_adjusted_score == 3.9
        assert score.gender_adjusted_score == 4.68
        assert score.final_score == 4.68
        assert score.risk_level == RiskLevel.HIGH
        assert len(score.contributing_factors) == 2
    
    def test_risk_score_to_dict(self):
        """测试风险评分转换为字典"""
        contributing_factors = {
            RiskFactorType.FAMILY_HISTORY: 2.5
        }
        
        score = RiskScore(
            individual_id="test-456",
            base_score=2.5,
            age_adjusted_score=2.5,
            gender_adjusted_score=2.5,
            final_score=2.5,
            risk_level=RiskLevel.MODERATE,
            contributing_factors=contributing_factors
        )
        
        data = score.to_dict()
        
        assert data["individual_id"] == "test-456"
        assert data["base_score"] == 2.5
        assert data["final_score"] == 2.5
        assert data["risk_level"] == "moderate"
        assert "family_history" in data["contributing_factors"]
        assert "calculation_timestamp" in data


class TestRiskCalculator:
    """测试风险计算器"""
    
    def test_create_calculator(self):
        """测试创建计算器"""
        calculator = RiskCalculator()
        
        assert calculator.weights is not None
        assert isinstance(calculator.weights, RiskFactorWeights)
        assert len(calculator.calculation_history) == 0
    
    def test_create_calculator_with_custom_weights(self):
        """测试使用自定义权重创建计算器"""
        weights = RiskFactorWeights()
        calculator = RiskCalculator(weights)
        
        assert calculator.weights is weights
    
    def test_calculate_basic_risk_score(self):
        """测试基本风险评分计算"""
        calculator = RiskCalculator()
        
        # 创建风险档案
        profile = RiskFactorProfile("test-individual")
        profile.add_risk_factor(RiskFactor(
            factor_type=RiskFactorType.FAMILY_HISTORY,
            value=True,
            weight=2.5
        ))
        profile.add_risk_factor(RiskFactor(
            factor_type=RiskFactorType.BMI,
            value=28.0,
            weight=1.3
        ))
        
        # 计算风险评分
        score = calculator.calculate_risk_score(
            individual_id="test-individual",
            risk_profile=profile,
            age=55.0,
            gender=Gender.MALE
        )
        
        assert isinstance(score, RiskScore)
        assert score.individual_id == "test-individual"
        assert score.base_score > 1.0  # 应该有风险因素贡献
        assert score.final_score > score.base_score  # 年龄和性别调整后应该更高
        assert isinstance(score.risk_level, RiskLevel)
        assert len(score.contributing_factors) == 2
    
    def test_multiplicative_algorithm(self):
        """测试乘法算法"""
        calculator = RiskCalculator()
        
        profile = RiskFactorProfile("test-mult")
        profile.add_risk_factor(RiskFactor(
            factor_type=RiskFactorType.FAMILY_HISTORY,
            value=True
        ))
        profile.add_risk_factor(RiskFactor(
            factor_type=RiskFactorType.DIABETES,
            value=True
        ))
        
        score = calculator.calculate_risk_score(
            individual_id="test-mult",
            risk_profile=profile,
            age=50.0,
            gender=Gender.FEMALE,
            algorithm="multiplicative"
        )
        
        # 乘法算法：基础评分应该是各因素权重的乘积
        family_weight = calculator.weights.get_weight_value(RiskFactorType.FAMILY_HISTORY)
        diabetes_weight = calculator.weights.get_weight_value(RiskFactorType.DIABETES)
        expected_base = family_weight * diabetes_weight
        
        assert abs(score.base_score - expected_base) < 0.001
    
    def test_additive_algorithm(self):
        """测试加法算法"""
        calculator = RiskCalculator()
        
        profile = RiskFactorProfile("test-add")
        profile.add_risk_factor(RiskFactor(
            factor_type=RiskFactorType.FAMILY_HISTORY,
            value=True
        ))
        profile.add_risk_factor(RiskFactor(
            factor_type=RiskFactorType.DIABETES,
            value=True
        ))
        
        score = calculator.calculate_risk_score(
            individual_id="test-add",
            risk_profile=profile,
            age=50.0,
            gender=Gender.FEMALE,
            algorithm="additive"
        )
        
        # 加法算法：基础评分应该是1.0 + 各因素权重减1的和
        family_weight = calculator.weights.get_weight_value(RiskFactorType.FAMILY_HISTORY)
        diabetes_weight = calculator.weights.get_weight_value(RiskFactorType.DIABETES)
        expected_base = 1.0 + (family_weight - 1.0) + (diabetes_weight - 1.0)
        
        assert abs(score.base_score - expected_base) < 0.001
    
    def test_age_adjustment(self):
        """测试年龄调整"""
        calculator = RiskCalculator()
        
        profile = RiskFactorProfile("test-age")
        profile.add_risk_factor(RiskFactor(
            factor_type=RiskFactorType.FAMILY_HISTORY,
            value=True
        ))
        
        # 计算不同年龄的风险评分
        score_young = calculator.calculate_risk_score(
            "test-age-young", profile, 40.0, Gender.FEMALE
        )
        score_old = calculator.calculate_risk_score(
            "test-age-old", profile, 70.0, Gender.FEMALE
        )
        
        # 年龄越大，调整后的评分应该越高
        assert score_old.age_adjusted_score > score_young.age_adjusted_score
        assert score_old.final_score > score_young.final_score
    
    def test_gender_adjustment(self):
        """测试性别调整"""
        calculator = RiskCalculator()
        
        profile = RiskFactorProfile("test-gender")
        profile.add_risk_factor(RiskFactor(
            factor_type=RiskFactorType.FAMILY_HISTORY,
            value=True
        ))
        
        # 计算不同性别的风险评分
        score_male = calculator.calculate_risk_score(
            "test-male", profile, 55.0, Gender.MALE
        )
        score_female = calculator.calculate_risk_score(
            "test-female", profile, 55.0, Gender.FEMALE
        )
        
        # 根据默认配置，男性风险应该更高
        gender_params = calculator.weights.get_gender_adjustment_params()
        if gender_params.get("enabled", True):
            male_mult = gender_params.get("male_multiplier", 1.2)
            female_mult = gender_params.get("female_multiplier", 1.0)
            
            if male_mult > female_mult:
                assert score_male.final_score > score_female.final_score
    
    def test_risk_level_determination(self):
        """测试风险等级确定"""
        calculator = RiskCalculator()
        
        # 创建不同风险水平的档案
        low_profile = RiskFactorProfile("low-risk")
        # 不添加任何风险因素，应该是低风险
        
        high_profile = RiskFactorProfile("high-risk")
        high_profile.add_risk_factor(RiskFactor(
            factor_type=RiskFactorType.FAMILY_HISTORY,
            value=True
        ))
        high_profile.add_risk_factor(RiskFactor(
            factor_type=RiskFactorType.IBD,
            value=True
        ))
        high_profile.add_risk_factor(RiskFactor(
            factor_type=RiskFactorType.BMI,
            value=35.0  # 高BMI
        ))
        
        low_score = calculator.calculate_risk_score(
            "low-risk", low_profile, 45.0, Gender.FEMALE
        )
        high_score = calculator.calculate_risk_score(
            "high-risk", high_profile, 65.0, Gender.MALE
        )
        
        # 验证风险等级
        assert low_score.risk_level in [RiskLevel.LOW, RiskLevel.MODERATE]
        assert high_score.risk_level in [RiskLevel.MODERATE, RiskLevel.HIGH]
        assert high_score.final_score > low_score.final_score
    
    def test_score_history_recording(self):
        """测试评分历史记录"""
        calculator = RiskCalculator()
        
        profile = RiskFactorProfile("history-test")
        profile.add_risk_factor(RiskFactor(
            factor_type=RiskFactorType.FAMILY_HISTORY,
            value=True
        ))
        
        # 计算多次评分
        for i in range(3):
            calculator.calculate_risk_score(
                "history-test", profile, 50.0 + i, Gender.MALE
            )
        
        # 检查历史记录
        history = calculator.get_score_history("history-test")
        assert len(history) == 3
        
        latest_score = calculator.get_latest_score("history-test")
        assert latest_score is not None
        assert latest_score == history[-1]
    
    def test_risk_trend_analysis(self):
        """测试风险趋势分析"""
        calculator = RiskCalculator()
        
        profile = RiskFactorProfile("trend-test")
        profile.add_risk_factor(RiskFactor(
            factor_type=RiskFactorType.BMI,
            value=25.0
        ))
        
        # 模拟BMI逐渐增加的趋势
        for i in range(5):
            profile.update_risk_factor(RiskFactorType.BMI, 25.0 + i * 2.0)
            calculator.calculate_risk_score(
                "trend-test", profile, 50.0, Gender.MALE
            )
        
        trend_analysis = calculator.analyze_risk_trend("trend-test")
        
        assert trend_analysis["trend"] in ["increasing", "stable", "decreasing"]
        assert trend_analysis["score_count"] == 5
        assert "latest_score" in trend_analysis
        assert "score_change" in trend_analysis
    
    def test_insufficient_data_trend(self):
        """测试数据不足时的趋势分析"""
        calculator = RiskCalculator()
        
        # 只计算一次评分
        profile = RiskFactorProfile("single-score")
        calculator.calculate_risk_score(
            "single-score", profile, 50.0, Gender.FEMALE
        )
        
        trend_analysis = calculator.analyze_risk_trend("single-score")
        
        assert trend_analysis["trend"] == "insufficient_data"
        assert trend_analysis["score_count"] == 1
    
    def test_compare_risk_profiles(self):
        """测试风险档案比较"""
        calculator = RiskCalculator()
        
        # 创建两个不同的风险档案
        profile1 = RiskFactorProfile("profile1")
        profile1.add_risk_factor(RiskFactor(
            factor_type=RiskFactorType.FAMILY_HISTORY,
            value=True
        ))
        
        profile2 = RiskFactorProfile("profile2")
        profile2.add_risk_factor(RiskFactor(
            factor_type=RiskFactorType.FAMILY_HISTORY,
            value=True
        ))
        profile2.add_risk_factor(RiskFactor(
            factor_type=RiskFactorType.BMI,
            value=30.0
        ))
        
        comparison = calculator.compare_risk_profiles(
            profile1, profile2, 50.0, 50.0, Gender.MALE, Gender.MALE
        )
        
        assert "profile1_score" in comparison
        assert "profile2_score" in comparison
        assert "score_difference" in comparison
        assert "contributing_factors_comparison" in comparison
        
        # profile2应该有更高的风险评分
        assert comparison["profile2_score"] > comparison["profile1_score"]
        assert comparison["score_difference"] > 0
    
    def test_calculator_statistics(self):
        """测试计算器统计信息"""
        calculator = RiskCalculator()
        
        # 初始统计
        stats = calculator.get_statistics()
        assert stats["total_calculations"] == 0
        assert stats["total_individuals"] == 0
        
        # 添加一些计算
        profile = RiskFactorProfile("stats-test")
        for i in range(3):
            calculator.calculate_risk_score(
                f"individual-{i}", profile, 50.0, Gender.MALE
            )
        
        stats = calculator.get_statistics()
        assert stats["total_calculations"] == 3
        assert stats["total_individuals"] == 3
        assert stats["average_calculations_per_individual"] == 1.0
    
    def test_clear_history(self):
        """测试清理历史记录"""
        calculator = RiskCalculator()
        
        profile = RiskFactorProfile("clear-test")
        calculator.calculate_risk_score(
            "individual1", profile, 50.0, Gender.MALE
        )
        calculator.calculate_risk_score(
            "individual2", profile, 50.0, Gender.FEMALE
        )
        
        assert len(calculator.calculation_history) == 2
        
        # 清理单个个体的历史
        calculator.clear_history("individual1")
        assert len(calculator.calculation_history) == 1
        assert "individual1" not in calculator.calculation_history
        
        # 清理所有历史
        calculator.clear_history()
        assert len(calculator.calculation_history) == 0
    
    def test_invalid_algorithm(self):
        """测试无效算法"""
        calculator = RiskCalculator()
        
        profile = RiskFactorProfile("invalid-algo")
        
        with pytest.raises(ValueError, match="不支持的算法"):
            calculator.calculate_risk_score(
                "invalid-algo", profile, 50.0, Gender.MALE, 
                algorithm="invalid_algorithm"
            )
    
    def test_empty_risk_profile(self):
        """测试空风险档案"""
        calculator = RiskCalculator()
        
        empty_profile = RiskFactorProfile("empty")
        
        score = calculator.calculate_risk_score(
            "empty", empty_profile, 50.0, Gender.FEMALE
        )
        
        # 空档案应该有基础评分1.0（乘法算法）
        assert score.base_score == 1.0
        assert len(score.contributing_factors) == 0
        assert score.risk_level == RiskLevel.LOW
    
    def test_continuous_factors_calculation(self):
        """测试连续型风险因素计算"""
        calculator = RiskCalculator()
        
        profile = RiskFactorProfile("continuous-test")
        profile.add_risk_factor(RiskFactor(
            factor_type=RiskFactorType.BMI,
            value=30.0  # 高BMI
        ))
        profile.add_risk_factor(RiskFactor(
            factor_type=RiskFactorType.SEDENTARY_LIFESTYLE,
            value=10.0  # 久坐10小时
        ))
        
        score = calculator.calculate_risk_score(
            "continuous-test", profile, 50.0, Gender.MALE
        )
        
        # 验证连续型因素有贡献
        assert RiskFactorType.BMI in score.contributing_factors
        assert RiskFactorType.SEDENTARY_LIFESTYLE in score.contributing_factors
        assert score.contributing_factors[RiskFactorType.BMI] > 1.0
        assert score.contributing_factors[RiskFactorType.SEDENTARY_LIFESTYLE] > 1.0
    
    def test_repr(self):
        """测试字符串表示"""
        calculator = RiskCalculator()
        
        profile = RiskFactorProfile("repr-test")
        calculator.calculate_risk_score(
            "individual1", profile, 50.0, Gender.MALE
        )
        calculator.calculate_risk_score(
            "individual2", profile, 50.0, Gender.FEMALE
        )
        
        repr_str = repr(calculator)
        
        assert "RiskCalculator" in repr_str
        assert "individuals=2" in repr_str
        assert "calculations=2" in repr_str
