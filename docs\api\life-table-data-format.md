# 生命表数据格式规范

## 概述

本文档定义了结直肠癌筛查微观模拟模型中生命表数据的标准格式和要求。生命表数据用于计算年龄和性别特异性死亡率，是模拟自然死亡的核心数据。

## 数据格式要求

### 文件格式

支持以下文件格式：
- **CSV格式** (推荐): `.csv`
- **Excel格式**: `.xlsx`, `.xls`

### 必需列

生命表数据文件必须包含以下三列：

| 列名 | 数据类型 | 描述 | 示例值 |
|------|----------|------|--------|
| `age` | 数值型 | 年龄（整数，0-120） | 0, 1, 2, ..., 100 |
| `gender` | 字符串 | 性别标识 | "male", "female" |
| `mortality_rate` | 数值型 | 年度死亡率（0-1之间的小数） | 0.00654, 0.01234 |

### 数据验证规则

#### 年龄列 (age)
- **数据类型**: 数值型（整数或浮点数）
- **取值范围**: 0 ≤ age ≤ 100
- **完整性**: 必须覆盖所需的年龄范围
- **连续性**: 建议年龄数据连续，避免大的间隙

#### 性别列 (gender)
- **数据类型**: 字符串
- **有效值**: 
  - 标准值: `"male"`, `"female"`
  - 兼容值: `"M"`, `"F"`, `"m"`, `"f"`, `"Male"`, `"Female"`
- **标准化**: 系统会自动将兼容值转换为标准值

#### 死亡率列 (mortality_rate)
- **数据类型**: 数值型（浮点数）
- **取值范围**: 0 ≤ mortality_rate ≤ 1
- **精度**: 建议保留5位小数
- **单位**: 年度死亡概率

## 标准数据文件

### 中国2020年生命表 (china_2020.csv)

基于中国国家统计局发布的2020年生命表数据。

**数据来源**: 中国国家统计局
**覆盖年龄**: 0-100岁
**数据年份**: 2020年
**文件位置**: `data/life_tables/china_2020.csv`

### WHO全球生命表 (who_global.csv)

基于世界卫生组织全球健康观察站数据。

**数据来源**: WHO Global Health Observatory
**覆盖年龄**: 0-100岁
**数据年份**: 2019年（最新可用数据）
**文件位置**: `data/life_tables/who_global.csv`

## 数据文件示例

### CSV格式示例

```csv
age,gender,mortality_rate
0,male,0.00654
0,female,0.00521
1,male,0.00043
1,female,0.00035
2,male,0.00029
2,female,0.00024
...
50,male,0.00293
50,female,0.00243
...
100,male,0.22900
100,female,0.20800
```

### Excel格式示例

Excel文件应包含相同的列结构，第一行为列标题。

## 数据质量检查

### 自动验证

系统会自动执行以下验证：

1. **格式验证**
   - 检查必需列是否存在
   - 验证数据类型是否正确
   - 检查文件是否可读

2. **数值范围验证**
   - 年龄范围: 0-100
   - 死亡率范围: 0-1
   - 性别值有效性

3. **数据完整性检查**
   - 检查缺失值
   - 验证年龄覆盖范围
   - 检查性别数据完整性

### 数据质量报告

使用 `LifeTable.validate_table_data()` 方法可以生成详细的数据质量报告：

```python
from src.modules.population import LifeTable

life_table = LifeTable()
life_table.load_life_table("data/life_tables/china_2020.csv")
validation_result = life_table.validate_table_data()

print(f"总记录数: {validation_result['total_records']}")
print(f"年龄范围: {validation_result['age_range']}")
print(f"性别覆盖: {validation_result['genders']}")
```

## 数据处理功能

### 插值计算

对于非整数年龄或缺失的年龄数据，系统支持线性插值：

```python
# 获取35.5岁男性的死亡率（自动插值）
mortality_rate = life_table.get_mortality_rate(35.5, "male")
```

### 数据平滑

支持对死亡率数据进行平滑处理：

```python
# 使用移动平均平滑
smoothed_table = life_table.smooth_mortality_rates(
    window_size=3, 
    method="moving_average"
)

# 使用指数平滑
smoothed_table = life_table.smooth_mortality_rates(
    window_size=5, 
    method="exponential"
)
```

## 自定义生命表

### 创建自定义生命表

1. **准备数据文件**
   - 按照标准格式创建CSV或Excel文件
   - 确保包含所有必需列
   - 验证数据质量

2. **加载自定义生命表**
   ```python
   life_table = LifeTable()
   custom_table = life_table.load_life_table(
       "path/to/custom_life_table.csv",
       table_name="custom_population"
   )
   ```

3. **验证和使用**
   ```python
   # 验证数据质量
   validation = life_table.validate_table_data("custom_population")
   
   # 设置为当前使用的生命表
   life_table.set_current_table("custom_population")
   
   # 查询死亡率
   rate = life_table.get_mortality_rate(65, "female")
   ```

## 数据更新和维护

### 数据来源

- **中国数据**: 中国国家统计局、中国疾病预防控制中心
- **国际数据**: WHO、联合国人口司、Human Mortality Database
- **学术数据**: 发表的流行病学研究和生命表

### 更新频率

- **建议频率**: 每5年更新一次
- **触发条件**: 新的官方生命表发布
- **版本控制**: 保留历史版本用于比较分析

### 数据验证流程

1. **来源验证**: 确认数据来源的权威性
2. **格式检查**: 验证文件格式和结构
3. **数值验证**: 检查数据范围和合理性
4. **趋势分析**: 与历史数据比较，识别异常
5. **专家审核**: 流行病学专家审核数据质量

## 使用最佳实践

### 选择合适的生命表

1. **研究人群匹配**: 选择与研究人群特征最匹配的生命表
2. **时间一致性**: 使用与研究时期相近的生命表数据
3. **地理相关性**: 优先使用本地或相似地区的生命表

### 敏感性分析

建议使用多个生命表进行敏感性分析：

```python
# 比较不同生命表的影响
tables = ["china_2020", "who_global"]
results = {}

for table_name in tables:
    life_table.set_current_table(table_name)
    # 运行模拟...
    results[table_name] = simulation_results
```

### 不确定性处理

考虑死亡率的不确定性：

1. **置信区间**: 如果有可用数据，使用死亡率的置信区间
2. **敏感性分析**: 测试死亡率±10%的影响
3. **概率敏感性分析**: 使用死亡率的概率分布

## 技术支持

### 常见问题

**Q: 如何处理缺失的年龄数据？**
A: 系统会自动使用线性插值填补缺失值。

**Q: 可以使用其他国家的生命表吗？**
A: 可以，但需要考虑人群特征的差异性。

**Q: 如何验证生命表数据的质量？**
A: 使用 `validate_table_data()` 方法进行全面检查。

### 联系方式

如有技术问题或数据质量问题，请联系开发团队。
