"""
桌面应用主入口文件

提供PyQt6桌面应用的主要入口点和应用程序类。
"""

import sys
import logging
from pathlib import Path
from typing import Optional

from PyQt6.QtWidgets import QApplication, QMainWindow, QMessageBox
from PyQt6.QtCore import Qt, QTimer, pyqtSignal
from PyQt6.QtGui import QIcon, QPixmap

# 导入核心模块
from src.core import Population, SimulationState
from src.modules.population import PopulationGenerator


class Application(QApplication):
    """
    主应用程序类
    
    管理应用程序生命周期、全局设置和窗口管理。
    """
    
    def __init__(self, argv: list[str]):
        """初始化应用程序"""
        super().__init__(argv)
        
        # 设置应用程序基本信息
        self.setApplicationName("结直肠癌筛查模拟器")
        self.setApplicationVersion("1.0.0")
        self.setOrganizationName("深圳市南山区慢性病防治院")
        self.setOrganizationDomain("www.sznsmby.cn")
        
        # 设置应用程序属性（PyQt6中这些属性已经默认启用）
        # self.setAttribute(Qt.ApplicationAttribute.AA_EnableHighDpiScaling, True)
        # self.setAttribute(Qt.ApplicationAttribute.AA_UseHighDpiPixmaps, True)
        
        # 初始化日志
        self._setup_logging()
        
        # 创建主窗口
        self.main_window: Optional[MainWindow] = None
        
        # 设置应用程序图标
        self._setup_application_icon()
        
        # 设置样式
        self._setup_application_style()
        
        logging.info("应用程序初始化完成")
    
    def _setup_logging(self) -> None:
        """设置日志配置"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.StreamHandler(sys.stdout),
                logging.FileHandler('app.log', encoding='utf-8')
            ]
        )
    
    def _setup_application_icon(self) -> None:
        """设置应用程序图标"""
        from pathlib import Path

        # 查找图标文件
        project_root = Path(__file__).parent.parent.parent.parent
        icon_path = project_root / "resources" / "icons" / "app.png"

        icon = QIcon()
        if icon_path.exists():
            icon.addFile(str(icon_path))
            logging.info(f"已加载应用程序图标: {icon_path}")
        else:
            logging.warning(f"图标文件不存在: {icon_path}")

        self.setWindowIcon(icon)
    
    def _setup_application_style(self) -> None:
        """设置应用程序样式"""
        # 设置基本样式
        style = """
        QMainWindow {
            background-color: #f5f5f5;
        }
        
        QMenuBar {
            background-color: #ffffff;
            border-bottom: 1px solid #e0e0e0;
            padding: 4px;
        }
        
        QMenuBar::item {
            background-color: transparent;
            padding: 4px 8px;
            margin: 2px;
            border-radius: 4px;
        }
        
        QMenuBar::item:selected {
            background-color: #e3f2fd;
        }
        
        QStatusBar {
            background-color: #ffffff;
            border-top: 1px solid #e0e0e0;
        }
        
        QToolBar {
            background-color: #ffffff;
            border: 1px solid #e0e0e0;
            spacing: 4px;
            padding: 4px;
        }
        """
        self.setStyleSheet(style)
    
    def create_main_window(self) -> 'MainWindow':
        """创建并显示主窗口"""
        if self.main_window is None:
            self.main_window = MainWindow()
        
        self.main_window.show()
        return self.main_window
    
    def show_error_message(self, title: str, message: str) -> None:
        """显示错误消息对话框"""
        QMessageBox.critical(None, title, message)
    
    def show_info_message(self, title: str, message: str) -> None:
        """显示信息消息对话框"""
        QMessageBox.information(None, title, message)


class MainWindow(QMainWindow):
    """
    主窗口类
    
    提供应用程序的主要用户界面，包含菜单栏、工具栏、
    中央区域和状态栏。
    """
    
    # 信号定义
    simulation_started = pyqtSignal()
    simulation_paused = pyqtSignal()
    simulation_stopped = pyqtSignal()
    
    def __init__(self, parent=None):
        """初始化主窗口"""
        super().__init__(parent)
        
        # 窗口基本设置
        self.setWindowTitle("结直肠癌筛查模拟器")
        self.setMinimumSize(1200, 800)
        self.resize(1400, 900)
        
        # 居中显示窗口
        self._center_window()
        
        # 初始化组件
        self._setup_menu_bar()
        self._setup_tool_bar()
        self._setup_status_bar()
        self._setup_central_widget()
        
        # 初始化数据
        self.population: Optional[Population] = None
        self.simulation_state: Optional[SimulationState] = None
        
        logging.info("主窗口初始化完成")
    
    def _center_window(self) -> None:
        """将窗口居中显示"""
        screen = self.screen().availableGeometry()
        window = self.frameGeometry()
        window.moveCenter(screen.center())
        self.move(window.topLeft())

    def _setup_menu_bar(self) -> None:
        """设置菜单栏"""
        from PyQt6.QtGui import QAction, QKeySequence

        menubar = self.menuBar()

        # 文件菜单
        file_menu = menubar.addMenu("文件(&F)")

        # 新建项目
        new_action = QAction("新建项目(&N)", self)
        new_action.setShortcut(QKeySequence.StandardKey.New)
        new_action.setStatusTip("创建新的模拟项目")
        new_action.triggered.connect(self._new_project)
        file_menu.addAction(new_action)

        # 打开项目
        open_action = QAction("打开项目(&O)", self)
        open_action.setShortcut(QKeySequence.StandardKey.Open)
        open_action.setStatusTip("打开现有的模拟项目")
        open_action.triggered.connect(self._open_project)
        file_menu.addAction(open_action)

        file_menu.addSeparator()

        # 保存项目
        save_action = QAction("保存项目(&S)", self)
        save_action.setShortcut(QKeySequence.StandardKey.Save)
        save_action.setStatusTip("保存当前项目")
        save_action.triggered.connect(self._save_project)
        file_menu.addAction(save_action)

        # 另存为
        save_as_action = QAction("另存为(&A)", self)
        save_as_action.setShortcut(QKeySequence.StandardKey.SaveAs)
        save_as_action.setStatusTip("将项目保存到新位置")
        save_as_action.triggered.connect(self._save_project_as)
        file_menu.addAction(save_as_action)

        file_menu.addSeparator()

        # 退出
        exit_action = QAction("退出(&X)", self)
        exit_action.setShortcut(QKeySequence.StandardKey.Quit)
        exit_action.setStatusTip("退出应用程序")
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)

        # 编辑菜单
        edit_menu = menubar.addMenu("编辑(&E)")

        # 撤销
        undo_action = QAction("撤销(&U)", self)
        undo_action.setShortcut(QKeySequence.StandardKey.Undo)
        undo_action.setStatusTip("撤销上一个操作")
        edit_menu.addAction(undo_action)

        # 重做
        redo_action = QAction("重做(&R)", self)
        redo_action.setShortcut(QKeySequence.StandardKey.Redo)
        redo_action.setStatusTip("重做上一个操作")
        edit_menu.addAction(redo_action)

        # 视图菜单
        view_menu = menubar.addMenu("视图(&V)")

        # 全屏
        fullscreen_action = QAction("全屏(&F)", self)
        fullscreen_action.setShortcut(QKeySequence.StandardKey.FullScreen)
        fullscreen_action.setStatusTip("切换全屏模式")
        fullscreen_action.triggered.connect(self._toggle_fullscreen)
        view_menu.addAction(fullscreen_action)

        # 帮助菜单
        help_menu = menubar.addMenu("帮助(&H)")

        # 关于
        about_action = QAction("关于(&A)", self)
        about_action.setStatusTip("显示应用程序信息")
        about_action.triggered.connect(self._show_about)
        help_menu.addAction(about_action)

    def _setup_tool_bar(self) -> None:
        """设置工具栏"""
        from PyQt6.QtWidgets import QToolBar
        from PyQt6.QtGui import QAction
        from PyQt6.QtCore import QSize

        toolbar = QToolBar("主工具栏", self)
        toolbar.setIconSize(QSize(24, 24))
        self.addToolBar(toolbar)

        # 新建按钮
        new_action = QAction("新建", self)
        new_action.setStatusTip("创建新项目")
        new_action.triggered.connect(self._new_project)
        toolbar.addAction(new_action)

        # 打开按钮
        open_action = QAction("打开", self)
        open_action.setStatusTip("打开项目")
        open_action.triggered.connect(self._open_project)
        toolbar.addAction(open_action)

        # 保存按钮
        save_action = QAction("保存", self)
        save_action.setStatusTip("保存项目")
        save_action.triggered.connect(self._save_project)
        toolbar.addAction(save_action)

        toolbar.addSeparator()

        # 运行模拟按钮
        run_action = QAction("运行模拟", self)
        run_action.setStatusTip("开始运行模拟")
        run_action.triggered.connect(self._start_simulation)
        toolbar.addAction(run_action)

        # 暂停模拟按钮
        pause_action = QAction("暂停模拟", self)
        pause_action.setStatusTip("暂停模拟")
        pause_action.triggered.connect(self._pause_simulation)
        toolbar.addAction(pause_action)

        # 停止模拟按钮
        stop_action = QAction("停止模拟", self)
        stop_action.setStatusTip("停止模拟")
        stop_action.triggered.connect(self._stop_simulation)
        toolbar.addAction(stop_action)

    def _setup_status_bar(self) -> None:
        """设置状态栏"""
        from PyQt6.QtWidgets import QLabel, QProgressBar

        status_bar = self.statusBar()

        # 状态标签
        self.status_label = QLabel("就绪")
        status_bar.addWidget(self.status_label)

        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        status_bar.addPermanentWidget(self.progress_bar)

        # 显示初始状态
        status_bar.showMessage("应用程序已启动", 3000)

    def _setup_central_widget(self) -> None:
        """设置中央区域"""
        from PyQt6.QtWidgets import QTabWidget, QWidget, QVBoxLayout, QLabel
        from .windows.config_wizard import PopulationConfigWidget
        from .widgets.simulation_control import SimulationControlWidget

        # 创建标签页控件
        self.tab_widget = QTabWidget()
        self.setCentralWidget(self.tab_widget)

        # 配置标签页 - 集成实际的人群配置组件
        self.config_widget = PopulationConfigWidget()
        self.tab_widget.addTab(self.config_widget, "人群配置")

        # 模拟标签页 - 集成实际的模拟控制组件
        self.simulation_control = SimulationControlWidget()
        self.tab_widget.addTab(self.simulation_control, "模拟控制")

        # 结果标签页 - 创建结果显示容器
        results_container = QWidget()
        results_layout = QVBoxLayout(results_container)

        # 添加打开结果窗口的按钮
        from PyQt6.QtWidgets import QPushButton
        open_results_button = QPushButton("打开结果查看器")
        open_results_button.setStyleSheet("""
            QPushButton {
                background-color: #2196F3;
                color: white;
                border: none;
                padding: 12px 24px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
        """)
        open_results_button.clicked.connect(self._open_results_window)

        results_layout.addWidget(QLabel("模拟结果将在这里显示"))
        results_layout.addWidget(open_results_button)
        results_layout.addStretch()

        self.tab_widget.addTab(results_container, "结果显示")

        # 连接组件间的信号
        self._connect_component_signals()

        # 初始化结果窗口（延迟创建）
        self.results_window = None

    def _connect_component_signals(self) -> None:
        """连接组件间的信号"""
        # 连接配置变更信号
        self.config_widget.config_changed.connect(self._on_config_changed)
        self.config_widget.preview_requested.connect(self._on_preview_requested)

        # 连接模拟控制信号
        self.simulation_control.simulation_start_requested.connect(self._on_simulation_start)
        self.simulation_control.simulation_pause_requested.connect(self._on_simulation_pause)
        self.simulation_control.simulation_stop_requested.connect(self._on_simulation_stop)
        self.simulation_control.simulation_reset_requested.connect(self._on_simulation_reset)
        self.simulation_control.parameters_changed.connect(self._on_parameters_changed)

    def _open_results_window(self) -> None:
        """打开结果查看器窗口"""
        if self.results_window is None:
            from .windows.results_viewer import ResultsWindow
            self.results_window = ResultsWindow(self)

            # 连接结果窗口信号
            self.results_window.export_requested.connect(self._on_export_requested)
            self.results_window.refresh_requested.connect(self._on_refresh_requested)

        self.results_window.show()
        self.results_window.raise_()
        self.results_window.activateWindow()

        logging.info("打开结果查看器窗口")

    # 信号处理方法
    def _on_config_changed(self, config: dict) -> None:
        """处理配置变更"""
        logging.info(f"人群配置已更新: 人群规模={config.get('population_size', 0)}")
        self.status_label.setText("人群配置已更新")

    def _on_preview_requested(self) -> None:
        """处理预览请求"""
        logging.info("生成人群配置预览")
        self.status_label.setText("正在生成预览...")

    def _on_simulation_start(self, params: dict) -> None:
        """处理模拟开始"""
        logging.info(f"开始模拟: {params}")
        self.status_label.setText("模拟运行中...")
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)

        # 发送模拟开始信号
        self.simulation_started.emit()

    def _on_simulation_pause(self) -> None:
        """处理模拟暂停"""
        logging.info("模拟已暂停")
        self.status_label.setText("模拟已暂停")
        self.simulation_paused.emit()

    def _on_simulation_stop(self) -> None:
        """处理模拟停止"""
        logging.info("模拟已停止")
        self.status_label.setText("模拟已停止")
        self.progress_bar.setVisible(False)
        self.simulation_stopped.emit()

    def _on_simulation_reset(self) -> None:
        """处理模拟重置"""
        logging.info("模拟已重置")
        self.status_label.setText("模拟已重置")
        self.progress_bar.setVisible(False)
        self.progress_bar.setValue(0)

    def _on_parameters_changed(self, params: dict) -> None:
        """处理参数变更"""
        logging.info(f"模拟参数已更新: {params}")
        self.status_label.setText("模拟参数已更新")

    def _on_export_requested(self, format_type: str, file_path: str) -> None:
        """处理导出请求"""
        logging.info(f"导出请求: {format_type} -> {file_path}")
        self.status_label.setText(f"已导出{format_type.upper()}文件")

    def _on_refresh_requested(self) -> None:
        """处理刷新请求"""
        logging.info("刷新结果数据")
        self.status_label.setText("结果数据已刷新")

    # 菜单事件处理方法
    def _new_project(self) -> None:
        """新建项目"""
        logging.info("新建项目")
        self.status_label.setText("新建项目")
        # TODO: 实现新建项目逻辑

    def _open_project(self) -> None:
        """打开项目"""
        from PyQt6.QtWidgets import QFileDialog

        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "打开项目文件",
            "",
            "项目文件 (*.json);;所有文件 (*)"
        )

        if file_path:
            logging.info(f"打开项目: {file_path}")
            self.status_label.setText(f"已打开: {Path(file_path).name}")
            # TODO: 实现打开项目逻辑

    def _save_project(self) -> None:
        """保存项目"""
        logging.info("保存项目")
        self.status_label.setText("项目已保存")
        # TODO: 实现保存项目逻辑

    def _save_project_as(self) -> None:
        """另存为项目"""
        from PyQt6.QtWidgets import QFileDialog

        file_path, _ = QFileDialog.getSaveFileName(
            self,
            "另存为项目文件",
            "",
            "项目文件 (*.json);;所有文件 (*)"
        )

        if file_path:
            logging.info(f"另存为项目: {file_path}")
            self.status_label.setText(f"已保存: {Path(file_path).name}")
            # TODO: 实现另存为项目逻辑

    def _toggle_fullscreen(self) -> None:
        """切换全屏模式"""
        if self.isFullScreen():
            self.showNormal()
            logging.info("退出全屏模式")
        else:
            self.showFullScreen()
            logging.info("进入全屏模式")

    def _show_about(self) -> None:
        """显示关于对话框"""
        from PyQt6.QtWidgets import QMessageBox

        QMessageBox.about(
            self,
            "关于",
            "<h3>结直肠癌筛查模拟器</h3>"
            "<p>版本: 1.0.0</p>"
            "<p>用于评估不同筛查策略成本效益的微观模拟模型</p>"
            "<p>© 2025 Medical Simulation Lab</p>"
        )

    # 模拟控制方法
    def _start_simulation(self) -> None:
        """开始模拟"""
        logging.info("开始模拟")
        self.status_label.setText("模拟运行中...")
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)
        self.simulation_started.emit()
        # TODO: 实现模拟启动逻辑

    def _pause_simulation(self) -> None:
        """暂停模拟"""
        logging.info("暂停模拟")
        self.status_label.setText("模拟已暂停")
        self.simulation_paused.emit()
        # TODO: 实现模拟暂停逻辑

    def _stop_simulation(self) -> None:
        """停止模拟"""
        logging.info("停止模拟")
        self.status_label.setText("模拟已停止")
        self.progress_bar.setVisible(False)
        self.simulation_stopped.emit()
        # TODO: 实现模拟停止逻辑

    def update_progress(self, value: int) -> None:
        """更新进度条"""
        self.progress_bar.setValue(value)
        if value >= 100:
            self.progress_bar.setVisible(False)
            self.status_label.setText("模拟完成")

    def closeEvent(self, event) -> None:
        """窗口关闭事件"""
        from PyQt6.QtWidgets import QMessageBox

        reply = QMessageBox.question(
            self,
            "确认退出",
            "确定要退出应用程序吗？",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            logging.info("应用程序退出")
            event.accept()
        else:
            event.ignore()


def main() -> int:
    """主函数"""
    app = Application(sys.argv)

    try:
        # 创建并显示主窗口
        app.create_main_window()

        # 运行应用程序
        return app.exec()

    except Exception as e:
        logging.error(f"应用程序运行错误: {e}")
        app.show_error_message("错误", f"应用程序运行时发生错误:\n{e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
