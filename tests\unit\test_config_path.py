"""
配置文件路径管理测试

测试环境变量支持和配置文件路径解析功能。
"""

import os
import pytest
import yaml
from pathlib import Path
from tempfile import TemporaryDirectory

from src.modules.disease.risk_weights import (
    get_default_config_path,
    set_config_path,
    set_config_dir,
    clear_config_env,
    get_current_config_info,
    RiskFactorWeights
)


class TestConfigPathManagement:
    """测试配置路径管理功能"""
    
    def setup_method(self):
        """每个测试前清理环境变量"""
        clear_config_env()
    
    def teardown_method(self):
        """每个测试后清理环境变量"""
        clear_config_env()
    
    def test_default_config_path_exists(self):
        """测试默认配置文件路径存在"""
        # 清除环境变量，使用默认路径
        clear_config_env()
        
        try:
            config_path = get_default_config_path()
            assert config_path.exists()
            assert config_path.name == "default_weights.yaml"
        except FileNotFoundError:
            # 如果默认路径不存在，这是预期的行为
            pytest.skip("默认配置文件不存在，跳过测试")
    
    def test_environment_variable_config_file(self):
        """测试通过环境变量指定配置文件"""
        with TemporaryDirectory() as temp_dir:
            # 创建临时配置文件
            config_file = Path(temp_dir) / "test_weights.yaml"
            test_config = {
                "risk_factor_weights": {
                    "version": "test-1.0",
                    "weights": {
                        "family_history": {
                            "value": 2.0,
                            "category": "genetic"
                        }
                    }
                }
            }
            
            with open(config_file, 'w', encoding='utf-8') as f:
                yaml.dump(test_config, f)
            
            # 设置环境变量
            set_config_path(config_file)
            
            # 验证路径解析
            resolved_path = get_default_config_path()
            assert resolved_path == config_file.absolute()
            
            # 验证可以加载配置
            weights = RiskFactorWeights(resolved_path)
            assert weights.metadata["version"] == "test-1.0"
    
    def test_environment_variable_config_dir(self):
        """测试通过环境变量指定配置目录"""
        with TemporaryDirectory() as temp_dir:
            # 创建配置目录和文件
            config_dir = Path(temp_dir)
            config_file = config_dir / "default_weights.yaml"
            
            test_config = {
                "risk_factor_weights": {
                    "version": "test-dir-1.0",
                    "weights": {
                        "family_history": {
                            "value": 3.0,
                            "category": "genetic"
                        }
                    }
                }
            }
            
            with open(config_file, 'w', encoding='utf-8') as f:
                yaml.dump(test_config, f)
            
            # 设置环境变量
            set_config_dir(config_dir)
            
            # 验证路径解析
            resolved_path = get_default_config_path()
            assert resolved_path == config_file.absolute()
            
            # 验证可以加载配置
            weights = RiskFactorWeights(resolved_path)
            assert weights.metadata["version"] == "test-dir-1.0"
    
    def test_set_config_path_nonexistent_file(self):
        """测试设置不存在的配置文件路径"""
        nonexistent_file = Path("/nonexistent/path/config.yaml")
        
        with pytest.raises(FileNotFoundError, match="配置文件不存在"):
            set_config_path(nonexistent_file)
    
    def test_set_config_dir_nonexistent_dir(self):
        """测试设置不存在的配置目录"""
        nonexistent_dir = Path("/nonexistent/directory")
        
        with pytest.raises(FileNotFoundError, match="配置目录不存在"):
            set_config_dir(nonexistent_dir)
    
    def test_set_config_dir_missing_default_file(self):
        """测试配置目录中缺少默认文件"""
        with TemporaryDirectory() as temp_dir:
            config_dir = Path(temp_dir)
            
            with pytest.raises(FileNotFoundError, match="配置目录中找不到default_weights.yaml"):
                set_config_dir(config_dir)
    
    def test_clear_config_env(self):
        """测试清除配置环境变量"""
        with TemporaryDirectory() as temp_dir:
            config_file = Path(temp_dir) / "test.yaml"
            config_file.write_text("test: value")
            
            # 设置环境变量
            set_config_path(config_file)
            assert os.getenv("RISK_FACTOR_WEIGHTS_CONFIG") is not None
            
            # 清除环境变量
            clear_config_env()
            assert os.getenv("RISK_FACTOR_WEIGHTS_CONFIG") is None
            assert os.getenv("RISK_FACTOR_WEIGHTS_DIR") is None
    
    def test_get_current_config_info(self):
        """测试获取当前配置信息"""
        # 初始状态
        info = get_current_config_info()
        assert "config_file_env" in info
        assert "config_dir_env" in info
        assert "environment_variables" in info
        assert info["config_file_env"] is None
        assert info["config_dir_env"] is None
        
        # 设置环境变量后
        with TemporaryDirectory() as temp_dir:
            config_file = Path(temp_dir) / "test.yaml"
            config_file.write_text("test: value")
            
            set_config_path(config_file)
            info = get_current_config_info()
            
            assert info["config_file_env"] == str(config_file.absolute())
            assert info["environment_variables"]["RISK_FACTOR_WEIGHTS_CONFIG"] == str(config_file.absolute())
    
    def test_config_priority_order(self):
        """测试配置文件优先级顺序"""
        with TemporaryDirectory() as temp_dir:
            # 创建两个配置文件
            config_file1 = Path(temp_dir) / "priority1.yaml"
            config_file2 = Path(temp_dir) / "priority2.yaml"
            config_dir = Path(temp_dir) / "config_dir"
            config_dir.mkdir()
            config_file3 = config_dir / "default_weights.yaml"
            
            for config_file in [config_file1, config_file2, config_file3]:
                test_config = {
                    "risk_factor_weights": {
                        "version": f"test-{config_file.stem}",
                        "weights": {}
                    }
                }
                with open(config_file, 'w', encoding='utf-8') as f:
                    yaml.dump(test_config, f)
            
            # 测试优先级：直接文件路径 > 目录路径
            set_config_dir(config_dir)
            set_config_path(config_file1)  # 这个应该有更高优先级
            
            resolved_path = get_default_config_path()
            assert resolved_path == config_file1.absolute()
    
    def test_weights_with_custom_config(self):
        """测试使用自定义配置创建权重管理器"""
        with TemporaryDirectory() as temp_dir:
            config_file = Path(temp_dir) / "custom_weights.yaml"
            
            custom_config = {
                "risk_factor_weights": {
                    "version": "custom-1.0",
                    "source": "Custom Test Configuration",
                    "weights": {
                        "family_history": {
                            "value": 5.0,
                            "confidence_interval": [4.0, 6.0],
                            "category": "genetic",
                            "evidence_level": "high"
                        },
                        "body_mass_index": {
                            "baseline": 25.0,
                            "per_unit_increase": 0.1,
                            "max_effect": 2.0,
                            "calculation_method": "continuous",
                            "category": "lifestyle"
                        }
                    }
                },
                "risk_stratification": {
                    "low_risk": {"threshold": 1.0},
                    "moderate_risk": {"threshold_min": 1.0, "threshold_max": 2.0},
                    "high_risk": {"threshold": 2.0}
                }
            }
            
            with open(config_file, 'w', encoding='utf-8') as f:
                yaml.dump(custom_config, f)
            
            # 使用自定义配置
            set_config_path(config_file)
            weights = RiskFactorWeights()  # 不传递config_file，使用环境变量
            
            assert weights.metadata["version"] == "custom-1.0"
            assert weights.metadata["source"] == "Custom Test Configuration"
            
            # 验证权重值
            from src.modules.disease.risk_factors import RiskFactorType
            family_weight = weights.get_weight_value(RiskFactorType.FAMILY_HISTORY)
            assert family_weight == 5.0
    
    def test_config_file_not_found_error_message(self):
        """测试配置文件未找到时的错误信息"""
        clear_config_env()
        
        # 设置一个不存在的配置文件
        os.environ["RISK_FACTOR_WEIGHTS_CONFIG"] = "/nonexistent/config.yaml"
        
        try:
            get_default_config_path()
            pytest.fail("应该抛出FileNotFoundError")
        except FileNotFoundError as e:
            error_msg = str(e)
            assert "无法找到风险因素权重配置文件" in error_msg
            assert "环境变量 RISK_FACTOR_WEIGHTS_CONFIG" in error_msg
            assert "环境变量 RISK_FACTOR_WEIGHTS_DIR" in error_msg
    
    def test_reload_config_with_new_path(self):
        """测试重新加载配置时使用新路径"""
        with TemporaryDirectory() as temp_dir:
            # 创建第一个配置文件
            config_file1 = Path(temp_dir) / "config1.yaml"
            config1 = {
                "risk_factor_weights": {
                    "version": "config1",
                    "weights": {
                        "family_history": {"value": 2.0, "category": "genetic"}
                    }
                }
            }
            with open(config_file1, 'w', encoding='utf-8') as f:
                yaml.dump(config1, f)
            
            # 创建第二个配置文件
            config_file2 = Path(temp_dir) / "config2.yaml"
            config2 = {
                "risk_factor_weights": {
                    "version": "config2",
                    "weights": {
                        "family_history": {"value": 3.0, "category": "genetic"}
                    }
                }
            }
            with open(config_file2, 'w', encoding='utf-8') as f:
                yaml.dump(config2, f)
            
            # 使用第一个配置
            set_config_path(config_file1)
            weights = RiskFactorWeights()
            assert weights.metadata["version"] == "config1"
            
            # 切换到第二个配置并重新加载
            set_config_path(config_file2)
            weights.config_file = None  # 重置配置文件路径
            weights.reload_config()
            assert weights.metadata["version"] == "config2"
