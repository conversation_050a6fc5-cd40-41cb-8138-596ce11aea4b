#!/usr/bin/env python3
"""
简单的Excel文件读取测试

测试pandas读取Excel文件的功能
"""

import pandas as pd
import tempfile
from pathlib import Path


def test_excel_creation_and_reading():
    """测试Excel文件创建和读取"""
    
    print("测试Excel文件创建和读取功能")
    print("="*50)
    
    # 创建测试数据
    data = {
        '年龄': [45, 50, 55, 60],
        '性别': ['男', '女', '男', '女'],
        '人数': [100, 120, 140, 110]
    }
    
    df = pd.DataFrame(data)
    print("✓ 创建测试数据")
    print(df)
    
    # 创建临时Excel文件
    with tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False) as f:
        excel_file = f.name
    
    try:
        # 保存为Excel
        df.to_excel(excel_file, index=False)
        print(f"✓ 保存Excel文件: {excel_file}")
        
        # 读取Excel文件
        df_read = pd.read_excel(excel_file)
        print("✓ 读取Excel文件")
        print(df_read)
        
        # 验证数据一致性
        if df.equals(df_read):
            print("✓ 数据一致性验证通过")
        else:
            print("✗ 数据不一致")
            
        # 测试列名清理
        df_read.columns = df_read.columns.str.strip()
        print("✓ 列名清理完成")
        
        # 测试列名映射
        age_columns = ['age', '年龄', 'Age', 'AGE']
        gender_columns = ['gender', '性别', 'Gender', 'GENDER', 'sex']
        count_columns = ['count', 'number', '人数', '数量', 'Count', 'Number']
        
        column_mapping = {}
        
        # 查找年龄列
        for col in age_columns:
            if col in df_read.columns:
                column_mapping['age'] = col
                break
        
        # 查找性别列
        for col in gender_columns:
            if col in df_read.columns:
                column_mapping['gender'] = col
                break
                
        # 查找人数列
        for col in count_columns:
            if col in df_read.columns:
                column_mapping['count'] = col
                break
        
        print(f"✓ 列映射: {column_mapping}")
        
        # 测试性别标准化
        gender_mapping = {
            'M': 'MALE', 'F': 'FEMALE',
            'MALE': 'MALE', 'FEMALE': 'FEMALE',
            'Male': 'MALE', 'Female': 'FEMALE',
            'male': 'MALE', 'female': 'FEMALE',
            '男': 'MALE', '女': 'FEMALE',
            '男性': 'MALE', '女性': 'FEMALE',
        }
        
        if 'gender' in column_mapping:
            gender_col = column_mapping['gender']
            standardized_genders = []
            for value in df_read[gender_col]:
                value_str = str(value).strip()
                if value_str in gender_mapping:
                    standardized_genders.append(gender_mapping[value_str])
                else:
                    print(f"✗ 无效性别值: {value_str}")
                    
            print(f"✓ 性别标准化: {standardized_genders}")
        
        return True
        
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        # 清理临时文件
        try:
            Path(excel_file).unlink(missing_ok=True)
            print("✓ 清理临时文件")
        except Exception as e:
            print(f"⚠ 清理失败: {e}")


def test_different_excel_formats():
    """测试不同的Excel格式"""
    
    print("\n测试不同Excel格式")
    print("="*50)
    
    # 测试.xlsx格式
    data = {'age': [25, 30], 'gender': ['M', 'F']}
    df = pd.DataFrame(data)
    
    # 测试.xlsx
    with tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False) as f:
        xlsx_file = f.name
    
    try:
        df.to_excel(xlsx_file, index=False)
        df_read = pd.read_excel(xlsx_file)
        print("✓ .xlsx格式测试通过")
        
    except Exception as e:
        print(f"✗ .xlsx格式测试失败: {e}")
        
    finally:
        Path(xlsx_file).unlink(missing_ok=True)
    
    # 测试.xls格式（如果支持）
    try:
        with tempfile.NamedTemporaryFile(suffix='.xls', delete=False) as f:
            xls_file = f.name
        
        df.to_excel(xls_file, index=False)
        df_read = pd.read_excel(xls_file)
        print("✓ .xls格式测试通过")
        
    except Exception as e:
        print(f"⚠ .xls格式测试跳过: {e}")
        
    finally:
        try:
            Path(xls_file).unlink(missing_ok=True)
        except:
            pass


if __name__ == "__main__":
    print("Excel功能基础测试")
    print("="*50)
    
    success1 = test_excel_creation_and_reading()
    test_different_excel_formats()
    
    if success1:
        print("\n🎉 基础测试通过!")
    else:
        print("\n❌ 基础测试失败!")
