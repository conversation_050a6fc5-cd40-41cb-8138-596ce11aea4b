#!/usr/bin/env python3
"""
测试Excel文件导入功能

创建示例Excel文件并测试PopulationGenerator的Excel导入功能
"""

import sys
import tempfile
import pandas as pd
from pathlib import Path

# 添加src路径
sys.path.insert(0, "src")

try:
    # 直接导入，避免相对导入问题
    import importlib.util

    # 导入PopulationGenerator
    spec = importlib.util.spec_from_file_location(
        "population_generator",
        "src/modules/population/population_generator.py"
    )
    pop_gen_module = importlib.util.module_from_spec(spec)

    # 先导入依赖模块
    core_spec = importlib.util.spec_from_file_location("core", "src/core/__init__.py")
    core_module = importlib.util.module_from_spec(core_spec)
    sys.modules['core'] = core_module
    core_spec.loader.exec_module(core_module)

    utils_spec = importlib.util.spec_from_file_location("utils", "src/utils/__init__.py")
    utils_module = importlib.util.module_from_spec(utils_spec)
    sys.modules['utils'] = utils_module
    utils_spec.loader.exec_module(utils_module)

    # 执行模块
    spec.loader.exec_module(pop_gen_module)

    PopulationGenerator = pop_gen_module.PopulationGenerator
    Gender = core_module.Gender

    print("✓ 成功导入模块")
except ImportError as e:
    print(f"✗ 导入失败: {e}")
    sys.exit(1)
except Exception as e:
    print(f"✗ 模块加载失败: {e}")
    sys.exit(1)


def create_sample_excel_files():
    """创建示例Excel文件"""
    
    # 创建聚合格式Excel文件
    aggregated_data = {
        '年龄': [45, 45, 50, 50, 55, 55, 60, 60],
        '性别': ['男', '女', '男', '女', '男', '女', '男', '女'],
        '人数': [100, 95, 120, 115, 140, 135, 110, 105]
    }
    
    aggregated_df = pd.DataFrame(aggregated_data)
    
    # 创建个体格式Excel文件
    individual_data = {
        'age': [45, 50, 55, 60, 65, 70],
        'gender': ['M', 'F', 'M', 'F', 'M', 'F']
    }
    
    individual_df = pd.DataFrame(individual_data)
    
    # 保存为Excel文件
    with tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False) as f:
        aggregated_file = f.name
    
    with tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False) as f:
        individual_file = f.name
    
    try:
        aggregated_df.to_excel(aggregated_file, index=False)
        individual_df.to_excel(individual_file, index=False)
        print(f"✓ 创建聚合格式Excel文件: {aggregated_file}")
        print(f"✓ 创建个体格式Excel文件: {individual_file}")
        
        return aggregated_file, individual_file
        
    except Exception as e:
        print(f"✗ 创建Excel文件失败: {e}")
        return None, None


def test_excel_import():
    """测试Excel导入功能"""
    
    print("\n" + "="*60)
    print("测试Excel文件导入功能")
    print("="*60)
    
    # 创建示例文件
    aggregated_file, individual_file = create_sample_excel_files()
    
    if not aggregated_file or not individual_file:
        print("✗ 无法创建测试文件")
        return False
    
    try:
        # 创建生成器
        generator = PopulationGenerator(random_seed=42)
        print("✓ 创建PopulationGenerator")
        
        # 测试聚合格式Excel文件
        print("\n--- 测试聚合格式Excel文件 ---")
        population1 = generator.generate_population_from_file(
            aggregated_file,
            show_progress=False
        )
        
        print(f"✓ 成功导入聚合格式Excel文件")
        print(f"  生成人群规模: {len(population1.individuals)}")
        
        # 验证人群规模
        expected_size = pd.read_excel(aggregated_file)['人数'].sum()
        if len(population1.individuals) == expected_size:
            print(f"✓ 人群规模正确: {expected_size}")
        else:
            print(f"✗ 人群规模错误: 期望{expected_size}, 实际{len(population1.individuals)}")
        
        # 测试个体格式Excel文件
        print("\n--- 测试个体格式Excel文件 ---")
        population2 = generator.generate_population_from_file(
            individual_file,
            show_progress=False
        )
        
        print(f"✓ 成功导入个体格式Excel文件")
        print(f"  生成人群规模: {len(population2.individuals)}")
        
        # 验证人群规模
        expected_size2 = len(pd.read_excel(individual_file))
        if len(population2.individuals) == expected_size2:
            print(f"✓ 人群规模正确: {expected_size2}")
        else:
            print(f"✗ 人群规模错误: 期望{expected_size2}, 实际{len(population2.individuals)}")
        
        # 测试性别分布
        print("\n--- 验证性别分布 ---")
        stats1 = population1.statistics
        gender_dist1 = stats1.get_gender_distribution()
        print(f"聚合格式文件性别分布: {gender_dist1}")
        
        stats2 = population2.statistics
        gender_dist2 = stats2.get_gender_distribution()
        print(f"个体格式文件性别分布: {gender_dist2}")
        
        # 测试年龄分布
        print("\n--- 验证年龄分布 ---")
        ages1 = [ind.get_current_age() for ind in population1.individuals]
        ages2 = [ind.get_current_age() for ind in population2.individuals]
        
        print(f"聚合格式文件年龄范围: {min(ages1):.0f}-{max(ages1):.0f}岁")
        print(f"个体格式文件年龄范围: {min(ages2):.0f}-{max(ages2):.0f}岁")
        
        # 获取生成摘要
        summary = generator.get_last_generation_summary()
        if summary:
            print(f"\n--- 生成摘要 ---")
            print(f"总个体数: {summary.total_individuals}")
            print(f"生成时间: {summary.generation_time:.3f}秒")
            print(f"年龄统计: 均值={summary.age_stats['mean']:.1f}, "
                  f"标准差={summary.age_stats['std']:.1f}")
        
        print("\n✓ Excel导入功能测试完成")
        return True
        
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        # 清理临时文件
        try:
            Path(aggregated_file).unlink(missing_ok=True)
            Path(individual_file).unlink(missing_ok=True)
            print("✓ 清理临时文件")
        except Exception as e:
            print(f"⚠ 清理临时文件失败: {e}")


def test_error_handling():
    """测试错误处理"""
    
    print("\n" + "="*60)
    print("测试错误处理")
    print("="*60)
    
    generator = PopulationGenerator()
    
    # 测试不存在的文件
    try:
        generator.generate_population_from_file("nonexistent.xlsx")
        print("✗ 应该抛出FileNotFoundError")
    except FileNotFoundError:
        print("✓ 正确处理不存在的文件")
    except Exception as e:
        print(f"✗ 错误的异常类型: {e}")
    
    # 测试不支持的文件格式
    with tempfile.NamedTemporaryFile(suffix='.txt', delete=False) as f:
        temp_file = f.name
    
    try:
        generator.generate_population_from_file(temp_file)
        print("✗ 应该抛出ValidationError")
    except Exception as e:
        if "不支持的文件格式" in str(e):
            print("✓ 正确处理不支持的文件格式")
        else:
            print(f"✗ 错误信息不正确: {e}")
    finally:
        Path(temp_file).unlink(missing_ok=True)


if __name__ == "__main__":
    print("Excel文件导入功能测试")
    print("="*60)
    
    success = test_excel_import()
    test_error_handling()
    
    if success:
        print("\n🎉 所有测试通过!")
    else:
        print("\n❌ 测试失败!")
        sys.exit(1)
