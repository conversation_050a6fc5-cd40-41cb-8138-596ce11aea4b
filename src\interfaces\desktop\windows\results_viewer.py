"""
结果显示窗口

提供模拟结果的可视化显示，包括统计表格、图表展示和数据导出功能。
"""

import logging
from pathlib import Path
from typing import Dict, Any, List, Optional
from datetime import datetime

from PyQt6.QtWidgets import (
    QDialog, QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
    QGroupBox, QLabel, QPushButton, QTableWidget, QTableWidgetItem,
    QTabWidget, QTextEdit, QSplitter, QHeaderView, QComboBox,
    QCheckBox, QProgressBar, QFileDialog, QMessageBox
)
from PyQt6.QtCore import Qt, pyqtSignal, QThread, pyqtSlot
from PyQt6.QtGui import QFont, QPixmap

# 尝试导入matplotlib，如果失败则使用占位符
try:
    import matplotlib.pyplot as plt
    from matplotlib.figure import Figure

    # 尝试导入PyQt6后端，如果失败则使用PyQt5后端
    try:
        from matplotlib.backends.backend_qtagg import FigureCanvasQTAgg as FigureCanvas
        BACKEND_TYPE = "PyQt6"
    except ImportError:
        try:
            from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
            BACKEND_TYPE = "PyQt5"
            logging.warning("使用PyQt5 matplotlib后端，建议升级到PyQt6兼容版本")
        except ImportError:
            raise ImportError("无法导入matplotlib Qt后端")

    MATPLOTLIB_AVAILABLE = True
    logging.info(f"Matplotlib已加载，使用{BACKEND_TYPE}后端")

except ImportError as e:
    MATPLOTLIB_AVAILABLE = False
    BACKEND_TYPE = None
    logging.warning(f"Matplotlib未安装或配置错误，图表功能将不可用: {e}")

from src.core import Population, PopulationStatistics


class ResultsWindow(QDialog):
    """
    结果显示窗口
    
    提供模拟结果的完整显示界面，包括统计信息、图表和导出功能。
    """
    
    # 信号定义
    export_requested = pyqtSignal(str, str)  # 导出请求信号 (format, file_path)
    refresh_requested = pyqtSignal()         # 刷新请求信号
    
    def __init__(self, parent=None):
        """初始化结果显示窗口"""
        super().__init__(parent)
        
        self.setWindowTitle("模拟结果查看器")
        self.setModal(False)
        self.resize(1000, 700)
        
        # 数据存储
        self.population: Optional[Population] = None
        self.statistics: Optional[PopulationStatistics] = None
        self.simulation_results: Dict[str, Any] = {}
        
        self._setup_ui()
        self._connect_signals()
        
        logging.info("结果显示窗口初始化完成")
    
    def _setup_ui(self) -> None:
        """设置用户界面"""
        layout = QVBoxLayout(self)
        
        # 工具栏
        toolbar = self._create_toolbar()
        layout.addWidget(toolbar)
        
        # 主要内容区域
        main_content = self._create_main_content()
        layout.addWidget(main_content)
        
        # 状态栏
        status_bar = self._create_status_bar()
        layout.addWidget(status_bar)
    
    def _create_toolbar(self) -> QWidget:
        """创建工具栏"""
        toolbar = QWidget()
        layout = QHBoxLayout(toolbar)
        
        # 刷新按钮
        self.refresh_button = QPushButton("刷新数据")
        self.refresh_button.setStyleSheet("""
            QPushButton {
                background-color: #2196F3;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
        """)
        layout.addWidget(self.refresh_button)
        
        # 导出按钮
        self.export_csv_button = QPushButton("导出CSV")
        self.export_csv_button.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """)
        layout.addWidget(self.export_csv_button)
        
        self.export_pdf_button = QPushButton("导出PDF")
        self.export_pdf_button.setStyleSheet("""
            QPushButton {
                background-color: #FF5722;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #E64A19;
            }
        """)
        layout.addWidget(self.export_pdf_button)
        
        layout.addStretch()
        
        # 显示选项
        self.show_charts_checkbox = QCheckBox("显示图表")
        self.show_charts_checkbox.setChecked(True)
        layout.addWidget(self.show_charts_checkbox)
        
        return toolbar
    
    def _create_main_content(self) -> QWidget:
        """创建主要内容区域"""
        # 使用标签页组织内容
        self.tab_widget = QTabWidget()
        
        # 统计信息标签页
        stats_tab = self._create_statistics_tab()
        self.tab_widget.addTab(stats_tab, "统计信息")
        
        # 图表标签页
        if MATPLOTLIB_AVAILABLE:
            charts_tab = self._create_charts_tab()
            self.tab_widget.addTab(charts_tab, "图表分析")
        
        # 详细数据标签页
        data_tab = self._create_data_tab()
        self.tab_widget.addTab(data_tab, "详细数据")
        
        # 日志标签页
        log_tab = self._create_log_tab()
        self.tab_widget.addTab(log_tab, "运行日志")
        
        return self.tab_widget
    
    def _create_statistics_tab(self) -> QWidget:
        """创建统计信息标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 基本统计信息
        basic_stats_group = QGroupBox("基本统计信息")
        basic_layout = QGridLayout(basic_stats_group)
        
        # 统计标签
        self.total_population_label = QLabel("--")
        self.male_count_label = QLabel("--")
        self.female_count_label = QLabel("--")
        self.avg_age_label = QLabel("--")
        self.age_range_label = QLabel("--")
        
        basic_layout.addWidget(QLabel("总人口数:"), 0, 0)
        basic_layout.addWidget(self.total_population_label, 0, 1)
        basic_layout.addWidget(QLabel("男性人数:"), 1, 0)
        basic_layout.addWidget(self.male_count_label, 1, 1)
        basic_layout.addWidget(QLabel("女性人数:"), 2, 0)
        basic_layout.addWidget(self.female_count_label, 2, 1)
        basic_layout.addWidget(QLabel("平均年龄:"), 3, 0)
        basic_layout.addWidget(self.avg_age_label, 3, 1)
        basic_layout.addWidget(QLabel("年龄范围:"), 4, 0)
        basic_layout.addWidget(self.age_range_label, 4, 1)
        
        layout.addWidget(basic_stats_group)
        
        # 疾病状态统计
        disease_stats_group = QGroupBox("疾病状态统计")
        disease_layout = QGridLayout(disease_stats_group)
        
        self.normal_count_label = QLabel("--")
        self.adenoma_count_label = QLabel("--")
        self.cancer_count_label = QLabel("--")
        
        disease_layout.addWidget(QLabel("正常状态:"), 0, 0)
        disease_layout.addWidget(self.normal_count_label, 0, 1)
        disease_layout.addWidget(QLabel("腺瘤状态:"), 1, 0)
        disease_layout.addWidget(self.adenoma_count_label, 1, 1)
        disease_layout.addWidget(QLabel("癌症状态:"), 2, 0)
        disease_layout.addWidget(self.cancer_count_label, 2, 1)
        
        layout.addWidget(disease_stats_group)
        
        layout.addStretch()
        return widget
    
    def _create_charts_tab(self) -> QWidget:
        """创建图表标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        if not MATPLOTLIB_AVAILABLE:
            no_charts_label = QLabel("图表功能不可用：需要安装matplotlib")
            no_charts_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            layout.addWidget(no_charts_label)
            return widget
        
        # 图表控制面板
        chart_controls = QWidget()
        controls_layout = QHBoxLayout(chart_controls)
        
        controls_layout.addWidget(QLabel("图表类型:"))
        self.chart_type_combo = QComboBox()
        self.chart_type_combo.addItems([
            "年龄分布直方图",
            "性别分布饼图", 
            "疾病状态分布",
            "生存曲线"
        ])
        controls_layout.addWidget(self.chart_type_combo)
        
        self.update_chart_button = QPushButton("更新图表")
        controls_layout.addWidget(self.update_chart_button)
        
        controls_layout.addStretch()
        layout.addWidget(chart_controls)
        
        # 图表显示区域
        self.figure = Figure(figsize=(10, 6))
        self.canvas = FigureCanvas(self.figure)
        layout.addWidget(self.canvas)
        
        return widget
    
    def _create_data_tab(self) -> QWidget:
        """创建详细数据标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 数据表格
        self.data_table = QTableWidget()
        self.data_table.setAlternatingRowColors(True)
        self.data_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        
        # 设置表头
        headers = ["ID", "年龄", "性别", "疾病状态", "通路类型", "创建时间"]
        self.data_table.setColumnCount(len(headers))
        self.data_table.setHorizontalHeaderLabels(headers)
        
        # 自动调整列宽
        header = self.data_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.ResizeMode.ResizeToContents)
        
        layout.addWidget(self.data_table)
        
        return widget
    
    def _create_log_tab(self) -> QWidget:
        """创建日志标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 日志显示区域
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        
        # 设置等宽字体
        font = QFont("Consolas", 9)
        font.setStyleHint(QFont.StyleHint.Monospace)
        self.log_text.setFont(font)
        
        layout.addWidget(self.log_text)
        
        return widget
    
    def _create_status_bar(self) -> QWidget:
        """创建状态栏"""
        status_bar = QWidget()
        layout = QHBoxLayout(status_bar)
        
        self.status_label = QLabel("就绪")
        layout.addWidget(self.status_label)
        
        layout.addStretch()
        
        self.last_update_label = QLabel("最后更新: --")
        layout.addWidget(self.last_update_label)
        
        return status_bar

    def _connect_signals(self) -> None:
        """连接信号和槽"""
        # 工具栏按钮
        self.refresh_button.clicked.connect(self._refresh_data)
        self.export_csv_button.clicked.connect(self._export_csv)
        self.export_pdf_button.clicked.connect(self._export_pdf)

        # 图表控制
        if MATPLOTLIB_AVAILABLE:
            self.chart_type_combo.currentTextChanged.connect(self._update_chart)
            self.update_chart_button.clicked.connect(self._update_chart)

        # 显示选项
        self.show_charts_checkbox.toggled.connect(self._toggle_charts)

    def _refresh_data(self) -> None:
        """刷新数据"""
        logging.info("刷新结果数据")
        self.status_label.setText("正在刷新数据...")

        # 更新统计信息
        self._update_statistics()

        # 更新数据表格
        self._update_data_table()

        # 更新图表
        if MATPLOTLIB_AVAILABLE:
            self._update_chart()

        # 更新状态
        self.last_update_label.setText(f"最后更新: {datetime.now().strftime('%H:%M:%S')}")
        self.status_label.setText("数据刷新完成")

        self.refresh_requested.emit()

    def _update_statistics(self) -> None:
        """更新统计信息"""
        if not self.population or not self.statistics:
            return

        try:
            # 基本统计
            total_count = self.statistics.get_total_count()
            gender_dist = self.statistics.get_gender_distribution()
            age_stats = self.statistics.get_age_statistics()
            disease_dist = self.statistics.get_disease_state_distribution()

            # 更新标签
            self.total_population_label.setText(f"{total_count:,}")
            self.male_count_label.setText(f"{gender_dist.get('male', 0):,}")
            self.female_count_label.setText(f"{gender_dist.get('female', 0):,}")
            self.avg_age_label.setText(f"{age_stats.get('mean', 0):.1f} 岁")
            self.age_range_label.setText(f"{age_stats.get('min', 0):.0f} - {age_stats.get('max', 0):.0f} 岁")

            # 疾病状态统计
            self.normal_count_label.setText(f"{disease_dist.get('NORMAL', 0):,}")
            self.adenoma_count_label.setText(f"{disease_dist.get('LOW_RISK_ADENOMA', 0) + disease_dist.get('HIGH_RISK_ADENOMA', 0):,}")
            self.cancer_count_label.setText(f"{disease_dist.get('CLINICAL_CANCER', 0):,}")

        except Exception as e:
            logging.error(f"更新统计信息失败: {e}")
            self.status_label.setText(f"更新统计信息失败: {e}")

    def _update_data_table(self) -> None:
        """更新数据表格"""
        if not self.population:
            return

        try:
            individuals = list(self.population.individuals.values())
            self.data_table.setRowCount(len(individuals))

            for row, individual in enumerate(individuals):
                # ID
                self.data_table.setItem(row, 0, QTableWidgetItem(individual.individual_id[:8]))

                # 年龄
                age = individual.get_current_age(2025)  # 使用当前年份
                self.data_table.setItem(row, 1, QTableWidgetItem(f"{age:.0f}"))

                # 性别
                gender_text = "男" if individual.gender.name == "MALE" else "女"
                self.data_table.setItem(row, 2, QTableWidgetItem(gender_text))

                # 疾病状态
                disease_state = individual.current_disease_state.name
                self.data_table.setItem(row, 3, QTableWidgetItem(disease_state))

                # 通路类型
                pathway = individual.pathway_type.name if individual.pathway_type else "未知"
                self.data_table.setItem(row, 4, QTableWidgetItem(pathway))

                # 创建时间（模拟）
                self.data_table.setItem(row, 5, QTableWidgetItem(str(individual.birth_year)))

        except Exception as e:
            logging.error(f"更新数据表格失败: {e}")
            self.status_label.setText(f"更新数据表格失败: {e}")

    def _update_chart(self) -> None:
        """更新图表"""
        if not MATPLOTLIB_AVAILABLE or not self.population:
            return

        try:
            self.figure.clear()
            chart_type = self.chart_type_combo.currentText()

            if chart_type == "年龄分布直方图":
                self._create_age_histogram()
            elif chart_type == "性别分布饼图":
                self._create_gender_pie_chart()
            elif chart_type == "疾病状态分布":
                self._create_disease_state_chart()
            elif chart_type == "生存曲线":
                self._create_survival_curve()

            self.canvas.draw()

        except Exception as e:
            logging.error(f"更新图表失败: {e}")
            self.status_label.setText(f"更新图表失败: {e}")

    def _create_age_histogram(self) -> None:
        """创建年龄分布直方图"""
        if not self.statistics:
            return

        ax = self.figure.add_subplot(111)

        # 获取年龄数据
        ages = []
        for individual in self.population.individuals.values():
            age = individual.get_current_age(2025)
            ages.append(age)

        # 创建直方图
        ax.hist(ages, bins=20, alpha=0.7, color='skyblue', edgecolor='black')
        ax.set_xlabel('年龄')
        ax.set_ylabel('人数')
        ax.set_title('年龄分布直方图')
        ax.grid(True, alpha=0.3)

    def _create_gender_pie_chart(self) -> None:
        """创建性别分布饼图"""
        if not self.statistics:
            return

        ax = self.figure.add_subplot(111)

        gender_dist = self.statistics.get_gender_distribution()
        labels = ['男性', '女性']
        sizes = [gender_dist.get('male', 0), gender_dist.get('female', 0)]
        colors = ['lightblue', 'lightpink']

        ax.pie(sizes, labels=labels, colors=colors, autopct='%1.1f%%', startangle=90)
        ax.set_title('性别分布')

    def _create_disease_state_chart(self) -> None:
        """创建疾病状态分布图"""
        if not self.statistics:
            return

        ax = self.figure.add_subplot(111)

        disease_dist = self.statistics.get_disease_state_distribution()
        states = list(disease_dist.keys())
        counts = list(disease_dist.values())

        bars = ax.bar(states, counts, color=['green', 'yellow', 'orange', 'red'])
        ax.set_xlabel('疾病状态')
        ax.set_ylabel('人数')
        ax.set_title('疾病状态分布')
        ax.tick_params(axis='x', rotation=45)

        # 添加数值标签
        for bar in bars:
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2., height,
                   f'{int(height)}', ha='center', va='bottom')

    def _create_survival_curve(self) -> None:
        """创建生存曲线（占位符）"""
        ax = self.figure.add_subplot(111)

        # 这里应该实现真正的生存分析
        # 目前只是一个占位符
        import numpy as np

        x = np.linspace(0, 100, 100)
        y = np.exp(-x/50)  # 简单的指数衰减

        ax.plot(x, y, 'b-', linewidth=2)
        ax.set_xlabel('年龄')
        ax.set_ylabel('生存概率')
        ax.set_title('生存曲线（示例）')
        ax.grid(True, alpha=0.3)

    def _toggle_charts(self, enabled: bool) -> None:
        """切换图表显示"""
        if MATPLOTLIB_AVAILABLE:
            chart_tab_index = 1  # 图表标签页的索引
            self.tab_widget.setTabEnabled(chart_tab_index, enabled)

    def _export_csv(self) -> None:
        """导出CSV文件"""
        if not self.population:
            QMessageBox.warning(self, "警告", "没有可导出的数据")
            return

        file_path, _ = QFileDialog.getSaveFileName(
            self,
            "导出CSV文件",
            f"simulation_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
            "CSV文件 (*.csv);;所有文件 (*)"
        )

        if file_path:
            try:
                self._save_csv_data(file_path)
                QMessageBox.information(self, "成功", f"数据已导出到: {file_path}")
                self.export_requested.emit("csv", file_path)
            except Exception as e:
                QMessageBox.critical(self, "错误", f"导出CSV失败: {e}")
                logging.error(f"导出CSV失败: {e}")

    def _export_pdf(self) -> None:
        """导出PDF报告"""
        file_path, _ = QFileDialog.getSaveFileName(
            self,
            "导出PDF报告",
            f"simulation_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf",
            "PDF文件 (*.pdf);;所有文件 (*)"
        )

        if file_path:
            try:
                self._save_pdf_report(file_path)
                QMessageBox.information(self, "成功", f"报告已导出到: {file_path}")
                self.export_requested.emit("pdf", file_path)
            except Exception as e:
                QMessageBox.critical(self, "错误", f"导出PDF失败: {e}")
                logging.error(f"导出PDF失败: {e}")

    def _save_csv_data(self, file_path: str) -> None:
        """保存CSV数据"""
        import csv

        with open(file_path, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.writer(csvfile)

            # 写入表头
            headers = ["ID", "年龄", "性别", "疾病状态", "通路类型", "出生年份"]
            writer.writerow(headers)

            # 写入数据
            for individual in self.population.individuals.values():
                row = [
                    individual.individual_id,
                    individual.get_current_age(2025),
                    individual.gender.name,
                    individual.current_disease_state.name,
                    individual.pathway_type.name if individual.pathway_type else "",
                    individual.birth_year
                ]
                writer.writerow(row)

    def _save_pdf_report(self, file_path: str) -> None:
        """保存PDF报告"""
        # 这里应该实现PDF报告生成
        # 目前只是一个占位符
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write("模拟结果报告\n")
            f.write("=" * 50 + "\n")
            f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"总人口数: {len(self.population.individuals) if self.population else 0}\n")
            # 添加更多报告内容...

    # 公共接口方法
    def set_population(self, population: Population) -> None:
        """设置人群数据"""
        self.population = population
        if population:
            self.statistics = population.statistics
        self._refresh_data()

    def set_simulation_results(self, results: Dict[str, Any]) -> None:
        """设置模拟结果"""
        self.simulation_results = results
        self._refresh_data()

    def add_log_message(self, message: str) -> None:
        """添加日志消息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        formatted_message = f"[{timestamp}] {message}"
        self.log_text.append(formatted_message)

    def clear_data(self) -> None:
        """清空数据"""
        self.population = None
        self.statistics = None
        self.simulation_results = {}

        # 清空显示
        self.data_table.setRowCount(0)
        self.log_text.clear()

        if MATPLOTLIB_AVAILABLE:
            self.figure.clear()
            self.canvas.draw()

        # 重置标签
        labels = [
            self.total_population_label, self.male_count_label, self.female_count_label,
            self.avg_age_label, self.age_range_label, self.normal_count_label,
            self.adenoma_count_label, self.cancer_count_label
        ]

        for label in labels:
            label.setText("--")

        self.status_label.setText("数据已清空")
        self.last_update_label.setText("最后更新: --")
