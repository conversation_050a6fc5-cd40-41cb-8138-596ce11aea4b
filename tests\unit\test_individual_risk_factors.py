"""
Individual类风险因素功能测试

测试Individual类与风险因素管理系统的集成。
"""

import pytest
from datetime import datetime

from src.core import Individual, Gender, DiseaseState
from src.modules.disease.risk_factors import RiskFactorType, RiskFactor


class TestIndividualRiskFactors:
    """测试Individual类的风险因素功能"""
    
    def test_risk_factor_profile_lazy_initialization(self):
        """测试风险因素档案的延迟初始化"""
        individual = Individual(1980, Gender.MALE, "test-123")
        
        # 初始时应该没有风险因素档案
        assert individual._risk_factor_profile is None
        
        # 访问属性时应该自动初始化
        profile = individual.risk_factor_profile
        assert profile is not None
        assert profile.individual_id == "test-123"
        assert len(profile.risk_factors) == 0
    
    def test_set_boolean_risk_factor(self):
        """测试设置布尔型风险因素"""
        individual = Individual(1980, Gender.MALE, "test-123")
        
        success = individual.set_risk_factor(
            RiskFactorType.FAMILY_HISTORY,
            True,
            weight=2.5,
            source="medical_history"
        )
        
        assert success is True
        assert individual.has_risk_factor(RiskFactorType.FAMILY_HISTORY)
        
        risk_factor = individual.get_risk_factor(RiskFactorType.FAMILY_HISTORY)
        assert risk_factor is not None
        assert risk_factor.value is True
        assert risk_factor.weight == 2.5
        assert risk_factor.source == "medical_history"
    
    def test_set_continuous_risk_factor(self):
        """测试设置连续型风险因素"""
        individual = Individual(1980, Gender.FEMALE, "test-456")
        
        success = individual.set_risk_factor(
            RiskFactorType.BMI,
            25.5,
            weight=1.2,
            source="measurement"
        )
        
        assert success is True
        assert individual.has_risk_factor(RiskFactorType.BMI)
        
        risk_factor = individual.get_risk_factor(RiskFactorType.BMI)
        assert risk_factor is not None
        assert risk_factor.value == 25.5
        assert risk_factor.weight == 1.2
        assert risk_factor.source == "measurement"
    
    def test_set_multiple_risk_factors(self):
        """测试设置多个风险因素"""
        individual = Individual(1975, Gender.MALE, "test-789")
        
        # 设置多个不同类型的风险因素
        individual.set_risk_factor(RiskFactorType.FAMILY_HISTORY, True, 2.5)
        individual.set_risk_factor(RiskFactorType.BMI, 28.0, 1.3)
        individual.set_risk_factor(RiskFactorType.DIABETES, False, 1.8)
        individual.set_risk_factor(RiskFactorType.SEDENTARY_LIFESTYLE, 10.5, 1.1)
        
        # 验证所有风险因素都已设置
        assert individual.has_risk_factor(RiskFactorType.FAMILY_HISTORY)
        assert individual.has_risk_factor(RiskFactorType.BMI)
        assert individual.has_risk_factor(RiskFactorType.DIABETES)
        assert individual.has_risk_factor(RiskFactorType.SEDENTARY_LIFESTYLE)
        
        # 验证风险因素档案中有4个因素
        assert len(individual.risk_factor_profile) == 4
    
    def test_update_risk_factor(self):
        """测试更新风险因素"""
        individual = Individual(1985, Gender.FEMALE, "test-update")
        
        # 设置初始值
        individual.set_risk_factor(RiskFactorType.BMI, 24.0)
        
        # 更新值
        success = individual.update_risk_factor(
            RiskFactorType.BMI,
            26.5,
            source="follow_up"
        )
        
        assert success is True
        
        risk_factor = individual.get_risk_factor(RiskFactorType.BMI)
        assert risk_factor.value == 26.5
        assert risk_factor.source == "follow_up"
    
    def test_update_nonexistent_risk_factor(self):
        """测试更新不存在的风险因素"""
        individual = Individual(1985, Gender.FEMALE, "test-update")
        
        success = individual.update_risk_factor(
            RiskFactorType.BMI,
            26.5
        )
        
        assert success is False
        assert not individual.has_risk_factor(RiskFactorType.BMI)
    
    def test_remove_risk_factor(self):
        """测试移除风险因素"""
        individual = Individual(1990, Gender.MALE, "test-remove")
        
        # 设置风险因素
        individual.set_risk_factor(RiskFactorType.SMOKING, True)
        assert individual.has_risk_factor(RiskFactorType.SMOKING)
        
        # 移除风险因素
        success = individual.remove_risk_factor(RiskFactorType.SMOKING)
        
        assert success is True
        assert not individual.has_risk_factor(RiskFactorType.SMOKING)
        assert individual.get_risk_factor(RiskFactorType.SMOKING) is None
    
    def test_remove_nonexistent_risk_factor(self):
        """测试移除不存在的风险因素"""
        individual = Individual(1990, Gender.MALE, "test-remove")
        
        success = individual.remove_risk_factor(RiskFactorType.SMOKING)
        
        assert success is False
    
    def test_get_risk_factor_summary(self):
        """测试获取风险因素摘要"""
        individual = Individual(1980, Gender.FEMALE, "test-summary")
        
        # 添加不同类型的风险因素
        individual.set_risk_factor(RiskFactorType.FAMILY_HISTORY, True)  # 遗传
        individual.set_risk_factor(RiskFactorType.BMI, 27.0)            # 生活方式
        individual.set_risk_factor(RiskFactorType.IBD, True)            # 疾病相关
        
        summary = individual.get_risk_factor_summary()
        
        assert summary["individual_id"] == "test-summary"
        assert summary["total_factors"] == 3
        assert summary["boolean_factors"] == 2
        assert summary["continuous_factors"] == 1
        assert summary["factors_by_category"]["genetic"] == 1
        assert summary["factors_by_category"]["lifestyle"] == 1
        assert summary["factors_by_category"]["disease_related"] == 1
    
    def test_set_invalid_risk_factor(self):
        """测试设置无效风险因素"""
        individual = Individual(1980, Gender.MALE, "test-invalid")
        
        # 尝试设置超出范围的BMI值
        success = individual.set_risk_factor(
            RiskFactorType.BMI,
            70.0  # 超出有效范围
        )
        
        assert success is False
        assert not individual.has_risk_factor(RiskFactorType.BMI)
    
    def test_to_dict_with_risk_factors(self):
        """测试包含风险因素的字典转换"""
        individual = Individual(1985, Gender.FEMALE, "test-dict")
        
        # 添加风险因素
        individual.set_risk_factor(RiskFactorType.FAMILY_HISTORY, True, 2.5)
        individual.set_risk_factor(RiskFactorType.BMI, 24.5, 1.2)
        
        data = individual.to_dict()
        
        # 验证基本信息
        assert data["individual_id"] == "test-dict"
        assert data["birth_year"] == 1985
        assert data["gender"] == "female"
        
        # 验证风险因素信息
        assert "risk_factors" in data
        risk_factors_data = data["risk_factors"]
        assert risk_factors_data["individual_id"] == "test-dict"
        assert "risk_factors" in risk_factors_data
        assert "family_history" in risk_factors_data["risk_factors"]
        assert "body_mass_index" in risk_factors_data["risk_factors"]
    
    def test_to_dict_without_risk_factors(self):
        """测试没有风险因素时的字典转换"""
        individual = Individual(1985, Gender.FEMALE, "test-dict-empty")
        
        data = individual.to_dict()
        
        # 验证基本信息
        assert data["individual_id"] == "test-dict-empty"
        assert data["birth_year"] == 1985
        assert data["gender"] == "female"
        
        # 应该没有风险因素信息
        assert "risk_factors" not in data
    
    def test_repr_with_risk_factors(self):
        """测试包含风险因素的字符串表示"""
        individual = Individual(1980, Gender.MALE, "test-repr-123")
        
        # 添加风险因素
        individual.set_risk_factor(RiskFactorType.FAMILY_HISTORY, True)
        individual.set_risk_factor(RiskFactorType.BMI, 25.0)
        
        repr_str = repr(individual)
        
        assert "Individual" in repr_str
        assert "test-rep" in repr_str  # 部分ID (前8个字符)
        assert "risk_factors=2" in repr_str
    
    def test_repr_without_risk_factors(self):
        """测试没有风险因素时的字符串表示"""
        individual = Individual(1980, Gender.MALE, "test-repr-456")
        
        repr_str = repr(individual)
        
        assert "Individual" in repr_str
        assert "test-rep" in repr_str  # 部分ID (前8个字符)
        assert "risk_factors=0" in repr_str
    
    def test_risk_factor_history_tracking(self):
        """测试风险因素历史跟踪"""
        individual = Individual(1980, Gender.FEMALE, "test-history")
        
        # 设置初始BMI
        individual.set_risk_factor(RiskFactorType.BMI, 24.0, source="initial")
        initial_factor = individual.get_risk_factor(RiskFactorType.BMI)
        initial_time = initial_factor.last_updated
        
        # 等待一小段时间确保时间戳不同
        import time
        time.sleep(0.01)
        
        # 更新BMI
        individual.update_risk_factor(RiskFactorType.BMI, 26.0, source="follow_up")
        updated_factor = individual.get_risk_factor(RiskFactorType.BMI)
        
        # 验证历史跟踪
        assert updated_factor.value == 26.0
        assert updated_factor.source == "follow_up"
        assert updated_factor.last_updated > initial_time
    
    def test_boolean_and_continuous_factor_types(self):
        """测试布尔型和连续型风险因素的正确处理"""
        individual = Individual(1975, Gender.MALE, "test-types")
        
        # 设置布尔型风险因素
        individual.set_risk_factor(RiskFactorType.FAMILY_HISTORY, True)
        individual.set_risk_factor(RiskFactorType.IBD, False)
        individual.set_risk_factor(RiskFactorType.DIABETES, True)
        individual.set_risk_factor(RiskFactorType.SMOKING, False)
        
        # 设置连续型风险因素
        individual.set_risk_factor(RiskFactorType.BMI, 25.5)
        individual.set_risk_factor(RiskFactorType.SEDENTARY_LIFESTYLE, 8.0)
        individual.set_risk_factor(RiskFactorType.ALCOHOL_CONSUMPTION, 5.0)
        individual.set_risk_factor(RiskFactorType.DIET_QUALITY, 75.0)
        
        profile = individual.risk_factor_profile
        
        # 验证布尔型因素
        boolean_factors = profile.get_boolean_factors()
        assert len(boolean_factors) == 4
        assert boolean_factors[RiskFactorType.FAMILY_HISTORY] is True
        assert boolean_factors[RiskFactorType.IBD] is False
        assert boolean_factors[RiskFactorType.DIABETES] is True
        assert boolean_factors[RiskFactorType.SMOKING] is False
        
        # 验证连续型因素
        continuous_factors = profile.get_continuous_factors()
        assert len(continuous_factors) == 4
        assert continuous_factors[RiskFactorType.BMI] == 25.5
        assert continuous_factors[RiskFactorType.SEDENTARY_LIFESTYLE] == 8.0
        assert continuous_factors[RiskFactorType.ALCOHOL_CONSUMPTION] == 5.0
        assert continuous_factors[RiskFactorType.DIET_QUALITY] == 75.0
