#!/usr/bin/env python3
"""
文件导入功能独立演示

演示文件导入的核心逻辑，不依赖PyQt6
"""

import pandas as pd
import numpy as np
from pathlib import Path
from typing import Dict, <PERSON><PERSON>

def validate_file_columns(df: pd.DataFrame) -> Dict[str, str]:
    """验证文件列并返回列名映射"""
    columns = df.columns.str.lower().str.strip()

    # 查找年龄列
    age_column = None
    for col in ['age', '年龄', 'Age', 'AGE']:
        if col.lower() in columns.values:
            # 找到匹配的原始列名
            matching_indices = columns == col.lower()
            age_column = df.columns[matching_indices].tolist()[0]
            break

    if age_column is None:
        raise ValueError("未找到年龄列，请确保文件包含 'age' 或 '年龄' 列")

    # 查找性别列
    gender_column = None
    for col in ['gender', 'sex', '性别', 'Gender', 'Sex', 'GENDER', 'SEX']:
        if col.lower() in columns.values:
            # 找到匹配的原始列名
            matching_indices = columns == col.lower()
            gender_column = df.columns[matching_indices].tolist()[0]
            break

    if gender_column is None:
        raise ValueError("未找到性别列，请确保文件包含 'gender'、'sex' 或 '性别' 列")

    # 查找人数列（可选）
    count_column = None
    for col in ['number', 'count', 'num', '人数', '数量', 'Number', 'Count', 'NUM', 'COUNT']:
        if col.lower() in columns.values:
            # 找到匹配的原始列名
            matching_indices = columns == col.lower()
            count_column = df.columns[matching_indices].tolist()[0]
            break

    result = {
        'age': age_column,
        'gender': gender_column
    }

    if count_column is not None:
        result['count'] = count_column

    return result

def standardize_gender_values(gender_series: pd.Series) -> pd.Series:
    """标准化性别值"""
    gender_mapping = {
        'M': 'Male', 'MALE': 'Male', '男': 'Male', '男性': 'Male',
        'F': 'Female', 'FEMALE': 'Female', '女': 'Female', '女性': 'Female'
    }
    
    genders = gender_series.map(gender_mapping)
    invalid_genders = genders.isna().sum()
    if invalid_genders > 0:
        raise ValueError(f"发现 {invalid_genders} 个无效的性别值，请使用 M/F、男/女 或 Male/Female")
    
    return genders

def process_individual_data(df: pd.DataFrame, column_mapping: Dict[str, str]) -> Tuple[Dict[int, int], Dict[str, int]]:
    """处理个体数据（每行代表一个人）"""
    
    # 清理数据
    df_clean = df[[column_mapping['age'], column_mapping['gender']]].copy()
    df_clean = df_clean.dropna()
    
    # 处理年龄数据
    age_col = df_clean[column_mapping['age']]
    try:
        ages = pd.to_numeric(age_col, errors='coerce').dropna().astype(int)
    except:
        raise ValueError("年龄列包含无效数据，请确保年龄为数字")
    
    # 验证年龄范围
    if ages.min() < 0 or ages.max() > 120:
        raise ValueError(f"年龄数据超出合理范围 (0-120)，实际范围: {ages.min()}-{ages.max()}")
    
    # 处理性别数据
    gender_col = df_clean[column_mapping['gender']].str.strip().str.upper()
    genders = standardize_gender_values(gender_col)
    
    # 生成分布统计
    age_distribution = ages.value_counts().to_dict()
    gender_distribution = genders.value_counts().to_dict()
    
    return age_distribution, gender_distribution

def process_aggregated_data(df: pd.DataFrame, column_mapping: Dict[str, str]) -> Tuple[Dict[int, int], Dict[str, int]]:
    """处理聚合数据（年龄、性别、人数）"""
    
    # 清理数据
    required_cols = [column_mapping['age'], column_mapping['gender'], column_mapping['count']]
    df_clean = df[required_cols].copy()
    df_clean = df_clean.dropna()
    
    # 处理年龄数据
    try:
        df_clean['age_clean'] = pd.to_numeric(df_clean[column_mapping['age']], errors='coerce').astype(int)
    except:
        raise ValueError("年龄列包含无效数据，请确保年龄为数字")
    
    # 验证年龄范围
    age_min, age_max = df_clean['age_clean'].min(), df_clean['age_clean'].max()
    if age_min < 0 or age_max > 120:
        raise ValueError(f"年龄数据超出合理范围 (0-120)，实际范围: {age_min}-{age_max}")
    
    # 处理人数数据
    try:
        df_clean['count_clean'] = pd.to_numeric(df_clean[column_mapping['count']], errors='coerce').astype(int)
    except:
        raise ValueError("人数列包含无效数据，请确保人数为正整数")
    
    # 验证人数为正数
    if (df_clean['count_clean'] <= 0).any():
        raise ValueError("人数必须为正整数")
    
    # 处理性别数据
    gender_col = df_clean[column_mapping['gender']].str.strip().str.upper()
    df_clean['gender_clean'] = standardize_gender_values(gender_col)
    
    # 生成年龄分布（按年龄汇总人数）
    age_distribution = df_clean.groupby('age_clean')['count_clean'].sum().to_dict()
    
    # 生成性别分布（按性别汇总人数）
    gender_distribution = df_clean.groupby('gender_clean')['count_clean'].sum().to_dict()
    
    return age_distribution, gender_distribution

def process_population_data(df: pd.DataFrame, column_mapping: Dict[str, str]) -> Tuple[Dict[int, int], Dict[str, int]]:
    """处理人群数据"""
    
    # 确定是否有人数列
    has_count_column = 'count' in column_mapping
    
    if has_count_column:
        # 处理聚合数据格式（年龄、性别、人数）
        return process_aggregated_data(df, column_mapping)
    else:
        # 处理个体数据格式（每行一个人）
        return process_individual_data(df, column_mapping)

def demo_file_processing():
    """演示文件处理功能"""
    print("=== 文件处理功能演示 ===\n")
    
    data_dir = Path('data')
    
    # 测试文件列表
    test_files = [
        ('sample_population.csv', '个体格式'),
        ('sample_population_aggregated.csv', '聚合格式'),
        ('sample_population_chinese.csv', '中文聚合格式')
    ]
    
    for filename, description in test_files:
        file_path = data_dir / filename
        if not file_path.exists():
            print(f"❌ 文件不存在: {filename}")
            continue
        
        print(f"📄 处理文件: {filename} ({description})")
        
        try:
            # 读取文件
            df = pd.read_csv(file_path)
            print(f"   原始数据: {len(df)} 行, 列: {list(df.columns)}")
            
            # 验证列
            column_mapping = validate_file_columns(df)
            print(f"   列映射: {column_mapping}")
            
            # 处理数据
            age_dist, gender_dist = process_population_data(df, column_mapping)
            
            # 显示结果
            total_population = sum(age_dist.values())
            print(f"   总人群规模: {total_population:,}")
            print(f"   年龄范围: {min(age_dist.keys())}-{max(age_dist.keys())}岁")
            print(f"   性别分布: {gender_dist}")
            
            # 计算统计信息
            ages = []
            for age, count in age_dist.items():
                ages.extend([age] * count)
            
            avg_age = np.mean(ages)
            std_age = np.std(ages)
            male_count = gender_dist.get('Male', 0)
            total_gender = sum(gender_dist.values())
            male_ratio = male_count / total_gender if total_gender > 0 else 0
            
            print(f"   平均年龄: {avg_age:.1f}岁")
            print(f"   年龄标准差: {std_age:.1f}岁")
            print(f"   男性比例: {male_ratio:.1%}")
            
            # 显示年龄分布前5个
            sorted_ages = sorted(age_dist.items(), key=lambda x: x[1], reverse=True)[:5]
            print(f"   主要年龄组:")
            for age, count in sorted_ages:
                print(f"     {age}岁: {count:,}人")
            
            print("   ✅ 处理成功\n")
            
        except Exception as e:
            print(f"   ❌ 处理失败: {e}\n")

def demo_error_cases():
    """演示错误情况处理"""
    print("=== 错误情况演示 ===\n")
    
    # 测试缺少必需列
    print("1. 缺少必需列:")
    try:
        df = pd.DataFrame({'other_col': [1, 2, 3]})
        validate_file_columns(df)
    except ValueError as e:
        print(f"   ✅ 正确捕获: {e}")
    
    # 测试无效年龄
    print("\n2. 无效年龄数据:")
    try:
        df = pd.DataFrame({'age': ['abc', 'def'], 'gender': ['M', 'F']})
        column_mapping = {'age': 'age', 'gender': 'gender'}
        process_population_data(df, column_mapping)
    except ValueError as e:
        print(f"   ✅ 正确捕获: {e}")
    
    # 测试无效性别
    print("\n3. 无效性别数据:")
    try:
        df = pd.DataFrame({'age': [45, 50], 'gender': ['X', 'Y']})
        column_mapping = {'age': 'age', 'gender': 'gender'}
        process_population_data(df, column_mapping)
    except ValueError as e:
        print(f"   ✅ 正确捕获: {e}")
    
    # 测试负数人数
    print("\n4. 无效人数数据:")
    try:
        df = pd.DataFrame({'age': [45, 50], 'gender': ['M', 'F'], 'number': [-10, 0]})
        column_mapping = {'age': 'age', 'gender': 'gender', 'count': 'number'}
        process_population_data(df, column_mapping)
    except ValueError as e:
        print(f"   ✅ 正确捕获: {e}")

def demo_format_comparison():
    """演示两种格式的对比"""
    print("\n=== 格式对比演示 ===\n")
    
    # 创建相同人群的两种格式数据
    print("创建相同人群的两种格式数据进行对比...")
    
    # 聚合格式
    aggregated_data = [
        {'age': 50, 'gender': 'M', 'number': 100},
        {'age': 50, 'gender': 'F', 'number': 95},
        {'age': 55, 'gender': 'M', 'number': 80},
        {'age': 55, 'gender': 'F', 'number': 85},
    ]
    
    # 个体格式（展开聚合数据）
    individual_data = []
    for item in aggregated_data:
        for _ in range(item['number']):
            individual_data.append({'age': item['age'], 'gender': item['gender']})
    
    # 处理聚合格式
    print("1. 聚合格式处理:")
    agg_df = pd.DataFrame(aggregated_data)
    agg_mapping = {'age': 'age', 'gender': 'gender', 'count': 'number'}
    agg_age_dist, agg_gender_dist = process_population_data(agg_df, agg_mapping)
    print(f"   数据行数: {len(agg_df)}")
    print(f"   总人数: {sum(agg_age_dist.values())}")
    print(f"   年龄分布: {agg_age_dist}")
    print(f"   性别分布: {agg_gender_dist}")
    
    # 处理个体格式
    print("\n2. 个体格式处理:")
    ind_df = pd.DataFrame(individual_data)
    ind_mapping = {'age': 'age', 'gender': 'gender'}
    ind_age_dist, ind_gender_dist = process_population_data(ind_df, ind_mapping)
    print(f"   数据行数: {len(ind_df)}")
    print(f"   总人数: {sum(ind_age_dist.values())}")
    print(f"   年龄分布: {ind_age_dist}")
    print(f"   性别分布: {ind_gender_dist}")
    
    # 验证结果一致性
    print("\n3. 结果对比:")
    age_match = agg_age_dist == ind_age_dist
    gender_match = agg_gender_dist == ind_gender_dist
    print(f"   年龄分布一致: {'✅' if age_match else '❌'}")
    print(f"   性别分布一致: {'✅' if gender_match else '❌'}")

def main():
    """主演示函数"""
    print("结直肠癌筛查模拟器 - 文件导入功能独立演示")
    print("=" * 60)
    
    demo_file_processing()
    demo_error_cases()
    demo_format_comparison()
    
    print("\n" + "=" * 60)
    print("文件导入功能演示完成！")
    print("\n✅ 功能特点:")
    print("• 支持个体和聚合两种数据格式")
    print("• 自动检测文件格式和列名")
    print("• 支持中英文列名")
    print("• 完善的数据验证和错误处理")
    print("• 生成标准化的年龄和性别分布")
    print("\n📊 这个功能解决了什么问题:")
    print("• 真实人群分布往往不是正态分布")
    print("• 可以直接导入人口普查或调研数据")
    print("• 支持不同地区、不同时期的人群结构")
    print("• 提高模拟的真实性和准确性")

if __name__ == "__main__":
    main()
