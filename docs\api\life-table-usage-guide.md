# 生命表使用指南

## 概述

本指南介绍如何在结直肠癌筛查微观模拟模型中使用生命表功能。生命表是计算自然死亡率的核心组件，为模拟提供年龄和性别特异性的死亡概率。

## 快速开始

### 基本使用流程

```python
from src.modules.population import LifeTable, LifeTableType
from src.core import MortalityEngine, Gender

# 1. 创建生命表管理器
life_table = LifeTable()

# 2. 加载预定义生命表
china_table = life_table.load_predefined_table(LifeTableType.CHINA_2020)

# 3. 查询死亡率
mortality_rate = life_table.get_mortality_rate(65, Gender.MALE)
print(f"65岁男性年度死亡率: {mortality_rate:.5f}")

# 4. 创建死亡率引擎
mortality_engine = MortalityEngine(life_table, random_seed=42)
```

## 生命表管理

### 加载生命表数据

#### 方法1: 加载预定义生命表

```python
# 加载中国2020年生命表
china_table = life_table.load_predefined_table(LifeTableType.CHINA_2020)

# 加载WHO全球生命表
who_table = life_table.load_predefined_table(LifeTableType.WHO_GLOBAL)
```

#### 方法2: 加载自定义生命表

```python
# 从CSV文件加载
custom_table = life_table.load_life_table(
    "path/to/custom_table.csv",
    table_name="custom_population",
    set_as_current=True
)

# 从Excel文件加载
excel_table = life_table.load_life_table(
    "path/to/life_table.xlsx",
    table_name="excel_data"
)
```

### 管理多个生命表

```python
# 查看已加载的生命表
available_tables = life_table.get_available_tables()
print(f"可用生命表: {available_tables}")

# 切换当前使用的生命表
life_table.set_current_table("china_2020")

# 获取当前生命表名称
current_table = life_table.get_current_table_name()
print(f"当前生命表: {current_table}")
```

## 死亡率查询

### 基本查询

```python
# 查询特定年龄和性别的死亡率
rate_male_50 = life_table.get_mortality_rate(50, "male")
rate_female_50 = life_table.get_mortality_rate(50, Gender.FEMALE)

# 查询生存概率
survival_prob = life_table.get_survival_probability(50, "male")
print(f"50岁男性生存概率: {survival_prob:.5f}")
```

### 高级查询

```python
# 计算预期寿命
life_expectancy = life_table.get_life_expectancy(65, "female")
print(f"65岁女性预期寿命: {life_expectancy:.1f}年")

# 指定特定生命表查询
rate = life_table.get_mortality_rate(
    age=60, 
    gender="male", 
    table_name="who_global"
)
```

### 批量查询

```python
# 批量查询多个年龄的死亡率
ages = [50, 55, 60, 65, 70]
male_rates = [life_table.get_mortality_rate(age, "male") for age in ages]
female_rates = [life_table.get_mortality_rate(age, "female") for age in ages]

# 创建年龄-死亡率对照表
mortality_table = {
    age: {
        "male": life_table.get_mortality_rate(age, "male"),
        "female": life_table.get_mortality_rate(age, "female")
    }
    for age in range(50, 81, 5)
}
```

## 死亡率引擎使用

### 创建和配置

```python
# 创建死亡率引擎
mortality_engine = MortalityEngine(
    life_table=life_table,
    random_seed=42,
    time_precision="monthly"  # "monthly", "quarterly", "yearly"
)
```

### 个体死亡率应用

```python
from src.core import Individual, Gender

# 创建个体
individual = Individual(
    birth_year=1960,
    gender=Gender.MALE,
    individual_id="person_001"
)

# 应用死亡率（模拟2025年）
death_occurred = mortality_engine.apply_mortality_to_individual(
    individual=individual,
    current_time=2025.0,
    time_step=1.0  # 1年时间步长
)

if death_occurred:
    print(f"个体 {individual.individual_id} 在2025年死亡")
    print(f"死亡状态: {individual.current_disease_state}")
```

### 人群死亡率应用

```python
from src.core import Population

# 假设已有人群对象
# population = Population(individuals)

# 对整个人群应用死亡率
result = mortality_engine.apply_mortality_to_population(
    population=population,
    current_time=2025.0,
    time_step=1.0,
    show_progress=True
)

# 查看结果
print(f"总个体数: {result.total_individuals}")
print(f"死亡个体数: {result.deaths_applied}")
print(f"自然死亡: {result.natural_deaths}")
print(f"癌症死亡: {result.cancer_deaths}")
print(f"实际死亡率: {result.death_rate:.4f}")
```

## 数据验证和质量检查

### 验证生命表数据

```python
# 验证当前生命表
validation_result = life_table.validate_table_data()

print(f"生命表名称: {validation_result['table_name']}")
print(f"总记录数: {validation_result['total_records']}")
print(f"年龄范围: {validation_result['age_range']}")
print(f"性别覆盖: {validation_result['genders']}")

# 检查数据完整性
for gender, completeness in validation_result['data_completeness'].items():
    coverage = completeness['coverage_ratio']
    print(f"{gender}性别数据覆盖率: {coverage:.1%}")

# 检查数据问题
if validation_result['issues']:
    print("发现的问题:")
    for issue in validation_result['issues']:
        print(f"  - {issue}")
```

### 数据质量改进

```python
# 平滑死亡率数据
smoothed_table = life_table.smooth_mortality_rates(
    window_size=3,
    method="moving_average"
)

# 验证平滑后的数据
smoothed_validation = life_table.validate_table_data(smoothed_table.name)
```

## 统计分析

### 人群死亡率统计

```python
# 获取人群死亡率统计
mortality_stats = mortality_engine.get_population_mortality_statistics(
    population=population,
    current_time=2025.0
)

print(f"存活个体数: {mortality_stats['total_alive']}")
print(f"平均死亡率: {mortality_stats['average_mortality_rate']:.5f}")

# 按年龄组查看死亡率
for age_group, stats in mortality_stats['mortality_by_age_group'].items():
    if stats['count'] > 0:
        print(f"{age_group}岁组: {stats['average_mortality_rate']:.5f}")

# 按性别查看死亡率
for gender, stats in mortality_stats['mortality_by_gender'].items():
    print(f"{gender}: {stats['average_mortality_rate']:.5f}")
```

### 应用历史分析

```python
# 获取死亡率应用历史
history = mortality_engine.get_application_history()

for i, result in enumerate(history):
    print(f"第{i+1}次应用:")
    print(f"  死亡个体数: {result.deaths_applied}")
    print(f"  应用时间: {result.application_time:.2f}秒")
    print(f"  实际死亡率: {result.death_rate:.4f}")
```

## 数据导出和保存

### 导出生命表数据

```python
# 导出为CSV格式
life_table.export_table_data(
    output_path="exported_life_table.csv",
    format="csv"
)

# 导出为Excel格式
life_table.export_table_data(
    output_path="exported_life_table.xlsx",
    format="excel"
)

# 导出为JSON格式（包含元数据）
life_table.export_table_data(
    output_path="exported_life_table.json",
    format="json"
)
```

## 高级用法

### 时间精度控制

```python
# 月度精度（默认）
monthly_engine = MortalityEngine(life_table, time_precision="monthly")

# 季度精度
quarterly_engine = MortalityEngine(life_table, time_precision="quarterly")

# 年度精度
yearly_engine = MortalityEngine(life_table, time_precision="yearly")
```

### 随机种子管理

```python
# 设置随机种子确保可重现性
mortality_engine.set_random_seed(12345)

# 重置应用历史
mortality_engine.reset_history()
```

### 自定义死亡原因判定

死亡率引擎会根据个体的疾病状态自动判定死亡原因：
- 正常状态或腺瘤状态：自然死亡 (DEATH_OTHER)
- 癌症状态：根据癌症分期概率判定癌症死亡 (DEATH_CANCER) 或自然死亡

## 性能优化

### 大规模人群处理

```python
# 对于大规模人群，启用进度显示
result = mortality_engine.apply_mortality_to_population(
    population=large_population,
    current_time=2025.0,
    show_progress=True  # 显示进度条
)
```

### 内存管理

```python
# 定期清理应用历史以节省内存
if len(mortality_engine.get_application_history()) > 100:
    mortality_engine.reset_history()
```

## 常见问题和解决方案

### 问题1: 生命表文件加载失败

```python
try:
    life_table.load_life_table("path/to/table.csv")
except FileNotFoundError:
    print("文件不存在，请检查路径")
except ValidationError as e:
    print(f"数据验证失败: {e}")
```

### 问题2: 死亡率查询超出范围

```python
try:
    rate = life_table.get_mortality_rate(150, "male")  # 年龄超出范围
except ValidationError as e:
    print(f"参数错误: {e}")
```

### 问题3: 性能优化

```python
# 预加载常用年龄范围的死亡率
age_range = range(50, 81)
mortality_cache = {
    age: {
        "male": life_table.get_mortality_rate(age, "male"),
        "female": life_table.get_mortality_rate(age, "female")
    }
    for age in age_range
}
```

## 最佳实践

1. **选择合适的生命表**: 根据研究人群特征选择最匹配的生命表
2. **数据验证**: 加载生命表后始终进行数据验证
3. **随机种子**: 设置随机种子确保结果可重现
4. **性能监控**: 对大规模模拟监控内存使用和执行时间
5. **敏感性分析**: 使用多个生命表进行敏感性分析
6. **定期更新**: 定期更新生命表数据以反映最新的人口统计趋势
