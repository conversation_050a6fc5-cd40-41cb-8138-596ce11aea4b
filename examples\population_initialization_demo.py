#!/usr/bin/env python3
"""
人群初始化演示脚本

展示如何使用PopulationGenerator和PopulationConfig进行人群初始化。
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.modules.population import PopulationGenerator, PopulationConfig, PopulationReporter
from src.core import Gender, PathwayType


def demo_basic_population_generation():
    """演示基本人群生成功能"""
    print("=" * 60)
    print("基本人群生成演示")
    print("=" * 60)
    
    # 创建人群生成器
    generator = PopulationGenerator(random_seed=42)
    
    # 定义生成参数
    population = generator.generate_population(
        size=1000,
        age_distribution={
            "type": "normal",
            "mean": 60.0,
            "std": 10.0,
            "min_age": 40,
            "max_age": 80
        },
        gender_distribution={
            "male_ratio": 0.52,
            "female_ratio": 0.48
        },
        pathway_distribution={
            "adenoma_carcinoma_ratio": 0.85,
            "serrated_adenoma_ratio": 0.15
        },
        show_progress=True
    )
    
    # 获取生成摘要
    summary = generator.get_last_generation_summary()
    
    print(f"\n✅ 成功生成人群:")
    print(f"   总人数: {population.get_size():,}")
    print(f"   生成时间: {summary.generation_time:.2f}秒")
    print(f"   平均年龄: {summary.age_stats['mean']:.1f}岁")
    
    # 显示统计信息
    stats = population.statistics.get_summary()
    print(f"\n📊 人群统计:")
    print(f"   男性: {stats['gender_distribution']['male']} ({stats['gender_distribution']['male']/population.get_size()*100:.1f}%)")
    print(f"   女性: {stats['gender_distribution']['female']} ({stats['gender_distribution']['female']/population.get_size()*100:.1f}%)")
    
    return population, summary


def demo_config_file_usage():
    """演示配置文件使用"""
    print("\n" + "=" * 60)
    print("配置文件使用演示")
    print("=" * 60)
    
    # 创建配置管理器
    config_manager = PopulationConfig()
    
    # 尝试加载预定义配置
    config_files = [
        "data/population_configs/china_adult_screening.yaml",
        "data/population_configs/small_test_population.yaml"
    ]
    
    populations = []
    
    for config_file in config_files:
        config_path = Path(config_file)
        if config_path.exists():
            print(f"\n📁 加载配置文件: {config_file}")
            
            # 加载配置
            config = config_manager.load_config(config_path)
            print(f"   配置名称: {config.name}")
            print(f"   人群规模: {config.size:,}")
            print(f"   年龄分布: {config.age_distribution.type}")
            
            # 生成小规模测试人群
            generator = PopulationGenerator(random_seed=config.random_seed)
            test_params = config.to_generation_params()
            test_params["size"] = min(500, config.size)  # 限制测试规模
            
            population = generator.generate_population(**test_params, show_progress=False)
            populations.append((population, config.name))
            
            print(f"   ✅ 生成测试人群: {population.get_size()} 个体")
        else:
            print(f"   ⚠️  配置文件不存在: {config_file}")
    
    return populations


def demo_custom_config_creation():
    """演示自定义配置创建"""
    print("\n" + "=" * 60)
    print("自定义配置创建演示")
    print("=" * 60)
    
    config_manager = PopulationConfig()
    
    # 创建自定义配置
    custom_config = config_manager.create_default_config(
        name="演示自定义人群",
        description="用于演示的自定义人群配置"
    )
    
    # 修改配置参数
    custom_config.size = 800
    custom_config.age_distribution.type = "uniform"
    custom_config.age_distribution.min_age = 30
    custom_config.age_distribution.max_age = 70
    custom_config.gender_distribution.male_ratio = 0.6
    custom_config.gender_distribution.female_ratio = 0.4
    
    print(f"📝 创建自定义配置:")
    print(f"   名称: {custom_config.name}")
    print(f"   规模: {custom_config.size}")
    print(f"   年龄范围: {custom_config.age_distribution.min_age}-{custom_config.age_distribution.max_age}岁")
    print(f"   男性比例: {custom_config.gender_distribution.male_ratio:.1%}")
    
    # 生成人群
    generator = PopulationGenerator(random_seed=42)
    population = generator.generate_population(
        **custom_config.to_generation_params(),
        show_progress=False
    )
    
    print(f"   ✅ 生成人群: {population.get_size()} 个体")
    
    return population, custom_config


def demo_population_reporting():
    """演示人群报告生成"""
    print("\n" + "=" * 60)
    print("人群报告生成演示")
    print("=" * 60)
    
    # 生成一个示例人群
    generator = PopulationGenerator(random_seed=42)
    population = generator.generate_population(
        size=2000,
        age_distribution={
            "type": "normal",
            "mean": 55.0,
            "std": 12.0,
            "min_age": 25,
            "max_age": 85
        },
        gender_distribution={
            "male_ratio": 0.55,
            "female_ratio": 0.45
        },
        show_progress=False
    )
    
    summary = generator.get_last_generation_summary()
    
    # 创建报告器
    reporter = PopulationReporter()
    
    print("📊 生成人群报告...")
    
    # 生成综合报告
    try:
        report_info = reporter.generate_comprehensive_report(
            population, 
            summary, 
            include_plots=True
        )
        
        print(f"   ✅ 报告已生成:")
        print(f"      文本报告: {report_info['metadata']['files_generated']['summary_report']}")
        
        if 'age_distribution' in report_info['plots']:
            print(f"      年龄分布图: {report_info['plots']['age_distribution']}")
        
        if 'gender_distribution' in report_info['plots']:
            print(f"      性别分布图: {report_info['plots']['gender_distribution']}")
        
        # 显示报告摘要
        print(f"\n📋 报告摘要:")
        print(f"   人群规模: {report_info['metadata']['population_size']:,}")
        print(f"   生成时间: {report_info['metadata']['generation_time']:.2f}秒")
        print(f"   输出目录: {report_info['metadata']['output_directory']}")
        
    except ImportError:
        print("   ⚠️  matplotlib未安装，跳过图表生成")
        # 只生成文本报告
        text_report = reporter.generate_summary_report(population, summary)
        print("   ✅ 文本报告已生成")
    
    return population


def demo_batch_generation():
    """演示批量人群生成"""
    print("\n" + "=" * 60)
    print("批量人群生成演示")
    print("=" * 60)
    
    # 定义多个配置
    batch_configs = [
        {
            "size": 300,
            "age_distribution": {"type": "uniform", "min_age": 20, "max_age": 40},
            "gender_distribution": {"male_ratio": 0.5, "female_ratio": 0.5},
        },
        {
            "size": 400,
            "age_distribution": {"type": "normal", "mean": 65, "std": 8, "min_age": 50, "max_age": 80},
            "gender_distribution": {"male_ratio": 0.45, "female_ratio": 0.55},
        },
        {
            "size": 250,
            "age_distribution": {"type": "uniform", "min_age": 30, "max_age": 60},
            "gender_distribution": {"male_ratio": 0.6, "female_ratio": 0.4},
        }
    ]
    
    print(f"🔄 批量生成 {len(batch_configs)} 个人群...")
    
    generator = PopulationGenerator(random_seed=42)
    populations = generator.generate_batch_populations(batch_configs, show_progress=True)
    
    print(f"\n✅ 批量生成完成:")
    for i, pop in enumerate(populations):
        ages = [ind.get_current_age() for ind in pop]
        print(f"   人群 {i+1}: {pop.get_size()} 个体, 平均年龄 {sum(ages)/len(ages):.1f}岁")
    
    return populations


def main():
    """主演示函数"""
    print("🎯 人群初始化功能演示")
    print("=" * 60)
    
    try:
        # 1. 基本人群生成
        basic_pop, basic_summary = demo_basic_population_generation()
        
        # 2. 配置文件使用
        config_populations = demo_config_file_usage()
        
        # 3. 自定义配置创建
        custom_pop, custom_config = demo_custom_config_creation()
        
        # 4. 人群报告生成
        report_pop = demo_population_reporting()
        
        # 5. 批量生成
        batch_populations = demo_batch_generation()
        
        print("\n" + "=" * 60)
        print("🎉 所有演示完成!")
        print("=" * 60)
        
        print(f"\n📈 演示总结:")
        print(f"   基本生成: {basic_pop.get_size()} 个体")
        print(f"   配置文件生成: {len(config_populations)} 个人群")
        print(f"   自定义配置生成: {custom_pop.get_size()} 个体")
        print(f"   报告生成: {report_pop.get_size()} 个体")
        print(f"   批量生成: {len(batch_populations)} 个人群")
        
        total_individuals = (
            basic_pop.get_size() + 
            sum(pop.get_size() for pop, _ in config_populations) +
            custom_pop.get_size() + 
            report_pop.get_size() + 
            sum(pop.get_size() for pop in batch_populations)
        )
        
        print(f"   总计生成: {total_individuals:,} 个体")
        
    except Exception as e:
        print(f"\n❌ 演示过程中发生错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
